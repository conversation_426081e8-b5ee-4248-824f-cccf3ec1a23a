import React, { useState } from 'react';
import { ChevronDown, ChevronUp, ChevronRight, Info, X } from 'lucide-react';

// Dummy data - shared across all interactions
const dummyTaskData = {
  id: 'task-001',
  name: 'User Authentication Flow Validation',
  context: 'prod - AWS us-east-1',
  systemPrompt: 'Validate all authentication endpoints including login, logout, password reset, and session management. Ensure compliance with security standards and performance requirements.',
  testScenarios: [
    {
      id: 'test-001',
      description: 'Calculate compound interest correctly',
      status: 'pass',
      output: '$1,628.89',
      input: { principal: 1000, rate: 0.05, time: 10, compound: 'monthly' },
      graderScores: [
        { criterion: 'Accuracy', score: 100, status: 'pass' },
        { criterion: 'Performance', score: 95, status: 'pass' },
        { criterion: 'Edge Cases', score: 90, status: 'pass' }
      ]
    },
    {
      id: 'test-002',
      description: 'Validate email format',
      status: 'pass',
      output: '<NAME_EMAIL>',
      input: { email: '<EMAIL>', strictMode: true },
      graderScores: [
        { criterion: 'Format Check', score: 100, status: 'pass' },
        { criterion: 'Domain Valid', score: 100, status: 'pass' },
        { criterion: 'Special Chars', score: 85, status: 'pass' }
      ]
    },
    {
      id: 'test-003',
      description: 'Process batch transactions in <2s',
      status: 'fail',
      output: 'Timeout at 5.3s',
      input: { transactions: Array(1000).fill({ amount: 50 }), timeout: 2000 },
      graderScores: [
        { criterion: 'Speed', score: 23, status: 'fail' },
        { criterion: 'Accuracy', score: 100, status: 'pass' },
        { criterion: 'Memory', score: 15, status: 'fail' },
        { criterion: 'Scalability', score: 8, status: 'fail' }
      ]
    },
    {
      id: 'test-004',
      description: 'Generate PDF report with charts',
      status: 'pass',
      output: 'PDF created, 2.3MB',
      input: { template: 'quarterly-report', charts: ['revenue', 'users', 'performance'] },
      graderScores: [
        { criterion: 'File Size', score: 85, status: 'pass' },
        { criterion: 'Render Time', score: 90, status: 'pass' },
        { criterion: 'Chart Quality', score: 95, status: 'pass' }
      ]
    },
    {
      id: 'test-005',
      description: 'Handle 1000 concurrent connections',
      status: 'fail',
      output: 'Failed at 743 connections',
      input: { maxConnections: 1000, timeout: 30000, protocol: 'websocket' },
      graderScores: [
        { criterion: 'Concurrency', score: 74, status: 'warn' },
        { criterion: 'Stability', score: 45, status: 'fail' },
        { criterion: 'Resource Usage', score: 30, status: 'fail' }
      ]
    }
  ],
  aggregateScores: [
    { category: 'Authentication', passRate: 95 },
    { category: 'Data Validation', passRate: 88 },
    { category: 'Performance', passRate: 45 },
    { category: 'Security', passRate: 92 },
    { category: 'API Endpoints', passRate: 76 },
    { category: 'Error Handling', passRate: 23 },
    { category: 'Integration', passRate: 85 },
    { category: 'UI Components', passRate: 91 },
    { category: 'Database', passRate: 67 },
    { category: 'Caching', passRate: 78 },
    { category: 'Logging', passRate: 94 },
    { category: 'Monitoring', passRate: 89 }
  ]
};

// Component for individual QA bubble
const QABubble = ({ category, passRate, onClick }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  
  const getColor = (rate) => {
    if (rate > 80) return 'bg-green-500';
    if (rate > 50) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="relative">
      <div
        className={`w-6 h-6 rounded-full ${getColor(passRate)} cursor-pointer hover:ring-2 hover:ring-gray-400 transition-all`}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        onClick={() => onClick(category)}
      />
      {showTooltip && (
        <div className="absolute z-50 -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
          {category}: {passRate}%
        </div>
      )}
    </div>
  );
};

// Component for test scenario row
const TestScenarioRow = ({ scenario, onClick, onHover, onHoverEnd }) => {
  const [showOutput, setShowOutput] = useState(false);

  return (
    <div className="relative">
      <div
        className="flex items-center py-1 cursor-pointer hover:bg-gray-100 px-2 rounded"
        onClick={() => onClick(scenario)}
        onMouseEnter={() => {
          setShowOutput(true);
          onHover(scenario);
        }}
        onMouseLeave={() => {
          setShowOutput(false);
          onHoverEnd();
        }}
      >
        <span className="mr-2">{scenario.status === 'pass' ? '✅' : '❌'}</span>
        <span className="text-sm">{scenario.description}</span>
      </div>
      {showOutput && (
        <div className="absolute z-40 left-8 -top-6 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
          Output: {scenario.output}
        </div>
      )}
    </div>
  );
};

// Main Application Component
export default function TaskVisualizationSystem() {
  const [expandedTests, setExpandedTests] = useState(false);
  const [sidebar1Content, setSidebar1Content] = useState(null);
  const [sidebar2Content, setSidebar2Content] = useState(null);

  // Handlers for different click actions
  const handleTaskClick = () => {
    setSidebar1Content({
      type: 'task',
      title: 'Task Configuration',
      content: {
        name: dummyTaskData.name,
        id: dummyTaskData.id,
        systemPrompt: dummyTaskData.systemPrompt,
        created: '2024-01-15 09:30:00',
        lastRun: '2024-01-15 14:45:00',
        runCount: 47,
        avgDuration: '3m 24s'
      }
    });
    setSidebar2Content(null);
  };

  const handleContextClick = () => {
    setSidebar1Content({
      type: 'context',
      title: 'Environment Configuration',
      content: {
        environment: 'Production',
        region: 'AWS us-east-1',
        apiEndpoint: 'https://api.prod.example.com',
        database: 'PostgreSQL 14.2',
        cache: 'Redis 6.2',
        variables: {
          MAX_RETRIES: '3',
          TIMEOUT: '30000',
          BATCH_SIZE: '100'
        }
      }
    });
    setSidebar2Content(null);
  };

  const handleQAGridClick = (category) => {
    const score = dummyTaskData.aggregateScores.find(s => s.category === category);
    setSidebar1Content({
      type: 'requirements',
      title: `${category} Requirements`,
      content: {
        category,
        passRate: score.passRate,
        totalTests: 24,
        passed: Math.round(24 * score.passRate / 100),
        requirements: [
          `REQ-${category}-001: Basic functionality`,
          `REQ-${category}-002: Edge case handling`,
          `REQ-${category}-003: Performance benchmarks`,
          `REQ-${category}-004: Security compliance`
        ]
      }
    });
    setSidebar2Content(null);
  };

  const handleTestClick = (scenario) => {
    setSidebar1Content({
      type: 'test',
      title: scenario.description,
      content: {
        id: scenario.id,
        status: scenario.status,
        input: scenario.input,
        duration: '234ms',
        memory: '45MB'
      }
    });
    setSidebar2Content({
      type: 'grader',
      scenario: scenario
    });
  };

  const handleFullQAClick = () => {
    setSidebar1Content({
      type: 'qa-dashboard',
      title: 'Complete QA Dashboard',
      content: {
        totalTests: dummyTaskData.testScenarios.length,
        passed: dummyTaskData.testScenarios.filter(t => t.status === 'pass').length,
        failed: dummyTaskData.testScenarios.filter(t => t.status === 'fail').length,
        avgPassRate: Math.round(dummyTaskData.aggregateScores.reduce((acc, s) => acc + s.passRate, 0) / dummyTaskData.aggregateScores.length),
        categories: dummyTaskData.aggregateScores
      }
    });
    setSidebar2Content(null);
  };

  const closeSidebar = (tier) => {
    if (tier === 2) {
      setSidebar2Content(null);
    } else {
      setSidebar1Content(null);
      setSidebar2Content(null);
    }
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Main View - Left Third */}
      <div className="w-1/3 p-4 overflow-auto border-r border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Task Cards</h2>
        
        {/* Task Card */}
        <div className="bg-white rounded-lg shadow-md p-4 max-w-md">
          {/* Task Name */}
          <div 
            className="font-semibold text-gray-800 cursor-pointer hover:text-blue-600 flex items-center"
            onClick={handleTaskClick}
          >
            <span className="truncate">Task: {dummyTaskData.name.substring(0, 20)}...</span>
            <Info className="w-4 h-4 ml-1 text-gray-400" />
          </div>
          
          {/* Context */}
          <div 
            className="text-sm text-gray-600 cursor-pointer hover:text-blue-600 flex items-center mt-1"
            onClick={handleContextClick}
          >
            <span>Context: {dummyTaskData.context}</span>
            <Info className="w-4 h-4 ml-1 text-gray-400" />
          </div>
          
          {/* Test Scenarios Toggle */}
          <div className="mt-3">
            <button
              className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
              onClick={() => setExpandedTests(!expandedTests)}
            >
              {expandedTests ? <ChevronUp className="w-4 h-4 mr-1" /> : <ChevronDown className="w-4 h-4 mr-1" />}
              {dummyTaskData.testScenarios.length} Tests
              <Info className="w-4 h-4 ml-1 text-gray-400" />
            </button>
            
            {/* QA Grid */}
            <div className="mt-3 p-3 bg-gray-50 rounded relative">
              <div className="grid grid-cols-4 gap-2">
                {dummyTaskData.aggregateScores.map((score, idx) => (
                  <QABubble
                    key={idx}
                    category={score.category}
                    passRate={score.passRate}
                    onClick={handleQAGridClick}
                  />
                ))}
              </div>
              <button
                className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                onClick={handleFullQAClick}
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
            
            {/* Expanded Test List */}
            {expandedTests && (
              <div className="mt-2 space-y-1">
                {dummyTaskData.testScenarios.map(scenario => (
                  <TestScenarioRow
                    key={scenario.id}
                    scenario={scenario}
                    onClick={handleTestClick}
                    onHover={() => {}}
                    onHoverEnd={() => {}}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Sidebar Tier 1 - Middle Third */}
      <div className="w-1/3 bg-white border-r border-gray-200">
        {sidebar1Content ? (
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="font-semibold text-gray-800">{sidebar1Content.title}</h3>
              <button
                onClick={() => closeSidebar(1)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 overflow-auto flex-1">
              {/* Content based on type */}
              {sidebar1Content.type === 'task' && (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Task Name</label>
                    <p className="mt-1">{sidebar1Content.content.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">System Prompt</label>
                    <p className="mt-1 text-sm bg-gray-50 p-3 rounded">{sidebar1Content.content.systemPrompt}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Created</label>
                      <p className="mt-1 text-sm">{sidebar1Content.content.created}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Run</label>
                      <p className="mt-1 text-sm">{sidebar1Content.content.lastRun}</p>
                    </div>
                  </div>
                </div>
              )}
              
              {sidebar1Content.type === 'context' && (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Environment</label>
                    <p className="mt-1">{sidebar1Content.content.environment}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Variables</label>
                    <pre className="mt-1 text-xs bg-gray-50 p-3 rounded overflow-auto">
                      {JSON.stringify(sidebar1Content.content.variables, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
              
              {sidebar1Content.type === 'test' && (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <p className="mt-1">
                      {sidebar1Content.content.status === 'pass' ? '✅ Passed' : '❌ Failed'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Input</label>
                    <pre className="mt-1 text-xs bg-gray-50 p-3 rounded overflow-auto">
                      {JSON.stringify(sidebar1Content.content.input, null, 2)}
                    </pre>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Duration</label>
                      <p className="mt-1 text-sm">{sidebar1Content.content.duration}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Memory</label>
                      <p className="mt-1 text-sm">{sidebar1Content.content.memory}</p>
                    </div>
                  </div>
                </div>
              )}
              
              {sidebar1Content.type === 'requirements' && (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-2xl font-bold text-gray-800">{sidebar1Content.content.passRate}%</p>
                    <p className="text-sm text-gray-600">Pass Rate</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Requirements</label>
                    <ul className="mt-2 space-y-2">
                      {sidebar1Content.content.requirements.map((req, idx) => (
                        <li key={idx} className="text-sm text-gray-700 flex items-start">
                          <span className="mr-2">•</span>
                          <span>{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
              
              {sidebar1Content.type === 'qa-dashboard' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-green-50 p-3 rounded text-center">
                      <p className="text-2xl font-bold text-green-600">{sidebar1Content.content.passed}</p>
                      <p className="text-sm text-gray-600">Passed</p>
                    </div>
                    <div className="bg-red-50 p-3 rounded text-center">
                      <p className="text-2xl font-bold text-red-600">{sidebar1Content.content.failed}</p>
                      <p className="text-sm text-gray-600">Failed</p>
                    </div>
                    <div className="bg-blue-50 p-3 rounded text-center">
                      <p className="text-2xl font-bold text-blue-600">{sidebar1Content.content.avgPassRate}%</p>
                      <p className="text-sm text-gray-600">Avg Pass</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Category Breakdown</label>
                    <div className="mt-2 space-y-2">
                      {sidebar1Content.content.categories.map((cat, idx) => (
                        <div key={idx} className="flex justify-between items-center">
                          <span className="text-sm text-gray-700">{cat.category}</span>
                          <div className="flex items-center">
                            <div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  cat.passRate > 80 ? 'bg-green-500' : 
                                  cat.passRate > 50 ? 'bg-orange-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${cat.passRate}%` }}
                              />
                            </div>
                            <span className="text-sm text-gray-600 w-12 text-right">{cat.passRate}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-400">
            <p className="text-center">Click on any element in the task card<br />to view details</p>
          </div>
        )}
      </div>

      {/* Sidebar Tier 2 - Right Third */}
      <div className="w-1/3 bg-gray-50">
        {sidebar2Content ? (
          <div className="h-full flex flex-col">
            <div className="p-4 border-b border-gray-200 bg-white flex justify-between items-center">
              <h3 className="font-semibold text-gray-800">Grader Details</h3>
              <button
                onClick={() => closeSidebar(2)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 overflow-auto flex-1">
              {sidebar2Content.type === 'grader' && (
                <div className="space-y-4">
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-medium text-gray-800 mb-3">Grader Scores</h4>
                    <div className="space-y-3">
                      {sidebar2Content.scenario.graderScores.map((score, idx) => (
                        <div key={idx} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`w-5 h-5 rounded-full mr-3 ${
                              score.status === 'pass' ? 'bg-green-500' :
                              score.status === 'warn' ? 'bg-orange-500' : 'bg-red-500'
                            }`} />
                            <span className="text-sm text-gray-700">{score.criterion}</span>
                          </div>
                          <span className="text-sm font-medium">{score.score}%</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-medium text-gray-800 mb-3">Output</h4>
                    <pre className="text-sm bg-gray-50 p-3 rounded">
{JSON.stringify({
  status: sidebar2Content.scenario.status,
  output: sidebar2Content.scenario.output,
  timestamp: '2024-01-15T14:45:00Z'
}, null, 2)}
                    </pre>
                  </div>
                  
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-medium text-gray-800 mb-2">Overall Status</h4>
                    <p className={`text-lg font-bold ${
                      sidebar2Content.scenario.status === 'pass' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {sidebar2Content.scenario.status === 'pass' ? 'PASSED' : 'FAILED'}
                    </p>
                    <button className="mt-3 text-sm text-blue-600 hover:text-blue-800">
                      View detailed logs →
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-400">
            <p className="text-center">Additional details will appear here<br />when you select a test case</p>
          </div>
        )}
      </div>
    </div>
  );
}