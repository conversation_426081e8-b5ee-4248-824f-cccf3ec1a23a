#!/usr/bin/env python3
"""
Simple screenshot tool using macOS screencapture and Chrome
"""
import subprocess
import time
import os
import sys
from datetime import datetime

def take_screenshot(port="5173", path=""):
    """Open browser and take screenshot using macOS tools"""
    
    # Clean up the path - remove leading slash if present
    if path.startswith("/"):
        path = path[1:]
    
    url = f"http://localhost:{port}/{path}" if path else f"http://localhost:{port}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = f"screenshots/screenshot_{timestamp}.png"
    
    # Ensure directory exists
    os.makedirs("screenshots", exist_ok=True)
    
    # Open URL in Chrome
    print(f"Opening {url} in Chrome...")
    subprocess.run(["open", "-a", "Google Chrome", url])
    
    # Wait for page to load
    time.sleep(3)
    
    # Take screenshot using screencapture - capture window without interaction
    print(f"Taking screenshot of active window...")
    subprocess.run(["screencapture", "-x", "-T", "3", screenshot_path])
    
    print(f"Screenshot saved to: {screenshot_path}")
    
    # Open in Cursor IDE
    try:
        subprocess.run(["cursor", screenshot_path], check=True)
        print("Screenshot opened in Cursor IDE")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Note: Could not open in Cursor, but screenshot saved successfully")
    
    return screenshot_path

if __name__ == "__main__":
    port = sys.argv[1] if len(sys.argv) > 1 else "8080"
    take_screenshot(port)