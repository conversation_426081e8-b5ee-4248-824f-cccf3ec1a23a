# Task Visualization System PRD

## Overview
Build a React-based task visualization system that displays task cards with expandable test scenarios and a three-column layout for progressive detail disclosure.

## Core Requirements

### Layout Structure
- **Three-column layout** (each 1/3 width):
  1. Left: Main task cards view
  2. Middle: First-tier details sidebar
  3. Right: Second-tier details sidebar (grader details)

### Task Card Component
The main task card should display:
- **Task name** (truncated with "..." if too long, includes info icon)
- **Context line** (e.g., "prod - AWS us-east-1", includes info icon)
- **Expandable test scenarios section** with:
  - Chevron icon (down/up) + "N Tests" button
  - Info icon next to test count
- **QA Grid visualization**:
  - 4x3 grid of colored circles representing test categories
  - Colors: Green (>80%), Orange (>50%), Red (≤50%)
  - Hover tooltips showing "Category: XX%"
  - Chevron-right button in top-right corner

### Test Scenarios List
When expanded, show:
- Each test with pass/fail icon (✅/❌)
- Test description
- Hover tooltip showing output
- Click to open details in sidebars

### Sidebar Behaviors
**Sidebar 1 (Middle)** - Shows different content based on click:
- Task details (name, system prompt, metadata)
- Context/environment configuration
- Test details (status, input, duration, memory)
- Category requirements (from QA grid clicks)
- Full QA dashboard (from chevron-right click)

**Sidebar 2 (Right)** - Shows:
- Grader scores with colored indicators
- Output details
- Overall status (PASSED/FAILED)

### Interactive Elements
1. **Clickable elements with hover states**:
   - Task name → Task configuration
   - Context → Environment details
   - QA bubbles → Category requirements
   - Test rows → Test + grader details
   - Chevron-right → Full QA dashboard

2. **Visual feedback**:
   - Hover effects on all clickable elements
   - Tooltips for QA bubbles and test outputs
   - Close buttons (X) on sidebars

### Styling Requirements
- Clean, modern design with gray color scheme
- White cards with subtle shadows
- Gray-50 background for main container
- Proper spacing and padding
- Responsive hover states
- Smooth transitions

### Data Structure
Must support:
- Task metadata (id, name, context, systemPrompt)
- Test scenarios array with:
  - id, description, status, output, input
  - graderScores array (criterion, score, status)
- Aggregate scores for QA grid visualization

## Success Criteria
1. Three-column layout matches reference exactly
2. All interactive elements work as specified
3. Clean, professional appearance
4. Smooth hover effects and transitions
5. Proper data flow between components
6. Responsive sidebar content based on clicks