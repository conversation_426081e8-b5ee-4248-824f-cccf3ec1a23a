# Task Visualization Development Commands

# Start the development server with hot-reloading
dev:
    cd task-viz && npm run dev

# Install dependencies for the Vite project  
install:
    cd task-viz && npm install

# Build the project for production
build:
    cd task-viz && npm run build

# Take a screenshot of the running app
screenshot port="5173":
    python3 screenshot_simple.py {{port}} /

# Open the app in browser
open:
    open http://localhost:5173

# Start server and open browser
start:
    cd task-viz && npm run dev &
    sleep 2
    open http://localhost:5173

# Clean up - kill dev server
stop:
    lsof -ti:5173 | head -1 | xargs kill -9 2>/dev/null || echo "No server running on port 5173"

# Show this help
help:
    @just --list