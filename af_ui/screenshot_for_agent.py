#!/usr/bin/env python3
"""
Simple screenshot utility for <PERSON> to analyze UI visually.
Uses macOS screencapture for actual screenshots.
"""

import subprocess
import time
import os
import sys
from datetime import datetime


def capture_ui_screenshot(url="http://localhost:3000", output_dir="screenshots"):
    """
    Capture a screenshot of the UI for Claude analysis.
    
    Args:
        url: The localhost URL to screenshot
        output_dir: Directory to save screenshots
    
    Returns:
        dict: Screenshot path and metadata
    """
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate timestamp for unique filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = os.path.join(output_dir, f"screenshot_{timestamp}.png")
    
    try:
        # Open URL in Chrome
        print(f"Opening {url} in Chrome...")
        subprocess.run(["open", "-a", "Google Chrome", url])
        
        # Wait for page to load
        time.sleep(3)
        
        # Take screenshot using screencapture - capture window without interaction
        print(f"Taking screenshot...")
        subprocess.run(["screencapture", "-x", "-T", "2", screenshot_path])
        
        print(f"Screenshot saved: {screenshot_path}")
        
        # Open in Cursor IDE
        try:
            subprocess.run(["cursor", screenshot_path], check=True)
            print("Screenshot opened in Cursor IDE")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("Note: Could not open in Cursor, but screenshot saved successfully")
        
        return {
            "screenshot_path": screenshot_path,
            "url": url,
            "timestamp": timestamp
        }
        
    except Exception as e:
        print(f"Error capturing screenshot: {e}")
        return None


def main():
    """CLI entry point"""
    url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:3000"
    result = capture_ui_screenshot(url)
    
    if result:
        print(f"✅ Screenshot captured successfully")
        print(f"📁 Path: {result['screenshot_path']}")
        print(f"🌐 URL: {result['url']}")
    else:
        print("❌ Failed to capture screenshot")


if __name__ == "__main__":
    main()