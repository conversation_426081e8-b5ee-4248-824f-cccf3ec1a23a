#!/usr/bin/env python3
"""
Simple screenshot utility for <PERSON> to analyze UI visually.
Uses Node.js playwright script for consistency.
"""

import subprocess
import os
import sys
from datetime import datetime


def capture_ui_screenshot(url="http://localhost:3000", output_dir="screenshots"):
    """
    Capture a screenshot of the UI for Claude analysis.
    
    Args:
        url: The localhost URL to screenshot
        output_dir: Directory to save screenshots
    
    Returns:
        dict: Screenshot path and metadata
    """
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Extract port from URL
    port = url.split(":")[-1].split("/")[0]
    
    # Generate timestamp for unique filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        # Use the existing playwright setup in task-viz
        print(f"Taking screenshot of {url}...")
        result = subprocess.run(
            ["node", "task-viz/playwright_screenshot.js", port],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            # Extract filename from output
            output_lines = result.stdout.strip().split('\n')
            for line in output_lines:
                if "Screenshot saved to:" in line:
                    saved_path = line.split("Screenshot saved to:")[-1].strip()
                    # Move file to our screenshots directory
                    filename = os.path.basename(saved_path)
                    new_path = os.path.join(output_dir, filename.replace("playwright_", "screenshot_"))
                    
                    # Copy the file
                    subprocess.run(["cp", saved_path, new_path])
                    
                    print(f"Screenshot saved: {new_path}")
                    
                    # Open in Cursor IDE
                    try:
                        subprocess.run(["cursor", new_path], check=True)
                        print("Screenshot opened in Cursor IDE")
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        print("Note: Could not open in Cursor, but screenshot saved successfully")
                    
                    return {
                        "screenshot_path": new_path,
                        "url": url,
                        "timestamp": timestamp
                    }
        else:
            print(f"Error: {result.stderr}")
            
    except Exception as e:
        print(f"Error capturing screenshot: {e}")
    
    return None


def main():
    """CLI entry point"""
    url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:3000"
    result = capture_ui_screenshot(url)
    
    if result:
        print(f"✅ Screenshot captured successfully")
        print(f"📁 Path: {result['screenshot_path']}")
        print(f"🌐 URL: {result['url']}")
    else:
        print("❌ Failed to capture screenshot")


if __name__ == "__main__":
    main()