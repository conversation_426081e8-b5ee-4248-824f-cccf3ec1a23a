#!/usr/bin/env python3
"""
Screenshot tool for browser automation
"""
import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import os
from datetime import datetime

def take_screenshot(url, output_path=None):
    """Take a screenshot of the given URL"""
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # Run in background
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # Create driver
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # Navigate to URL
        print(f"Loading {url}...")
        driver.get(url)
        
        # Wait for page to load
        time.sleep(3)
        
        # Generate filename if not provided
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"screenshots/screenshot_{timestamp}.png"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Take screenshot
        driver.save_screenshot(output_path)
        print(f"Screenshot saved to: {output_path}")
        
        # Also get page source for debugging
        with open(output_path.replace('.png', '.html'), 'w') as f:
            f.write(driver.page_source)
        print(f"Page source saved to: {output_path.replace('.png', '.html')}")
        
        return output_path
        
    finally:
        driver.quit()

if __name__ == "__main__":
    port = sys.argv[1] if len(sys.argv) > 1 else "8080"
    url = f"http://localhost:{port}/"
    
    screenshot_path = take_screenshot(url)