import asyncio
import copy
import json
import sys
from typing import Any

from prompt_generator.core import run_orchestrator
from refinement_pipeline.core import RefinementPipeline
from refinement_pipeline.models import PromptFeedback
from requirements_doc_generator.core import RequirementsGenerator
from synthetic_data_generator.core import SeedGenerator
from test_case_chunker.core import TestCaseChunker


def to_serializable(obj):
    if hasattr(obj, "to_dict"):
        return to_serializable(obj.to_dict())
    if hasattr(obj, "__dict__") and not isinstance(obj, type):
        return {k: to_serializable(v) for k, v in obj.__dict__.items()}
    if isinstance(obj, dict):
        return {k: to_serializable(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [to_serializable(i) for i in obj]
    if isinstance(obj, tuple):
        return tuple(to_serializable(i) for i in obj)
    return obj


def safe_json_dumps(obj, **kwargs) -> str:
    def default(o):
        print(f"[DEBUG] Non-serializable type encountered: {type(o)}")
        return to_serializable(o)

    return json.dumps(obj, default=default, **kwargs)


def capture_step_output(step_name: str, output: Any, step_number: int = None) -> str:
    """Capture and format the output of a step for logging.
    
    Args:
        step_name: Name of the step
        output: The output data to capture
        step_number: Optional step number for ordering
        
    Returns:
        str: Formatted output string
    """
    step_header = f"===== STEP {step_number}: {step_name.upper()} =====" if step_number else f"===== {step_name.upper()} ====="
    
    # Convert output to serializable format
    serializable_output = to_serializable(output)
    
    # Format the output
    formatted_output = f"{step_header}\n"
    formatted_output += f"Timestamp: {asyncio.get_event_loop().time() if asyncio.get_event_loop().is_running() else 'N/A'}\n"
    formatted_output += f"Output Type: {type(output).__name__}\n"
    formatted_output += f"Output Size: {len(safe_json_dumps(serializable_output))} characters\n"
    formatted_output += f"Output Data:\n{safe_json_dumps(serializable_output, indent=2)}\n"
    formatted_output += "=" * 50 + "\n\n"
    
    return formatted_output


def write_step_output(step_output: str, filename: str = "step_by_step_output.txt"):
    """Write a step output immediately to the file.
    
    Args:
        step_output: The formatted step output string
        filename: The file to write to
    """
    # Create file with header if it doesn't exist
    try:
        with open(filename, "r") as f:
            existing_content = f.read()
    except FileNotFoundError:
        existing_content = ""
    
    if not existing_content:
        with open(filename, "w") as f:
            f.write("FULL FLOW STEP-BY-STEP OUTPUT\n")
            f.write("=" * 50 + "\n\n")
    
    # Append the step output
    with open(filename, "a") as f:
        f.write(step_output)
        f.flush()  # Force write to disk immediately
    
    print(f"📝 Step output written to {filename}")


async def feedback_loop(actual_prompt: dict[str, Any], req_doc: dict[str, Any]) -> PromptFeedback | None:
    """Run the feedback loop.

    Runs the synthetic data generator, test case chunker, and test executor.
    Returns the prompt feedback.

    Args:
        actual_prompt: The actual prompt to use for the feedback loop.
        req_doc: The requirements document to use for the feedback loop.

    Returns:
        PromptFeedback: The prompt feedback.

    """
    # 3. Synthetic Data Generator (using requirements doc as input)
    print("[3/6] Running Synthetic Data Generator...")
    synth_gen = SeedGenerator()

    synth_output = await synth_gen.generate_json(actual_prompt, req_doc)
    synth_output_serializable = to_serializable(synth_output)

    # 4. Test Case Chunker (using synthetic data and prompt as input)
    print("[4/6] Running Test Case Chunker...")
    chunker = TestCaseChunker()

    # Extract seeds from synthetic data output - flatten the nested structure
    seeds_data = synth_output_serializable.get("seeds", {})
    seeds = []

    # Flatten seeds from all categories
    for _, category_data in seeds_data.items():
        if isinstance(category_data, dict) and "seeds" in category_data:
            category_seeds = category_data.get("seeds", [])
            seeds.extend(category_seeds)

    if not seeds:
        print("[WARNING] No seeds found in synthetic data output")
        chunked_cases = None
    else:
        print(f"[CHUNKER] Found {len(seeds)} seeds to process")
        chunked_cases = await chunker.chunk_test_cases(seeds, actual_prompt)

        # Get statistics
        stats = chunker.get_statistics(chunked_cases)
        print(f"[CHUNKER] Generated {stats['total_cases']} test cases")
        if "success_rate" in stats:
            print(f"[CHUNKER] Success rate: {stats['success_rate']:.2%}")
        else:
            print("[CHUNKER] No test cases generated")

    # 5. Test Executor (using chunked test cases as input)
    print("[5/6] Running Test Executor...")
    refinement_pipeline = RefinementPipeline()
    prompt_feedback = None
    if chunked_cases:
        prompt_feedback = await refinement_pipeline.generate_feedback(
            test_cases=chunked_cases.test_cases,
            requirements_doc=req_doc,
            prompt=actual_prompt,
        )

    return prompt_feedback if prompt_feedback else None


async def main() -> None:
    # Delete existing step-by-step output file for fresh start
    import os
    step_output_file = "step_by_step_output.txt"
    if os.path.exists(step_output_file):
        os.remove(step_output_file)
        print(f"🗑️  Deleted existing {step_output_file} for fresh start")
    
    # Get the initial prompt
    if len(sys.argv) > 1:
        input_prompt = sys.argv[1]
    else:
        input_prompt = "Create a comprehensive industry 101 platform that provides educational content, interactive learning modules, and progress tracking for various industries"
    
    # Capture and write initial input immediately
    step_output = capture_step_output("INITIAL INPUT", input_prompt, 0)
    write_step_output(step_output)

    # 1. Requirements Document Generator
    print("[1/6] Running Requirements Document Generator...")
    req_gen = RequirementsGenerator()
    req_doc = req_gen.generate_json(input_prompt)
    initial_req_doc = copy.deepcopy(req_doc)
    
    # Capture and write requirements output immediately
    step_output = capture_step_output("REQUIREMENTS DOCUMENT GENERATOR", req_doc, 1)
    write_step_output(step_output)

    # 2. Prompt Generator (using requirements doc as input)
    print("[2/6] Running Prompt Generator...")
    prompt_input = req_doc["requirements_doc"]["problem_statement"] + "\n" + "\n".join(req_doc["requirements_doc"]["key_requirements"])
    state, history = run_orchestrator(prompt_input)
    prompt_output = state

    if hasattr(prompt_output, "to_dict") or hasattr(prompt_output, "__dict__"):
        prompt_output_dict = to_serializable(prompt_output)
    else:
        prompt_output_dict = prompt_output

    actual_prompt = prompt_output_dict.get("json_prompt", prompt_output_dict)
    initial_prompt = copy.deepcopy(actual_prompt)
    
    # Capture and write prompt generator output immediately
    step_output = capture_step_output("PROMPT GENERATOR", {
        "prompt_input": prompt_input,
        "generated_prompt": actual_prompt,
        "orchestrator_history": history
    }, 2)
    write_step_output(step_output)

    refinement_pipeline = RefinementPipeline()

    for i in range(3):
        prompt_feedback = await feedback_loop(actual_prompt, req_doc)
        if not prompt_feedback:
            break
        if prompt_feedback.issues_present:
            print("[6/6] Running Prompt Refiner...")
            print(f"[REFINER] Refining prompt {i + 1} of 3...")
            refined_prompt = await refinement_pipeline.refine_prompt(actual_prompt, prompt_feedback)
            actual_prompt = refined_prompt
            
            # Capture and write refinement output immediately
            step_output = capture_step_output(f"PROMPT REFINEMENT ITERATION {i+1}", {
                "feedback": to_serializable(prompt_feedback),
                "refined_prompt": actual_prompt
            }, 3 + i)
            write_step_output(step_output)
        else:
            break

    print(f"[FINAL PROMPT] {actual_prompt}")

    print("[3/6] Running Synthetic Data Generator...")
    synth_gen = SeedGenerator()

    synth_output = await synth_gen.generate_json(actual_prompt, req_doc)
    synth_output_serializable = to_serializable(synth_output)
    
    # Capture and write synthetic data output immediately
    step_output = capture_step_output("SYNTHETIC DATA GENERATOR", synth_output_serializable, 6)
    write_step_output(step_output)

    # 4. Test Case Chunker (using synthetic data and prompt as input)
    print("[4/6] Running Test Case Chunker...")
    chunker = TestCaseChunker()

    # Extract seeds from synthetic data output - flatten the nested structure
    seeds_data = synth_output_serializable.get("seeds", {})
    seeds = []

    # Flatten seeds from all categories
    for _, category_data in seeds_data.items():
        if isinstance(category_data, dict) and "seeds" in category_data:
            category_seeds = category_data.get("seeds", [])
            seeds.extend(category_seeds)

    if not seeds:
        print("[WARNING] No seeds found in synthetic data output")
        chunked_cases = None
    else:
        print(f"[CHUNKER] Found {len(seeds)} seeds to process")
        chunked_cases = await chunker.chunk_test_cases(seeds, actual_prompt)
    
    # Capture and write chunker output immediately
    step_output = capture_step_output("TEST CASE CHUNKER", {
        "seeds_count": len(seeds),
        "chunked_cases": to_serializable(chunked_cases) if chunked_cases else None
    }, 7)
    write_step_output(step_output)

    # 5. Requirements Editor (using initial prompt and edited prompt as input)
    print("[6/6] Running Requirements Editor...")
    edited_requirements = await refinement_pipeline.edit_requirements(
        requirements_doc=initial_req_doc,
        initial_prompt=initial_prompt,
        edited_prompt=actual_prompt,
    )
    
    # Capture and write requirements editor output immediately
    step_output = capture_step_output("REQUIREMENTS EDITOR", edited_requirements, 8)
    write_step_output(step_output)

    # Write final results (existing format)
    with open("results.txt", "w") as f:
        f.write("===== INITIAL PROMPT =====\n")
        f.write(safe_json_dumps(input_prompt, indent=2))
        f.write("\n\n")
        f.write("===== INITIAL REQUIREMENTS =====\n")
        f.write(safe_json_dumps(initial_req_doc, indent=2))
        f.write("\n\n")
        f.write("===== GENERATED PROMPT =====\n")
        f.write(safe_json_dumps(initial_prompt, indent=2))
        f.write("\n\n")
        f.write("===== FINAL PROMPT =====\n")
        f.write(safe_json_dumps(actual_prompt, indent=2))
        f.write("\n\n")
        f.write("===== FINAL REQUIREMENTS =====\n")
        f.write(safe_json_dumps(edited_requirements, indent=2))
        f.write("\n\n")
        f.write("===== FINAL TESTS =====\n")
        f.write(safe_json_dumps(chunked_cases, indent=2))
        f.write("\n\n")

    print("\n✅ Full flow complete.")
    print("📄 Step-by-step output written to: step_by_step_output.txt")
    print("📄 Final results saved to: results.txt")


if __name__ == "__main__":
    asyncio.run(main())
