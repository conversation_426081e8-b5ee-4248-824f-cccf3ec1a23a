"""
Startup script for the Prompt Engineering Ecosystem.
Launches all three FastAPI services and provides monitoring.
"""

import subprocess
import time
import signal
import sys
import os
from typing import List, Dict
import asyncio
import aiohttp

class ServiceManager:
    def __init__(self):
        self.services = {
            "prompt_generator": {
                "script": "prompt_generator_api.py",
                "port": 8001,
                "name": "Prompt Generator API",
                "process": None
            },
            "requirements_doc": {
                "script": "requirements_doc_api.py", 
                "port": 8002,
                "name": "Requirements Document Generator API",
                "process": None
            },
            "synthetic_data": {
                "script": "synthetic_data_api.py",
                "port": 8003,
                "name": "Synthetic Data Generator API",
                "process": None
            }
        }
        self.running = False
        
    def start_service(self, service_id: str, service_config: Dict) -> bool:
        """Start a single service."""
        try:
            print(f"🚀 Starting {service_config['name']} on port {service_config['port']}...")
            
            # Start the service in a subprocess
            process = subprocess.Popen(
                [sys.executable, service_config['script']],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            service_config['process'] = process
            
            # Wait a moment for the service to start
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                print(f"✅ {service_config['name']} started successfully")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ {service_config['name']} failed to start")
                print(f"   Error: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to start {service_config['name']}: {e}")
            return False
    
    def start_all_services(self) -> bool:
        """Start all services."""
        print("=" * 60)
        print("🚀 STARTING PROMPT ENGINEERING ECOSYSTEM")
        print("=" * 60)
        
        success_count = 0
        for service_id, service_config in self.services.items():
            if self.start_service(service_id, service_config):
                success_count += 1
            else:
                # Stop any already started services
                self.stop_all_services()
                return False
        
        if success_count == len(self.services):
            print(f"\n✅ All {success_count} services started successfully!")
            self.running = True
            return True
        else:
            print(f"\n❌ Only {success_count}/{len(self.services)} services started")
            return False
    
    def stop_service(self, service_id: str, service_config: Dict):
        """Stop a single service."""
        if service_config['process']:
            try:
                print(f"🛑 Stopping {service_config['name']}...")
                service_config['process'].terminate()
                service_config['process'].wait(timeout=5)
                print(f"✅ {service_config['name']} stopped")
            except subprocess.TimeoutExpired:
                print(f"⚠️  Force killing {service_config['name']}...")
                service_config['process'].kill()
            except Exception as e:
                print(f"❌ Error stopping {service_config['name']}: {e}")
    
    def stop_all_services(self):
        """Stop all services."""
        print("\n🛑 Stopping all services...")
        for service_id, service_config in self.services.items():
            self.stop_service(service_id, service_config)
        self.running = False
    
    async def check_service_health(self, session: aiohttp.ClientSession, service_config: Dict) -> bool:
        """Check if a service is responding."""
        try:
            async with session.get(f"http://localhost:{service_config['port']}/health", timeout=5) as response:
                return response.status == 200
        except:
            return False
    
    async def monitor_services(self):
        """Monitor all services for health."""
        print("\n📊 MONITORING SERVICES")
        print("-" * 40)
        
        async with aiohttp.ClientSession() as session:
            while self.running:
                healthy_count = 0
                
                for service_id, service_config in self.services.items():
                    is_healthy = await self.check_service_health(session, service_config)
                    status_icon = "✅" if is_healthy else "❌"
                    print(f"{status_icon} {service_config['name']}: {'Healthy' if is_healthy else 'Unhealthy'}")
                    
                    if is_healthy:
                        healthy_count += 1
                
                print(f"\n📈 Status: {healthy_count}/{len(self.services)} services healthy")
                print("-" * 40)
                
                # Wait before next check
                await asyncio.sleep(10)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.stop_all_services()
        sys.exit(0)

def main():
    """Main function."""
    manager = ServiceManager()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, manager.signal_handler)
    signal.signal(signal.SIGTERM, manager.signal_handler)
    
    try:
        # Start all services
        if not manager.start_all_services():
            print("❌ Failed to start all services. Exiting.")
            sys.exit(1)
        
        # Print service URLs
        print("\n🌐 SERVICE URLs:")
        print("-" * 40)
        for service_id, service_config in manager.services.items():
            print(f"• {service_config['name']}: http://localhost:{service_config['port']}")
            print(f"  - Health: http://localhost:{service_config['port']}/health")
            print(f"  - Docs: http://localhost:{service_config['port']}/docs")
        
        print("\n📋 USAGE:")
        print("-" * 40)
        print("• Run health check: python health_checker.py")
        print("• Press Ctrl+C to stop all services")
        print("• Check individual service logs for detailed information")
        
        # Start monitoring
        asyncio.run(manager.monitor_services())
        
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        manager.stop_all_services()

if __name__ == "__main__":
    main() 