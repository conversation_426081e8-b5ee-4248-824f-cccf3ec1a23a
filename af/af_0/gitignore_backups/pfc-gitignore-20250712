# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# LINTER CONFIG NOT READY TO SHARE WITH GROUP.
.sourcery.yaml
.ruff_cache/
.pytest_cache/
# .vscode/ # we share this.
# .cursor/ # we share this.

# IDE
# .vscode/
# !.vscode/extensions.json
# !.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
# logs/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Configuration files with sensitive data
# config.yaml
config_local.yaml

# Temporary files
*.tmp
*.temp
# .tpm/* 