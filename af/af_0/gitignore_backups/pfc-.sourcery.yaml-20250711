# Sourcery configuration
# Minimal, non-verbose setup for code quality

# Rule configuration - focus on refactoring, not verbose style checks
rule_settings:
  enable:
    - default  # Core refactoring rules
    - gpsg  # Google Python Style Guide (selective)
  disable:
    # Disable verbose/opinionated rules
    - require-parameter-annotation  # Don't force type hints everywhere
    - require-return-annotation  # Don't force return type hints
    - no-long-functions  # We have our own length limits
    - comments  # Don't analyze comment quality
    
  rule_types:
    - refactoring  # Focus on code improvements
    - suggestion  # Helpful suggestions
    # - comment  # Disabled - too verbose
    
# Don't analyze these paths
ignore:
  - .venv/
  - .archive/
  - tests/  # Can be removed if you want test analysis
  - notebooks/
  - docs/

# Metrics thresholds (optional)
metrics:
  quality_threshold: 25.0  # Minimum code quality score

# GitHub integration settings
github:
  request_review: "none"  # Don't auto-request reviews
  sourcery_branch: sourcery/{base_branch}  # Branch naming pattern