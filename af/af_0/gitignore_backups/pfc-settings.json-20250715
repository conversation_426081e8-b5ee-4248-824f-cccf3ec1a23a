{
    // Python configuration
    "python.defaultInterpreterPath": ".venv/bin/python",
    "[python]": {
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll.ruff": "explicit",
            "source.organizeImports.ruff": "explicit"
        }
    },
    
    // Notebook configuration
    "notebook.formatOnSave.enabled": true,
    "notebook.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    
    // Ruff configuration (v2024+ native server)
    "ruff.enable": true,
    "ruff.configuration": "pyproject.toml",
    
    // Cursor Pyright configuration
    "cursorpyright.analysis.typeCheckingMode": "strict",
    "cursorpyright.analysis.inlayHints.variableTypes": true,
    "cursorpyright.analysis.inlayHints.functionReturnTypes": true,
    
    // File exclusions
    "files.exclude": {
        "uv.lock": true,
        ".archive": true,
        ".ruff_cache": true,
        ".venv": true,
        ".mypy_cache": true,
        ".ruffignore": true,
        "requirements.txt": true,
        ".pytest_cache": true,
        ".cursorignore": true,
        ".coverage": true,
        ".sourcery.yaml": false,
        "src/pfc/__init__.py": true,
        "src/pfc/core/__init__.py": true,
        "**__init__.py": true,
        "src/pfc/doc_gen/__init__.py": true,
        ".sourcery": true,
        ".github": true,
        "src/pfc/__pycache__": true,
        "src/pfc/core/__pycache__": true,
        "src/pfc/core/utils/__pycache__": true,
        "src/pfc/core/utils/adapters/__init__.py": true,
        "src/pfc/core/utils/__init__.py": true,
        "src/pfc/core/llms/__pycache__": true,
        ".tmp/af3/__init__.py": true,
        ".tmp/af3/__pycache__": true
    }
}