{"files.exclude": {"uv.lock": true, ".archive": true, ".ruff_cache": true, ".venv": true, ".mypy_cache": true, ".ruffignore": true, "requirements.txt": true, ".pytest_cache": true, ".cursorignore": true, ".coverage": true, ".sourcery.yaml": true, "src/pfc/__init__.py": true, "src/pfc/core/__init__.py": true, "**__init__.py": true, "src/pfc/doc_gen/__init__.py": true, ".sourcery": true, ".github": true, "src/pfc/__pycache__": true, "src/pfc/core/__pycache__": true, "src/pfc/core/utils/__pycache__": true, "src/pfc/core/utils/adapters/__init__.py": true, "src/pfc/core/utils/__init__.py": true, "src/pfc/core/llms/__pycache__": true}}