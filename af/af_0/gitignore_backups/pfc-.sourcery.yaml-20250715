# Sourcery configuration for PFC project
# Focus on refactoring suggestions, not style enforcement

# Ignore common directories
ignore:
  # Core exclusions
  - .tmp/
  - __pycache__/
  - .archive/
  - .venv/
  - build/
  - dist/
  - "*.egg-info"
  - .pytest_cache/
  - .ruff_cache/
  - notebooks/
  - docs/
  
  # Cache directories
  - .mypy_cache/
  - .sourcery/
  - .coverage
  - .coverage.*
  - htmlcov/
  - .hypothesis/
  - .tox/
  - .nox/
  
  # IDE directories
  - .cursor/
  - .vscode/
  - .idea/
  
  # Build outputs
  - site/
  - _build/
  - node_modules/
  
  # Files to ignore
  - "*.pyc"
  - "*.pyo"
  - "*.pyd"
  - "*.so"
  - "*.dll"
  - "*.log"
  - "*.lock"
  - "*.orig"
  - "*.rej"
  - "*.bak"
  - "*.swp"
  - "*.swo"
  - "*~"
  - .DS_Store
  - Thumbs.db
  - .env
  - .env.*
  - justfile
  - Makefile
  - "*.mk"

# Rule configuration
rule_settings:
  # Enable default refactoring rules
  enable:
    - default
    
  # Disable all Google style guide rules
  disable:
    - google-python-style-guide
    
  # Enable all rule types to see various suggestions
  rule_types:
    - refactoring
    - suggestion
    - comment
    
  
# Code quality metrics - set low threshold since we use brittle code
metrics:
  quality_threshold: 15.0 

# # Sourcery configuration
# # Minimal, non-verbose setup for code quality

# # Rule configuration - focus on refactoring, not verbose style checks
# rule_settings:
#   enable:
#     - default  # Core refactoring rules
#     - gpsg  # Google Python Style Guide (selective)
#   disable:
#     # Disable verbose/opinionated rules
#     - require-parameter-annotation  # Don't force type hints everywhere
#     - require-return-annotation  # Don't force return type hints
#     - no-long-functions  # We have our own length limits
#     - comments  # Don't analyze comment quality
    
#   rule_types:
#     - refactoring  # Focus on code improvements
#     - suggestion  # Helpful suggestions
#     # - comment  # Disabled - too verbose
    
# # Don't analyze these paths
# ignore:
#   - .venv/
#   - .archive/
#   - tests/  # Can be removed if you want test analysis
#   - notebooks/
#   - docs/

# # Metrics thresholds (optional)
# metrics:
#   quality_threshold: 25.0  # Minimum code quality score

# # GitHub integration settings
# github:
#   request_review: "none"  # Don't auto-request reviews
#   sourcery_branch: sourcery/{base_branch}  # Branch naming pattern