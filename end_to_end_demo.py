"""
End-to-end demo: Generate requirements, prompt, and synthetic data using the API ecosystem.
OPTIMIZED FOR SPEED
"""

import asyncio
import aiohttp
import sys
import json
import time
from datetime import datetime

REQUIREMENTS_API = "http://localhost:8002/generate-requirements"
PROMPT_API = "http://localhost:8001/generate-prompt"
SYNTHETIC_API = "http://localhost:8003/generate-seeds"

async def generate_requirements(session, initial_prompt):
    req = {
        "initial_prompt": initial_prompt,
        "max_iterations": 1,  # Reduced from 2 for speed
        "output_format": "json"
    }
    async with session.post(REQUIREMENTS_API, json=req) as resp:
        resp.raise_for_status()
        data = await resp.json()
        print("\n[1] Requirements Document:")
        print(json.dumps(data["requirements_doc"], indent=2))
        return data["requirements_doc"]

async def generate_prompt(session, task, requirements_doc):
    req = {
        "task": task,
        "target_score": 7.0,  # Reduced from 8.5 for speed
        "max_turns": 4  # Reduced from 8 for speed
    }
    async with session.post(PROMPT_API, json=req) as resp:
        resp.raise_for_status()
        data = await resp.json()
        print("\n[2] Generated Prompt:")
        print(data["prompt"])
        return data["prompt"]

async def generate_synthetic_data(session, prompt, requirements_doc):
    req = {
        "prompt_json": {"prompt": prompt},
        "requirements_doc": {"requirements_doc": requirements_doc},
        "max_iterations": 1,  # Keep at 1 for speed
        "seed_count_per_category": 3,  # Reduced from 5 for speed
        "enable_seed_selection": False  # Disable selection for speed
    }
    try:
        async with session.post(SYNTHETIC_API, json=req) as resp:
            resp.raise_for_status()
            data = await resp.json()
            print("\n[3] Synthetic Test Seeds:")
            for i, seed in enumerate(data["seeds"], 1):
                print(f"Seed {i}: {seed['input']} => {seed['expected_output']}")
            return data["seeds"]
    except aiohttp.ClientResponseError as e:
        print(f"\n❌ Synthetic Data Generation failed: {e.status} - {e.message}")
        print("This step is timing out due to heavy processing. The first two steps worked successfully!")
        return []
    except Exception as e:
        print(f"\n❌ Unexpected error in synthetic data generation: {e}")
        return []

def save_comprehensive_output(initial_prompt, requirements_doc, prompt, seeds, execution_time):
    """Save all outputs to a single file that gets rewritten each run."""
    
    # Create comprehensive output structure
    comprehensive_output = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "initial_prompt": initial_prompt,
            "execution_time_seconds": execution_time,
            "pipeline_version": "1.0.0",
            "api_endpoints": {
                "requirements": REQUIREMENTS_API,
                "prompt": PROMPT_API,
                "synthetic": SYNTHETIC_API
            }
        },
        "step_1_requirements": {
            "status": "completed",
            "output": requirements_doc,
            "timestamp": datetime.now().isoformat()
        },
        "step_2_prompt": {
            "status": "completed",
            "output": prompt,
            "timestamp": datetime.now().isoformat()
        },
        "step_3_synthetic_data": {
            "status": "completed" if seeds else "failed",
            "seeds": seeds,
            "total_seeds": len(seeds) if seeds else 0,
            "timestamp": datetime.now().isoformat()
        },
        "summary": {
            "requirements_generated": True,
            "prompt_generated": True,
            "synthetic_data_generated": bool(seeds),
            "total_steps_completed": 3 if seeds else 2,
            "success_rate": "100%" if seeds else "66%"
        }
    }
    
    # Save to single file (overwrites each run)
    filename = "pipeline_output.json"
    
    with open(filename, "w") as f:
        json.dump(comprehensive_output, f, indent=2)
    
    print(f"\n💾 Pipeline output saved to: {filename}")
    print(f"📊 Summary: {comprehensive_output['summary']['total_steps_completed']}/3 steps completed")
    print(f"✅ Success rate: {comprehensive_output['summary']['success_rate']}")
    
    return filename

async def main():
    initial_prompt = (
        sys.argv[1] if len(sys.argv) > 1 else "Create a medical diagnosis assistant for patient triage"
    )
    
    # Use shorter timeouts for faster execution
    timeout = aiohttp.ClientTimeout(
        total=300,  # 5 minutes total timeout
        connect=10,  # 10 seconds to connect
        sock_read=60  # 60 seconds to read response
    )
    
    start_time = time.time()
    
    async with aiohttp.ClientSession(timeout=timeout) as session:
        try:
            print("🚀 Starting end-to-end demo...")
            print(f"📝 Initial prompt: {initial_prompt}")
            
            requirements_doc = await generate_requirements(session, initial_prompt)
            prompt = await generate_prompt(session, initial_prompt, requirements_doc)
            
            print("\n⏳ Generating synthetic data (this may take a while)...")
            seeds = await generate_synthetic_data(session, prompt, requirements_doc)
            
            execution_time = time.time() - start_time
            
            # Save comprehensive output to file
            filename = save_comprehensive_output(
                initial_prompt, requirements_doc, prompt, seeds, execution_time
            )
            
            if seeds:
                print(f"\n✅ Demo completed successfully! Generated {len(seeds)} test seeds.")
            else:
                print("\n⚠️ Demo partially completed. Requirements and Prompt generation worked, but synthetic data generation timed out.")
                print("💡 Tip: The synthetic data generator is doing heavy parallel processing. Consider running it separately with reduced settings.")
                
        except Exception as e:
            print(f"\n❌ Demo failed: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 