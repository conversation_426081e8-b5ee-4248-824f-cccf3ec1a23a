# Data Flow Mapping: Pipeline → UI Bridge

## Overview
This document explains how our pipeline execution data flows into the Task Visualization UI components.

## Data Flow Architecture

```ascii
┌─────────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   PIPELINE DATA     │───▶│  TRANSFORMATION  │───▶│    UI COMPONENTS    │
│                     │    │     LAYER        │    │                     │
│ step_combined.json  │    │                  │    │ React SPA Interface │
│ generation_as_      │    │ data_transformer │    │                     │
│ validation.json     │    │                  │    │ 3-Panel Layout      │
└─────────────────────┘    └──────────────────┘    └─────────────────────┘
```

## UI Layout Structure

```ascii
FULL SCREEN LAYOUT:
┌─────────────────────┬──────────────────────┬─────────────────────┐
│   MAIN VIEW (1/3)   │  SIDEBAR TIER 1 (1/3)│ SIDEBAR TIER 2 (1/3)│
├─────────────────────┼──────────────────────┼─────────────────────┤
│                     │                      │                     │
│ ┌─────────────────┐ │                      │                     │
│ │Task: Industry   │ │                      │                     │
│ │      101...   ⓘ │ │                      │                     │
│ │Context: pipeline│ │                      │                     │
│ │        v1.0   ⓘ │ │                      │                     │
│ │                 │ │                      │                     │
│ │▼ 9 Tests     ⓘ │ │                      │                     │
│ │┌──────────────┐│ │                      │                     │
│ ││● ● ○ ●   [▶]││ │  CLICK TO EXPAND     │                     │
│ ││○ ● ○ ●      ││ │  DETAILS HERE        │                     │
│ ││● ○ ◐ ○      ││ │                      │                     │
│ │└──────────────┘│ │                      │                     │
│ └─────────────────┘ │                      │                     │
└─────────────────────┴──────────────────────┴─────────────────────┘
```

## Data Mapping Structure

### 1. Task Card Level
```ascii
PIPELINE DATA                    UI COMPONENT
─────────────────               ────────────────
metadata.initial_prompt    ──▶  taskCard.name
"pipeline v1.0.0"         ──▶  taskCard.context  
final_tests.test_cases     ──▶  taskCard.testScenarios[]
calculated_aggregates      ──▶  taskCard.aggregateScores[]
```

### 2. Test Scenarios Mapping
```ascii
PIPELINE STRUCTURE              UI STRUCTURE
──────────────────             ─────────────
test_cases[].seed_id      ──▶  testScenarios[].id
test_cases[].validation   ──▶  testScenarios[].status
test_cases[].user_message ──▶  testScenarios[].description
test_cases[].placeholders ──▶  testScenarios[].input
confidence_score          ──▶  testScenarios[].output
validation.issues         ──▶  testScenarios[].graderScores[]
```

### 3. Aggregate Scores Grid
```ascii
QA GRID VISUALIZATION:
┌──────────────────────────────────────┐
│ Content Quality    Completeness      │
│      ●                ●              │
│     34%              22%             │
│                                      │
│ Accuracy          Confidence         │
│    ●                 ○               │
│   27%               86%              │
└──────────────────────────────────────┘

LEGEND:
● Red    = <50% pass rate (failing)
◐ Orange = 50-80% pass rate (warning) 
○ Green  = >80% pass rate (passing)
```

## Expanded Test List View

```ascii
WHEN TESTS ARE EXPANDED:
┌─────────────────────────────────────────┐
│ Task: Industry 101 Document Generator   │
│ Context: pipeline v1.0.0               │
│                                         │
│ ▲ 9 Tests                               │
│ ┌─────────────────┐                     │
│ │● ● ○ ●         │ (AGGREGATE SCORES)  │
│ │○ ● ○ ●         │                     │
│ │● ○ ◐ ○         │                     │
│ └─────────────────┘                     │
│                                         │
│ ❌ seed_1: Automotive analysis          │ 
│    [hover: "Output: Low confidence"]   │
│                                         │
│ ❌ seed_2: North America region        │
│    [hover: "Output: Validation failed"]│
│                                         │
│ ✅ seed_3: Company profiling           │
│    [hover: "Output: High confidence"]  │
│                                         │
│ ... (6 more test scenarios)            │
└─────────────────────────────────────────┘
```

## Detailed Test Case View

```ascii
CLICKING A TEST CASE - Full Object View:
┌─────────────────────┬──────────────────────┬─────────────────────┐
│ ┌─────────────────┐ │ Test: seed_1         │ Grader Details      │
│ │Task: Industry..  │ │ Automotive Analysis  │                     │
│ │Context: pipeline │ │ ────────────────     │ Grader Scores:      │
│ │                 │ │                      │                     │
│ │▲ 9 Tests        │ │ INPUTS:              │ ┌─────────────────┐ │
│ │❌ seed_1     ◄───┼─┤ {                    │ │ ● Content: 34%  │ │
│ │❌ seed_2        │ │   "region": "N.Am.", │ │ ● Complete: 22% │ │
│ │✅ seed_3        │ │   "period": "2020-23"│ │ ● Accuracy: 27% │ │
│ │❌ seed_4        │ │   "companies": "5"   │ │ ○ Confidence:86%│ │
│ │... 5 more       │ │ }                    │ └─────────────────┘ │
│ │┌──────────────┐│ │                      │                     │
│ ││● ● ○ ●      ││ │ OUTPUT:              │ Overall: FAILED     │
│ ││○ ● ○ ●      ││ │ {                    │ Issues: 4 found     │
│ ││● ○ ◐ ○      ││ │   "status": "failed",│                     │
│ │└──────────────┘│ │   "confidence": 0.4, │ [View validation    │
│ └─────────────────┘ │   "issues": [        │  details]           │
│                     │     "Empty message", │                     │
│                     │     "Low confidence" │                     │
│                     │   ]                  │                     │
│                     │ }                    │                     │
└─────────────────────┴──────────────────────┴─────────────────────┘
```

## Data Sources Available

### 1. `generation_as_validation.json`
- **Purpose**: Meta-validation of pipeline quality
- **Format**: Pre-formatted for SPA consumption
- **Use Case**: Demo pipeline self-assessment capabilities
- **Status**: ✅ Ready to use

### 2. `af_2/_temp2/transformed_spa_data.json`
- **Purpose**: Real pipeline execution results
- **Format**: Transformed from step_combined.json
- **Use Case**: Demo actual task execution outcomes
- **Status**: ✅ Ready to use (11% pass rate - authentic results)

## Integration Points

### Loading Data into UI
```javascript
// Option 1: Load validation-focused data
import validationData from './generation_as_validation.json';

// Option 2: Load execution results
import executionData from './af_2/_temp2/transformed_spa_data.json';

// Use in React component
const TaskVisualizationSystem = () => {
  const [taskData, setTaskData] = useState(validationData);
  // ... rest of component
};
```

### Dynamic Data Switching
```ascii
UI CONTROLS:
┌─────────────────────────────────────────┐
│ Data Source: [Validation ▼] [Execution] │
│                                         │
│ ┌─────────────────┐                     │
│ │Task: Industry..  │                     │
│ │Context: pipeline │                     │
│ └─────────────────┘                     │
└─────────────────────────────────────────┘
```

## Validation & Quality Metrics

### Current Data Quality
- **Validation Data**: 77% average pass rate (demo-quality)
- **Execution Data**: 11% average pass rate (realistic)
- **Format Compliance**: 100% (both datasets)
- **UI Compatibility**: ✅ Full compatibility

### Test Coverage Categories
```ascii
AGGREGATE SCORE CATEGORIES:
┌────────────────┬──────────────┬─────────────┐
│ Content Quality│ Completeness │ Accuracy    │
│      34%       │     22%      │    27%      │
│      ●         │     ●        │    ●        │
└────────────────┴──────────────┴─────────────┘
┌────────────────┬──────────────┬─────────────┐
│ Confidence     │ Format       │ Validation  │
│      86%       │     65%      │    45%      │
│      ○         │     ◐        │    ●        │
└────────────────┴──────────────┴─────────────┘
```

## Next Steps
1. **Choose Data Source**: Select validation or execution data based on demo needs
2. **Load into UI**: Import JSON data into React component
3. **Test Interactions**: Verify all click handlers work with real data
4. **Customize Styling**: Adjust colors/layouts based on actual pass rates