{"version": 1, "rules": [{"name": "Code Architecture Fundamentals", "description": "Core principles for code organization and style", "rules": [{"name": "Pydantic Strict Mode", "description": "All Pydantic models must use strict mode", "severity": "error", "pattern": {"regex": "class\\s+\\w+\\(BaseModel\\):", "filePattern": "*.py", "notMatches": ["model_config = {\"strict\": True", "ClassConfigDict: strict = True"]}, "message": "Pydantic models must use strict mode with 'model_config = {\"strict\": True}'"}, {"name": "Functional Core, Imperative Shell", "description": "Keep business logic pure and separate from side effects", "severity": "warning", "pattern": {"filePattern": "*/core/*.py", "matches": ["import os", "import sys", "print(", "open("], "notMatches": ["def main", "if __name__ == \"__main__\""]}, "message": "Core modules should contain pure functions without side effects or entry points"}, {"name": "Type Hints Required", "description": "All functions must use type hints", "severity": "error", "pattern": {"regex": "def\\s+\\w+\\([^:]*\\):", "filePattern": "*.py", "notMatches": ["def __init__"]}, "message": "Functions must use type hints for all parameters"}, {"name": "Return Type Hints Required", "description": "All functions must specify return types", "severity": "error", "pattern": {"regex": "def\\s+\\w+\\([^)]*\\):", "filePattern": "*.py", "notMatches": ["def __init__", "->"]}, "message": "Functions must specify return types"}, {"name": "No Exception Handling Outside Main", "description": "Exception handling should only occur in main() functions", "severity": "warning", "pattern": {"regex": "try:", "filePattern": "*.py", "notMatches": ["def main", "if __name__ == \"__main__\""]}, "message": "Exception handling should only occur in main() or entry points. Let exceptions bubble up."}]}, {"name": "Documentation Standards", "description": "Requirements for code documentation", "rules": [{"name": "File Docstrings Required", "description": "All Python files must have docstrings", "severity": "error", "pattern": {"regex": "import", "filePattern": "*.py", "notMatches": ["\"\"\""]}, "message": "Files must have descriptive docstrings including Oner, Pyramid Principle, and Requirements"}, {"name": "Numbered Step Comments", "description": "Use numbered comments for steps", "severity": "warning", "pattern": {"regex": "def\\s+\\w+", "filePattern": "*.py", "notMatches": ["# 1", "# 2", "# 3", "# One", "# Two"]}, "message": "Functions should use numbered comments to explain logical steps"}, {"name": "End-of-Line Justification Comments", "description": "Add EOL comments to justify implementation choices", "severity": "info", "pattern": {"regex": "\\S{40,}$", "filePattern": "*.py", "notMatches": ["#"]}, "message": "Consider adding EOL comments to explain why code is implemented this way"}]}, {"name": "Pydantic Usage Rules", "description": "Strict rules for using Pydantic models", "rules": [{"name": "Validate on Assignment", "description": "Pydantic models must validate on assignment", "severity": "error", "pattern": {"regex": "class\\s+\\w+\\(BaseModel\\):", "filePattern": "*.py", "notMatches": ["validate_assignment", "model_config"]}, "message": "Pydantic models must use 'validate_assignment': True"}, {"name": "Forbid Extra Keys", "description": "Pydantic models must forbid extra keys", "severity": "error", "pattern": {"regex": "class\\s+\\w+\\(BaseModel\\):", "filePattern": "*.py", "notMatches": ["extra=\"forbid\"", "\"extra\": \"forbid\""]}, "message": "Pydantic models must use 'extra=\"forbid\"'"}, {"name": "No Optional Fields in Intermediate Models", "description": "No Optional[] in intermediate models", "severity": "warning", "pattern": {"regex": "Optional\\[", "filePattern": "*/models/*.py", "notMatches": ["# System boundary", "# CLI boundary", "# HTTP boundary"]}, "message": "Optional fields are only allowed at system boundaries (CLI, HTTP)"}, {"name": "Schema Version Required", "description": "Pydantic models must have version", "severity": "warning", "pattern": {"regex": "class\\s+\\w+\\(BaseModel\\):", "filePattern": "*/models/*.py", "notMatches": ["__schema_version__"]}, "message": "Major schema classes should include __schema_version__ attribute"}, {"name": "Model Dump with mode=json", "description": "model_dump() should use mode=json or by_alias=True", "severity": "warning", "pattern": {"regex": "model_dump\\(\\)", "filePattern": "*.py"}, "message": "Use model_dump(mode=\"json\") or model_dump(by_alias=True) to reduce accidental type coercion"}, {"name": "Immutable Config Objects", "description": "Config objects should be immutable", "severity": "warning", "pattern": {"regex": "class\\s+\\w+Config\\(BaseModel\\):", "filePattern": "*.py", "notMatches": ["frozen=True", "\"frozen\": True"]}, "message": "Config objects should use frozen=True for immutability"}]}, {"name": "File Structure", "description": "Naming and organization conventions", "rules": [{"name": "Pipeline Script Naming", "description": "Pipeline scripts must follow naming convention", "severity": "warning", "pattern": {"regex": "\\.py$", "filePattern": "*/pipeline/*.py", "notMatches": ["^[a-z]{2}_"]}, "message": "Pipeline scripts should be named with prefix aa_cat_filename.py for alphabetical ordering"}, {"name": "Module Export Convention", "description": "Exported symbols should use __all__", "severity": "info", "pattern": {"regex": "def\\s+\\w+|class\\s+\\w+", "filePattern": "*.py", "notMatches": ["__all__", "__init__.py"]}, "message": "Define __all__ for symbols exported from this module"}]}, {"name": "Prompt Engineering Standards", "description": "Standards for prompt construction", "rules": [{"name": "Use Implicit Concatenation", "description": "Use parentheses for multiline prompts", "severity": "warning", "pattern": {"regex": "prompt\\s*=\\s*\"\"\"", "filePattern": "*/prompts/*.py"}, "message": "Use implicit concatenation with parentheses for multiline prompts: prompt = (\"line1\\n\" \"line2\\n\")"}, {"name": "Prompt Documentation Required", "description": "PromptObjects must have proper documentation", "severity": "warning", "pattern": {"regex": "class\\s+\\w+Prompt", "filePattern": "*/prompts/*.py", "notMatches": ["Oner:", "Pyramid"]}, "message": "PromptObjects must include Oner + Pyramid documentation"}]}, {"name": "Display Logic", "description": "Rules for UI/display code", "rules": [{"name": "Separate Display Logic", "description": "Display logic must be in separate modules", "severity": "warning", "pattern": {"regex": "rich|print\\(|format_table|colorama", "filePattern": "*/core/*.py"}, "message": "Display logic should be abstracted into util scripts, not in core modules"}, {"name": "Standard Display Colors", "description": "Use standard color conventions", "severity": "info", "pattern": {"regex": "print\\(", "filePattern": "*.py", "notMatches": ["rprint.dim", "rprint.yellow", "rprint.red", "rprint.green", "# Debug print"]}, "message": "Use _display_method utils with color conventions (dim=debug, white=info, yellow=note, red=fail/warning, green=success)"}]}, {"name": "Code Quality", "description": "General code quality rules", "rules": [{"name": "Maximum Function Length", "description": "Functions should be concise", "severity": "warning", "pattern": {"regex": "def\\s+\\w+[\\s\\S]{400,}?return", "filePattern": "*.py"}, "message": "Functions should be kept short and focused"}, {"name": "Maximum Model Length", "description": "Pydantic models should be concise", "severity": "warning", "pattern": {"regex": "class\\s+\\w+\\(BaseModel\\)[\\s\\S]{300,}?\\n\\s*$", "filePattern": "*.py"}, "message": "Pydantic models should be ≤ 25 lines"}, {"name": "DocTests Required", "description": "Models should have doctests", "severity": "info", "pattern": {"regex": "class\\s+\\w+\\(BaseModel\\)", "filePattern": "*/models/*.py", "notMatches": [">>>"]}, "message": "Pydantic models should include doctests showing happy path & failure cases"}]}, {"name": "Commit and PR Rules", "description": "Rules for commits and PRs", "rules": [{"name": "Remind to Commit", "description": "Remind to commit after making changes", "severity": "info", "pattern": {"anyFileModified": true, "filePattern": "*.py"}, "message": "Remember to commit after significant changes"}, {"name": "PR Review Time", "description": "PRs should be reviewed within 24 hours", "severity": "info", "pattern": {"regex": "# PR:", "filePattern": "*.py"}, "message": "PRs should be reviewed within 24 hours or escalated to COMPLAINER"}]}]}