#!/usr/bin/env python3
"""
Non-destructive conversion of txt files to JSON format.
Preserves all metadata and original structure.
"""

import json
import re
import os
import random
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path


def extract_step_by_step_sections(content: str) -> List[Dict[str, Any]]:
    """Extract sections from step_by_step_output.txt"""
    sections = []
    
    # Pattern to match section headers
    section_pattern = r'===== (.*?) ====='
    
    # Split content by sections
    parts = re.split(section_pattern, content)
    
    # Skip the header part
    if len(parts) > 1:
        parts = parts[1:]  # Remove everything before first section
    
    # Process pairs of (section_name, section_content)
    for i in range(0, len(parts), 2):
        if i + 1 < len(parts):
            section_name = parts[i].strip()
            section_content = parts[i + 1].strip()
            
            # Extract metadata
            timestamp_match = re.search(r'Timestamp: ([\d.]+)', section_content)
            type_match = re.search(r'Output Type: (\w+)', section_content)
            size_match = re.search(r'Output Size: (\d+)', section_content)
            
            # Extract data after "Output Data:"
            data_match = re.search(r'Output Data:\s*\n(.+?)(?:={50}|$)', section_content, re.DOTALL)
            
            if data_match:
                data_str = data_match.group(1).strip()
                
                # Parse JSON data
                try:
                    if data_str.startswith('"') and data_str.endswith('"'):
                        # It's a JSON string
                        data = json.loads(data_str)
                    elif data_str.startswith('{'):
                        # It's a JSON object
                        data = json.loads(data_str)
                    else:
                        # Fallback to raw string
                        data = data_str
                except json.JSONDecodeError:
                    print(f"Warning: Could not parse JSON for section '{section_name}', keeping as string")
                    data = data_str
                
                section_dict = {
                    "name": section_name,
                    "data": data
                }
                
                # Add metadata if available
                if timestamp_match:
                    section_dict["timestamp"] = float(timestamp_match.group(1))
                if type_match:
                    section_dict["output_type"] = type_match.group(1)
                if size_match:
                    section_dict["output_size"] = int(size_match.group(1))
                
                sections.append(section_dict)
    
    return sections


def extract_results_sections(content: str) -> Dict[str, Any]:
    """Extract sections from results.txt"""
    result = {}
    
    # Pattern to match section headers
    section_pattern = r'===== (.*?) ====='
    
    # Find all sections
    sections = re.split(section_pattern, content)
    
    # Process pairs of (section_name, section_content)
    for i in range(1, len(sections), 2):
        if i + 1 < len(sections):
            section_name = sections[i].strip()
            section_content = sections[i + 1].strip()
            
            # Convert section name to snake_case key
            key = section_name.lower().replace(' ', '_')
            
            # Parse JSON content
            try:
                if section_content.startswith('"') and section_content.endswith('"'):
                    # It's a JSON string
                    data = json.loads(section_content)
                elif section_content.startswith('{'):
                    # It's a JSON object
                    data = json.loads(section_content)
                else:
                    # Fallback to raw string
                    data = section_content
            except json.JSONDecodeError:
                print(f"Warning: Could not parse JSON for section '{section_name}', keeping as string")
                data = section_content
            
            result[key] = data
    
    return result


def verify_conversion(original_file: str, json_data: Any, num_samples: int = 30) -> List[Tuple[str, bool, str]]:
    """Verify that conversion preserved data by sampling and regex matching"""
    with open(original_file, 'r') as f:
        original_content = f.read()
    
    verification_results = []
    
    # Flatten JSON to get testable strings
    test_strings = []
    
    def extract_strings(obj, path=""):
        if isinstance(obj, dict):
            for k, v in obj.items():
                extract_strings(v, f"{path}.{k}" if path else k)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                extract_strings(item, f"{path}[{i}]")
        elif isinstance(obj, str) and len(obj) > 10:  # Only test meaningful strings
            test_strings.append((path, obj))
        elif isinstance(obj, (int, float)):
            test_strings.append((path, str(obj)))
    
    extract_strings(json_data)
    
    # Sample strings to test
    if len(test_strings) > num_samples:
        sampled = random.sample(test_strings, num_samples)
    else:
        sampled = test_strings
    
    # Test each sampled string
    for path, value in sampled:
        # Escape special regex characters
        escaped_value = re.escape(str(value)[:100])  # Test first 100 chars
        
        # Check if value exists in original
        if re.search(escaped_value, original_content):
            verification_results.append((path, True, "Found in original"))
        else:
            # Try a more lenient search (ignore whitespace differences)
            lenient_pattern = re.escape(str(value)[:50]).replace(r'\ ', r'\s+')
            if re.search(lenient_pattern, original_content):
                verification_results.append((path, True, "Found with whitespace differences"))
            else:
                verification_results.append((path, False, f"NOT FOUND: {value[:50]}..."))
    
    return verification_results


def main():
    """Main conversion function"""
    base_dir = Path("/Users/<USER>/Code3b/Github/pfc")
    
    # Convert step_by_step_output.txt
    step_file = base_dir / "step_by_step_output.txt"
    if step_file.exists():
        print("Converting step_by_step_output.txt...")
        with open(step_file, 'r') as f:
            content = f.read()
        
        sections = extract_step_by_step_sections(content)
        step_json = {
            "title": "FULL FLOW STEP-BY-STEP OUTPUT",
            "sections": sections
        }
        
        # Write JSON
        output_file = base_dir / "step_by_step.json"
        with open(output_file, 'w') as f:
            json.dump(step_json, f, indent=2)
        print(f"✓ Created {output_file}")
        
        # Verify
        print("\nVerifying step_by_step conversion...")
        results = verify_conversion(step_file, step_json)
        passed = sum(1 for _, success, _ in results if success)
        print(f"Verification: {passed}/{len(results)} samples found in original")
        
        # Show failed samples
        failed = [(path, msg) for path, success, msg in results if not success]
        if failed:
            print("Failed samples:")
            for path, msg in failed[:5]:  # Show first 5 failures
                print(f"  - {path}: {msg}")
    
    # Convert results.txt
    results_file = base_dir / "results.txt"
    if results_file.exists():
        print("\nConverting results.txt...")
        with open(results_file, 'r') as f:
            content = f.read()
        
        results_json = extract_results_sections(content)
        
        # Write JSON
        output_file = base_dir / "step_results.json"
        with open(output_file, 'w') as f:
            json.dump(results_json, f, indent=2)
        print(f"✓ Created {output_file}")
        
        # Verify
        print("\nVerifying results conversion...")
        results = verify_conversion(results_file, results_json)
        passed = sum(1 for _, success, _ in results if success)
        print(f"Verification: {passed}/{len(results)} samples found in original")
        
        # Show failed samples
        failed = [(path, msg) for path, success, msg in results if not success]
        if failed:
            print("Failed samples:")
            for path, msg in failed[:5]:  # Show first 5 failures
                print(f"  - {path}: {msg}")
    
    # Create combined output
    if (base_dir / "step_by_step.json").exists() and (base_dir / "step_results.json").exists():
        print("\nCreating combined output...")
        with open(base_dir / "step_by_step.json", 'r') as f:
            step_data = json.load(f)
        with open(base_dir / "step_results.json", 'r') as f:
            results_data = json.load(f)
        
        combined = {
            "step_by_step": step_data,
            "results": results_data
        }
        
        output_file = base_dir / "step_combined.json"
        with open(output_file, 'w') as f:
            json.dump(combined, f, indent=2)
        print(f"✓ Created {output_file}")
    
    print("\n✓ Conversion complete! Original files remain unchanged.")


if __name__ == "__main__":
    main()