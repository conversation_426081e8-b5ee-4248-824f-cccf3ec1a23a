"""
FastAPI wrapper for the Prompt Generator system.
Provides REST API endpoints for prompt generation, workflow recommendations, and analytics.
"""

import logging
# Configure logging to show logs from underlying modules
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uvicorn
import asyncio
import time
import json
from datetime import datetime

# Import the prompt generator components
from prompt_generator.enhanced_orchestrator_v2 import (
    run_requirements_aware_prompt_generation,
    get_workflow_recommendation,
    generate_analytics_report
)
from prompt_generator.enhanced_orchestrator import WorkflowType

app = FastAPI(
    title="Prompt Generator API",
    description="AI-powered prompt engineering system with multi-agent orchestration",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class PromptGenerationRequest(BaseModel):
    task: str
    target_score: float = 8.5
    max_turns: int = 12
    workflow_type: Optional[str] = None
    domain: Optional[str] = None

class PromptGenerationResponse(BaseModel):
    prompt: str
    input_type: str
    execution_time: float
    turn_count: int
    requirements_context: Optional[Dict[str, Any]] = None
    domain_context: Optional[Dict[str, Any]] = None
    quality_context: Optional[Dict[str, Any]] = None
    workflow_type: str
    final_score: float
    output_file: str

class WorkflowRecommendationRequest(BaseModel):
    task: str
    domain: Optional[str] = None
    complexity: Optional[str] = None

class WorkflowRecommendationResponse(BaseModel):
    recommended_workflow: str
    reasoning: str
    confidence: float
    alternative_workflows: List[str]

class AnalyticsRequest(BaseModel):
    file_path: str

class AnalyticsResponse(BaseModel):
    report: Dict[str, Any]
    insights: List[str]
    recommendations: List[str]

def save_api_output(result: Dict[str, Any], execution_time: float, request_data: Dict[str, Any]) -> str:
    """Save API output to a single file that gets rewritten each run."""
    
    # Create comprehensive output structure
    api_output = {
        "metadata": {
            "api_name": "Prompt Generator API",
            "generated_at": datetime.now().isoformat(),
            "execution_time_seconds": execution_time,
            "api_version": "1.0.0",
            "request_data": request_data
        },
        "result": result,
        "summary": {
            "prompt_generated": True,
            "final_score": result.get("final_score", 0.0),
            "turn_count": result.get("turn_count", 0),
            "status": "completed"
        }
    }
    
    # Save to single file (overwrites each run)
    filename = "prompt_output.json"
    
    with open(filename, "w") as f:
        json.dump(api_output, f, indent=2)
    
    print(f"[API] Output saved to: {filename}")
    return filename

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "service": "Prompt Generator API",
        "status": "healthy",
        "version": "1.0.0",
        "endpoints": [
            "/generate-prompt",
            "/workflow-recommendation", 
            "/analytics",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "prompt_generator",
        "version": "1.0.0"
    }

@app.post("/generate-prompt", response_model=PromptGenerationResponse)
async def generate_prompt(request: PromptGenerationRequest):
    """Generate a prompt using the multi-agent system."""
    try:
        start_time = time.time()
        
        # Run the prompt generation
        result = run_requirements_aware_prompt_generation(
            input_data=request.task,
            target_score=request.target_score,
            max_turns=request.max_turns
        )
        
        execution_time = time.time() - start_time
        
        # Prepare response data
        response_data = {
            "prompt": result.final_prompt,
            "input_type": result.input_type.value,
            "execution_time": execution_time,
            "turn_count": len(result.history),
            "requirements_context": result.requirements_context,
            "domain_context": result.domain_context,
            "quality_context": result.quality_context,
            "workflow_type": result.workflow_type.value if result.workflow_type else "standard",
            "final_score": getattr(result, 'final_score', 8.0)
        }
        
        # Save output to file
        output_file = save_api_output(response_data, execution_time, request.dict())
        
        return PromptGenerationResponse(
            prompt=result.final_prompt,
            input_type=result.input_type.value,
            execution_time=execution_time,
            turn_count=len(result.history),
            requirements_context=result.requirements_context,
            domain_context=result.domain_context,
            quality_context=result.quality_context,
            workflow_type=result.workflow_type.value if result.workflow_type else "standard",
            final_score=getattr(result, 'final_score', 8.0),
            output_file=output_file
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prompt generation failed: {str(e)}")

@app.post("/workflow-recommendation", response_model=WorkflowRecommendationResponse)
async def get_workflow_recommendation_endpoint(request: WorkflowRecommendationRequest):
    """Get workflow recommendation for a task."""
    try:
        recommendation = get_workflow_recommendation(
            task=request.task,
            domain=request.domain,
            complexity=request.complexity
        )
        
        return WorkflowRecommendationResponse(
            recommended_workflow=recommendation.get("recommended_workflow", "standard"),
            reasoning=recommendation.get("reasoning", ""),
            confidence=recommendation.get("confidence", 0.8),
            alternative_workflows=recommendation.get("alternative_workflows", [])
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Workflow recommendation failed: {str(e)}")

@app.post("/analytics", response_model=AnalyticsResponse)
async def generate_analytics(request: AnalyticsRequest):
    """Generate analytics report from saved data."""
    try:
        report = generate_analytics_report(request.file_path)
        
        return AnalyticsResponse(
            report=report.get("report", {}),
            insights=report.get("insights", []),
            recommendations=report.get("recommendations", [])
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analytics generation failed: {str(e)}")

@app.get("/workflows")
async def list_workflows():
    """List available workflow types."""
    return {
        "workflows": [
            {"name": "standard", "description": "Balanced workflow with quality and performance"},
            {"name": "domain_specific", "description": "Domain-optimized workflow with specialized roles"},
            {"name": "quality_focused", "description": "Quality-first workflow with comprehensive testing"},
            {"name": "performance_optimized", "description": "Speed-focused workflow with minimal iterations"},
            {"name": "compliance_critical", "description": "Compliance-first workflow with extensive validation"}
        ]
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001) 