"""
Data models for Test Case Chunker Module
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

@dataclass
class PlaceholderValue:
    """Represents a placeholder and its generated value"""
    placeholder: str
    value: str
    confidence: float = 1.0
    reasoning: Optional[str] = None

@dataclass
class TestCase:
    """Represents a single test case with seed data"""
    seed_id: str
    input: str
    expected_output: str
    reasoning: str
    category: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ChunkedTestCase:
    """Represents a processed test case ready for execution"""
    seed_id: str
    system_message: str
    user_message: str
    placeholders: Dict[str, str]
    seed_data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "seed_id": self.seed_id,
            "system_message": self.system_message,
            "user_message": self.user_message,
            "placeholders": self.placeholders,
            "seed_data": self.seed_data,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2)

@dataclass
class ChunkedTestCases:
    """Container for multiple chunked test cases"""
    test_cases: List[ChunkedTestCase] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_test_case(self, test_case: ChunkedTestCase):
        """Add a test case to the collection"""
        self.test_cases.append(test_case)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "test_cases": [tc.to_dict() for tc in self.test_cases],
            "metadata": self.metadata,
            "total_count": len(self.test_cases)
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2)
    
    def save_to_file(self, filepath: str):
        """Save to JSON file"""
        with open(filepath, 'w') as f:
            f.write(self.to_json()) 