"""
PlaceholderGenerator role for generating placeholder values from seed data
"""

import json
import re
from typing import Dict, List, Any
from .base import BaseRole

class PlaceholderGenerator(BaseRole):
    """Generates appropriate values for placeholders based on seed data"""
    
    async def process(self, seed_data: Dict[str, Any], placeholders: List[str], 
                     system_message: str, user_message: str) -> Dict[str, str]:
        """Generate values for placeholders based on seed data"""
        
        prompt = self._build_prompt(seed_data, placeholders, system_message, user_message)
        
        try:
            response = await self.call_llm(prompt)
            return self._parse_response(response, placeholders)
        except Exception as e:
            print(f"[ERROR] PlaceholderGenerator failed: {e}")
            return self._generate_fallback_values(placeholders, seed_data)
    
    def _build_prompt(self, seed_data: Dict[str, Any], placeholders: List[str], 
                      system_message: str, user_message: str) -> str:
        """Build prompt for placeholder generation"""
        
        return f"""You are a PlaceholderGenerator. Your task is to generate appropriate values for placeholders based on the provided seed data.

SEED DATA:
{json.dumps(seed_data, indent=2)}

PLACEHOLDERS TO FILL:
{json.dumps(placeholders, indent=2)}

SYSTEM MESSAGE:
{system_message}

USER MESSAGE:
{user_message}

INSTRUCTIONS:
1. Analyze the seed data and understand the context
2. Generate appropriate values for each placeholder that make sense in the context
3. Ensure the values are realistic and relevant to the seed data
4. Return a JSON object with placeholder as key and generated value as string

EXAMPLE OUTPUT:
{{
  "{{IndustryList}}": "technology, healthcare, finance",
  "{{LearningObjectives}}": "industry-specific skills, regulatory compliance, best practices",
  "{{UserProfile}}": "mid-level professionals seeking career advancement"
}}

Generate the placeholder values:"""
    
    def _parse_response(self, response: str, placeholders: List[str]) -> Dict[str, str]:
        """Parse LLM response to extract placeholder values"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                # Validate that all placeholders are present
                for placeholder in placeholders:
                    if placeholder not in result:
                        result[placeholder] = f"Generated value for {placeholder}"
                return result
            else:
                return self._generate_fallback_values(placeholders, {})
        except Exception as e:
            print(f"[WARNING] Failed to parse response: {e}")
            return self._generate_fallback_values(placeholders, {})
    
    def _generate_fallback_values(self, placeholders: List[str], seed_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate fallback values if LLM parsing fails"""
        fallback_values = {}
        for placeholder in placeholders:
            # Extract placeholder name without {{}}
            name = placeholder.strip('{}')
            if 'industry' in name.lower():
                fallback_values[placeholder] = "technology, healthcare, finance"
            elif 'learning' in name.lower() or 'objective' in name.lower():
                fallback_values[placeholder] = "skill development, knowledge acquisition, practical application"
            elif 'user' in name.lower() or 'profile' in name.lower():
                fallback_values[placeholder] = "professional users seeking industry knowledge"
            else:
                fallback_values[placeholder] = f"Default value for {name}"
        
        return fallback_values 