"""
TestCaseValidator role for validating generated test cases
"""

import json
from typing import Dict, Any, List
from .base import BaseRole

class TestCaseValidator(BaseRole):
    """Validates generated test cases and placeholder values"""
    
    async def process(self, test_case: Dict[str, Any], original_seed: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a test case and return validation results"""
        
        prompt = self._build_validation_prompt(test_case, original_seed)
        
        try:
            response = await self.call_llm(prompt)
            validation_result = self._parse_validation_response(response)
            return {
                "is_valid": validation_result.get("is_valid", False),
                "issues": validation_result.get("issues", []),
                "suggestions": validation_result.get("suggestions", []),
                "confidence": validation_result.get("confidence", 0.0)
            }
        except Exception as e:
            print(f"[ERROR] TestCaseValidator failed: {e}")
            return {
                "is_valid": True,  # Default to valid if validation fails
                "issues": [],
                "suggestions": [],
                "confidence": 0.5
            }
    
    def _build_validation_prompt(self, test_case: Dict[str, Any], original_seed: Dict[str, Any]) -> str:
        """Build validation prompt"""
        
        return f"""You are a TestCaseValidator. Your task is to validate a generated test case against the original seed data.

ORIGINAL SEED:
{json.dumps(original_seed, indent=2)}

GENERATED TEST CASE:
{json.dumps(test_case, indent=2)}

VALIDATION CRITERIA:
1. Placeholder values should be relevant to the seed data
2. System and user messages should remain intact
3. Placeholder values should be realistic and appropriate
4. The test case should be executable
5. Values should match the expected format and context

Return a JSON response with:
{{
  "is_valid": true/false,
  "issues": ["list of issues found"],
  "suggestions": ["list of improvement suggestions"],
  "confidence": 0.0-1.0
}}

Validate the test case:"""
    
    def _parse_validation_response(self, response: str) -> Dict[str, Any]:
        """Parse validation response"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return {
                    "is_valid": True,
                    "issues": [],
                    "suggestions": [],
                    "confidence": 0.5
                }
        except Exception as e:
            print(f"[WARNING] Failed to parse validation response: {e}")
            return {
                "is_valid": True,
                "issues": [],
                "suggestions": [],
                "confidence": 0.5
            } 