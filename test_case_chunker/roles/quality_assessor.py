"""
QualityAssessor role for assessing overall quality of generated test cases
"""

import json
from typing import Dict, Any, List
from .base import BaseRole

class QualityAssessor(BaseRole):
    """Assesses overall quality of generated test cases"""
    
    async def process(self, test_cases: List[Dict[str, Any]], original_seeds: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess quality of all test cases"""
        
        prompt = self._build_assessment_prompt(test_cases, original_seeds)
        
        try:
            response = await self.call_llm(prompt)
            assessment_result = self._parse_assessment_response(response)
            return assessment_result
        except Exception as e:
            print(f"[ERROR] QualityAssessor failed: {e}")
            return {
                "overall_quality_score": 0.5,
                "coverage_score": 0.5,
                "diversity_score": 0.5,
                "executability_score": 0.5,
                "recommendations": ["Assessment failed, using default values"]
            }
    
    def _build_assessment_prompt(self, test_cases: List[Dict[str, Any]], original_seeds: List[Dict[str, Any]]) -> str:
        """Build quality assessment prompt"""
        
        return f"""You are a QualityAssessor. Your task is to assess the overall quality of generated test cases.

ORIGINAL SEEDS COUNT: {len(original_seeds)}
GENERATED TEST CASES COUNT: {len(test_cases)}

SAMPLE ORIGINAL SEEDS:
{json.dumps(original_seeds[:3] if isinstance(original_seeds, list) else list(original_seeds.values())[:3], indent=2)}

SAMPLE GENERATED TEST CASES:
{json.dumps(test_cases[:3], indent=2)}

QUALITY ASSESSMENT CRITERIA:
1. Coverage: How well do the test cases cover different scenarios from the seeds?
2. Diversity: How diverse are the placeholder values and test scenarios?
3. Executability: Are the test cases ready for immediate execution?
4. Relevance: Do the placeholder values match the seed context?
5. Completeness: Are all placeholders properly filled?

Return a JSON response with:
{{
  "overall_quality_score": 0.0-1.0,
  "coverage_score": 0.0-1.0,
  "diversity_score": 0.0-1.0,
  "executability_score": 0.0-1.0,
  "relevance_score": 0.0-1.0,
  "completeness_score": 0.0-1.0,
  "recommendations": ["list of improvement suggestions"]
}}

Assess the quality:"""
    
    def _parse_assessment_response(self, response: str) -> Dict[str, Any]:
        """Parse assessment response"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return {
                    "overall_quality_score": 0.5,
                    "coverage_score": 0.5,
                    "diversity_score": 0.5,
                    "executability_score": 0.5,
                    "relevance_score": 0.5,
                    "completeness_score": 0.5,
                    "recommendations": ["Failed to parse assessment response"]
                }
        except Exception as e:
            print(f"[WARNING] Failed to parse assessment response: {e}")
            return {
                "overall_quality_score": 0.5,
                "coverage_score": 0.5,
                "diversity_score": 0.5,
                "executability_score": 0.5,
                "relevance_score": 0.5,
                "completeness_score": 0.5,
                "recommendations": ["Assessment parsing failed"]
            } 