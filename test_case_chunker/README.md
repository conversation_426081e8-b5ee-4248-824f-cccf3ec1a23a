# Test Case Chunker

The Test Case Chunker module processes synthetic data seeds and generated prompts to create executable test cases with placeholder values ready for execution.

## Overview

This module takes the output from the Synthetic Data Generator and the Prompt Generator to create ready-to-execute test cases. For each seed, it makes an LLM call to generate appropriate values for the placeholders in the prompt, creating structured test cases that can be immediately used for testing.

## Features

- **LLM-based placeholder generation**: Each seed gets processed through an LLM to generate contextually appropriate placeholder values
- **Validation**: Generated test cases are validated for quality and relevance
- **Quality assessment**: Overall quality metrics for the generated test cases
- **Structured output**: JSON format ready for parsing and execution
- **Configurable**: YAML-based configuration for all components

## Usage

### CLI Usage

```bash
python -m "Test Case Chunker.cli" --seeds synthetic_data.json --prompt generated_prompt.json --output chunked_test_cases.json
```

### Programmatic Usage

```python
from "Test Case Chunker" import TestCaseChunker

# Initialize chunker
chunker = TestCaseChunker()

# Process seeds and prompt
chunked_cases = await chunker.chunk_test_cases(seeds, prompt_json)

# Save results
await chunker.save_chunked_cases(chunked_cases, "output.json")

# Get statistics
stats = chunker.get_statistics(chunked_cases)
```

## Input Format

### Seeds Input
```json
{
  "seeds": [
    {
      "input": "test input",
      "expected_output": "expected output",
      "reasoning": "test reasoning",
      "category": "edge_cases",
      "metadata": {...}
    }
  ]
}
```

### Prompt Input
```json
{
  "json_prompt": {
    "system_message": "You are a prompt engineer...",
    "user_message": "Design a platform with {{IndustryList}} and {{LearningObjectives}}...",
    "metadata": {
      "placeholders": ["{{IndustryList}}", "{{LearningObjectives}}"]
    }
  }
}
```

## Output Format

```json
{
  "test_cases": [
    {
      "seed_id": "seed_1",
      "system_message": "You are a prompt engineer...",
      "user_message": "Design a platform with {{IndustryList}} and {{LearningObjectives}}...",
      "placeholders": {
        "{{IndustryList}}": "technology, healthcare, finance",
        "{{LearningObjectives}}": "industry-specific skills, regulatory compliance"
      },
      "seed_data": {...},
      "metadata": {
        "validation": {
          "is_valid": true,
          "confidence": 0.9
        }
      }
    }
  ],
  "metadata": {
    "quality_assessment": {
      "overall_quality_score": 0.85,
      "coverage_score": 0.9,
      "diversity_score": 0.8
    }
  },
  "total_count": 10
}
```

## Configuration

The module uses a YAML configuration file (`config/config.yaml`) with the following structure:

```yaml
llm:
  model: "gpt-4o-mini"
  temperature: 1.0
  max_tokens: 2000

roles:
  PlaceholderGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 1500
    description: "Generates appropriate values for placeholders based on seed data"
  
  TestCaseValidator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 1000
    description: "Validates generated test cases and placeholder values"
  
  QualityAssessor:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 1000
    description: "Assesses quality of generated test cases"

processing:
  batch_size: 5
  rate_limit_delay: 1.0
  max_retries: 3
  timeout: 30

output:
  format: "json"
  include_seed_data: true
  include_metadata: true
```

## Architecture

The module consists of three main roles:

1. **PlaceholderGenerator**: Generates appropriate values for placeholders based on seed data
2. **TestCaseValidator**: Validates generated test cases for quality and relevance
3. **QualityAssessor**: Assesses overall quality of all generated test cases

Each role makes separate LLM calls to ensure high-quality, contextually appropriate outputs.

## Integration

This module is designed to integrate seamlessly with the existing pipeline:

1. **Synthetic Data Generator** → produces seeds
2. **Prompt Generator** → produces prompt JSON
3. **Test Case Chunker** → processes both into executable test cases
4. **Next Step** → executes the test cases against the generated prompt 