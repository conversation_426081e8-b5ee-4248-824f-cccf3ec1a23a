# Test Case Chunker Configuration

llm:
  model: "gpt-4o-mini"
  temperature: 1.0
  max_tokens: 2000

roles:
  PlaceholderGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 1500
    description: "Generates appropriate values for placeholders based on seed data"
  
  TestCaseValidator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 1000
    description: "Validates generated test cases and placeholder values"
  
  QualityAssessor:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 1000
    description: "Assesses quality of generated test cases"

processing:
  batch_size: 5
  rate_limit_delay: 1.0
  max_retries: 3
  timeout: 30

output:
  format: "json"
  include_seed_data: true
  include_metadata: true 