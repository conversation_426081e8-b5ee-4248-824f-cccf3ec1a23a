"""
CLI interface for Test Case Chunker
"""

import asyncio
import json
import argparse
from pathlib import Path
from typing import Dict, Any

from .core import TestCaseChunker

async def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(description="Test Case Chunker - Process seeds and prompts into executable test cases")
    parser.add_argument("--seeds", required=True, help="Path to JSON file containing synthetic data seeds")
    parser.add_argument("--prompt", required=True, help="Path to JSON file containing generated prompt")
    parser.add_argument("--output", default="chunked_test_cases.json", help="Output file path")
    parser.add_argument("--config", help="Path to config file")
    
    args = parser.parse_args()
    
    try:
        # Load seeds
        print(f"[CLI] Loading seeds from {args.seeds}")
        with open(args.seeds, 'r') as f:
            seeds_data = json.load(f)
        
        # Extract seeds from synthetic data output
        seeds = seeds_data.get("seeds", [])
        if not seeds:
            print("[ERROR] No seeds found in the input file")
            return
        
        print(f"[CLI] Found {len(seeds)} seeds")
        
        # Load prompt
        print(f"[CLI] Loading prompt from {args.prompt}")
        with open(args.prompt, 'r') as f:
            prompt_data = json.load(f)
        
        # Initialize chunker
        chunker = TestCaseChunker(args.config)
        
        # Process test cases
        print("[CLI] Starting test case chunking...")
        chunked_cases = await chunker.chunk_test_cases(seeds, prompt_data)
        
        # Save results
        await chunker.save_chunked_cases(chunked_cases, args.output)
        
        # Print statistics
        stats = chunker.get_statistics(chunked_cases)
        print(f"\n[CLI] Processing complete!")
        print(f"Total cases: {stats['total_cases']}")
        print(f"Valid cases: {stats['valid_cases']}")
        print(f"Success rate: {stats['success_rate']:.2%}")
        print(f"Average placeholders per case: {stats['avg_placeholders_per_case']:.1f}")
        
        if chunked_cases.metadata.get("quality_assessment"):
            quality = chunked_cases.metadata["quality_assessment"]
            print(f"Overall quality score: {quality.get('overall_quality_score', 0):.2f}")
        
    except Exception as e:
        print(f"[ERROR] CLI execution failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main()) 