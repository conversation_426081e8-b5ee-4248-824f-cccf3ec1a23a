"""
Core Test Case Chunker module
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from pathlib import Path

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))
from config import Config
from models import ChunkedTestCase, ChunkedTestCases
from roles import PlaceholderGenerator, TestCaseValidator, QualityAssessor

class TestCaseChunker:
    """Main class for chunking test cases from synthetic data seeds"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = Config(config_path)
        self.placeholder_generator = PlaceholderGenerator(
            self.config.get_role_config("PlaceholderGenerator")
        )
        self.test_case_validator = TestCaseValidator(
            self.config.get_role_config("TestCaseValidator")
        )
        self.quality_assessor = QualityAssessor(
            self.config.get_role_config("QualityAssessor")
        )
    
    async def chunk_test_cases(self, 
                              seeds: List[Dict[str, Any]], 
                              prompt_json: Dict[str, Any]) -> ChunkedTestCases:
        """Process seeds and prompt to create chunked test cases"""
        
        print(f"[CHUNKER] Processing {len(seeds)} seeds with prompt")
        
        # Extract prompt components
        system_message = prompt_json.get("system_message", "")
        user_message = prompt_json.get("user_message", "")
        placeholders = prompt_json.get("metadata", {}).get("placeholders", [])
        
        if not placeholders:
            print("[WARNING] No placeholders found in prompt JSON")
            return ChunkedTestCases()
        
        print(f"[CHUNKER] Found {len(placeholders)} placeholders: {placeholders}")
        
        # Process seeds in parallel
        chunked_cases = ChunkedTestCases()
        
        async def process_single_seed(seed, seed_index):
            """Process a single seed with placeholder generation and validation"""
            try:
                print(f"[CHUNKER] Processing seed {seed_index+1}/{len(seeds)}")
                
                # Generate placeholder values for this seed
                placeholder_values = await self.placeholder_generator.process(
                    seed_data=seed,
                    placeholders=placeholders,
                    system_message=system_message,
                    user_message=user_message
                )
                
                # Create chunked test case
                chunked_case = ChunkedTestCase(
                    seed_id=f"seed_{seed_index+1}",
                    system_message=system_message,
                    user_message=user_message,
                    placeholders=placeholder_values,
                    seed_data=seed,
                    metadata={
                        "original_seed_index": seed_index,
                        "placeholders_count": len(placeholders),
                        "generated_values_count": len(placeholder_values)
                    }
                )
                
                # Validate the test case
                validation_result = await self.test_case_validator.process(
                    test_case=chunked_case.to_dict(),
                    original_seed=seed
                )
                
                # Add validation info to metadata
                chunked_case.metadata.update({
                    "validation": validation_result
                })
                
                print(f"[CHUNKER] Successfully processed seed {seed_index+1}")
                return chunked_case
                
            except Exception as e:
                print(f"[ERROR] Failed to process seed {seed_index+1}: {e}")
                return None
        
        # Create tasks for all seeds
        tasks = [process_single_seed(seed, i) for i, seed in enumerate(seeds)]
        
        # Run all tasks concurrently
        print(f"[CHUNKER] Starting parallel processing of {len(seeds)} seeds...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect successful results
        for result in results:
            if result is not None and not isinstance(result, Exception):
                chunked_cases.add_test_case(result)
        
        # Assess overall quality
        if chunked_cases.test_cases:
            quality_assessment = await self.quality_assessor.process(
                test_cases=[tc.to_dict() for tc in chunked_cases.test_cases],
                original_seeds=seeds
            )
            chunked_cases.metadata.update({
                "quality_assessment": quality_assessment
            })
        
        print(f"[CHUNKER] Completed processing. Generated {len(chunked_cases.test_cases)} test cases")
        return chunked_cases
    
    async def save_chunked_cases(self, chunked_cases: ChunkedTestCases, output_path: str):
        """Save chunked test cases to file"""
        try:
            chunked_cases.save_to_file(output_path)
            print(f"[CHUNKER] Saved {len(chunked_cases.test_cases)} test cases to {output_path}")
        except Exception as e:
            print(f"[ERROR] Failed to save chunked cases: {e}")
    
    def get_statistics(self, chunked_cases: ChunkedTestCases) -> Dict[str, Any]:
        """Get statistics about the chunked test cases"""
        if not chunked_cases.test_cases:
            return {"total_cases": 0}
        
        total_cases = len(chunked_cases.test_cases)
        total_placeholders = sum(len(tc.placeholders) for tc in chunked_cases.test_cases)
        avg_placeholders = total_placeholders / total_cases if total_cases > 0 else 0
        
        # Count validation results
        valid_cases = sum(1 for tc in chunked_cases.test_cases 
                         if tc.metadata.get("validation", {}).get("is_valid", True))
        
        return {
            "total_cases": total_cases,
            "valid_cases": valid_cases,
            "invalid_cases": total_cases - valid_cases,
            "total_placeholders": total_placeholders,
            "avg_placeholders_per_case": avg_placeholders,
            "success_rate": valid_cases / total_cases if total_cases > 0 else 0
        } 