"""Configuration for Test Executor Module
"""

from pathlib import Path
from typing import Any, Dict, Optional

import yaml


class Config:
    """Configuration manager for Test Executor"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or Path(__file__).parent / "config" / "config.yaml"
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file. Raise error if not found or invalid."""
        try:
            with open(self.config_path) as f:
                config = yaml.safe_load(f)
            if not config:
                raise ValueError(f"Config file {self.config_path} is empty or invalid.")
            print(f"[CONFIG] Loaded config from: {self.config_path}")
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"[CONFIG] Config file not found: {self.config_path}")
        except Exception as e:
            raise RuntimeError(f"[CONFIG] Error loading config: {e}")

    def get_role_config(self, role_name: str) -> Dict[str, Any]:
        """Get configuration for a specific role"""
        return self.config.get("roles", {}).get(role_name, {})

    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration"""
        return self.config.get("llm", {})

    def get_processing_config(self) -> Dict[str, Any]:
        """Get processing configuration"""
        return self.config.get("processing", {})
