# Test Executor Configuration

llm:
  model: "gpt-4o-mini"
  temperature: 1.0
  max_tokens: 10000

roles:
  FeedbackGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Generates feedback for the test results"
  
  ResultComparator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Compares test results and returns feedback"
  
  TestRunner:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Executes test cases"
  
  RequirementSummarizer:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Summarizes requirements"
  
  ValidationQuestionsGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Generates validation questions"
  
  PromptQuestionExecutor:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Executes prompt questions"

  FeedbackGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 10000
    description: "Generates feedback for the test results"

  PromptRefiner:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 15000
    description: "Refines prompt according to feedback provided"

  RequirementsEditor:
    model: "gpt-4o-mini"
    temperature: 1.0
    max_tokens: 15000
    description: "Edits requirements according to feedback provided"

processing:
  batch_size: 5
  rate_limit_delay: 1.0
  max_retries: 3
  timeout: 30

output:
  format: "json"
  include_seed_data: true
  include_metadata: true 