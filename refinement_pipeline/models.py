"""Data models for Test Executor Module."""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any


@dataclass
class TestResult:
    """Represents a single test result."""

    seed_id: str
    system_message: str
    user_message: str
    expected_output: str
    actual_output: str
    status: str
    comparison_result: dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    metadata: dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "seed_id": self.seed_id,
            "system_message": self.system_message,
            "user_message": self.user_message,
            "expected_output": self.expected_output,
            "actual_output": self.actual_output,
            "status": self.status,
            "comparison_result": self.comparison_result,
            "created_at": self.created_at.isoformat(),
            "metadata": self.metadata,
        }

@dataclass
class PromptFeedback:
    """Represents the prompt feedback after test execution."""

    system_message: str
    user_message: str
    test_results: list[TestResult]
    issues_present: bool = False
    critical_issues: dict[str, str] = field(default_factory=dict)
    changes_suggested: dict[str, list[dict[str, str]]] = field(default_factory=dict)
    changes_blacklisted: list[dict[str, str]] = field(default_factory=list)
    test_result_analysis: dict[str, list[str]] = field(default_factory=dict)
    implementation_priority: list[dict[str, str]] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    metadata: dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "system_message": self.system_message,
            "user_message": self.user_message,
            "test_results": [test_result.to_dict() for test_result in self.test_results],
            "issues_present": self.issues_present,
            "critical_issues": self.critical_issues,
            "changes_suggested": self.changes_suggested,
            "changes_blacklisted": self.changes_blacklisted,
            "test_result_analysis": self.test_result_analysis,
            "implementation_priority": self.implementation_priority,
            "created_at": self.created_at.isoformat(),
            "metadata": self.metadata,
        }

    def prepare_for_refinement(self) -> dict[str, Any]:
        """Prepare the prompt feedback for refinement."""
        return {
            "critical_issues": self.critical_issues,
            "changes_suggested": self.changes_suggested,
            "changes_blacklisted": self.changes_blacklisted,
            "test_result_analysis": self.test_result_analysis,
            "implementation_priority": self.implementation_priority,
        }
