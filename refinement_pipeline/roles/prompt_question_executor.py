"""Prompt Question Executor role for executing prompt questions."""


from .base import BaseRole


class PromptQuestionExecutor(BaseRole):
    """Executes prompt questions."""

    async def process(self, **kwargs) -> str:
        """Execute prompt questions."""
        try:
            provided_system_prompt = kwargs.get("provided_system_prompt", "")
            provided_user_prompt = kwargs.get("provided_user_prompt", "")
            questions = kwargs.get("questions", [])

            system_prompt = """
            You are a prompt evaluation specialist. 
            Your task is to analyze a given system prompt and user prompt and answer specific questions about them.

            Instructions:
            - You will receive a system prompt and user prompt and a set of evaluation questions
            - Analyze the system prompt and user prompt thoroughly against each question
            - Provide a binary pass/fail answer for each question
            - Base your evaluation on the system prompt and user prompt's content, structure, and adherence to the specified criteria
            - Be objective and consistent in your assessments
            """

            user_prompt_inputs = f"""
            Answer the provided questions about the system prompt and user prompt.

            Questions:
            {questions}

            System Prompt:
            {provided_system_prompt}

            User Prompt:
            {provided_user_prompt}
            """
            user_prompt_output = """
            Return the answers to the questions in a JSON object with the following structure:
            {
                "questions": [
                    {
                        "question": "question 1",
                        "answer": "pass/fail",
                        "reasoning": "reasoning for the answer",
                        "feedback": "how to improve the prompt to pass the question"
                    },
                    {
                        "question": "question 2",
                        "answer": "pass/fail",
                        "reasoning": "reasoning for the answer",
                        "feedback": "how to improve the prompt to pass the question"
                    },
                    ...
                ]
            }
            """
            user_prompt = user_prompt_inputs + user_prompt_output

            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] ResultComparator failed: {e}")
            return f"error: {e}"
