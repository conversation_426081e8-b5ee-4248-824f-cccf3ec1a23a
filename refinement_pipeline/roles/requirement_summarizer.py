"""Requirement Summarizer role for summarizing the requirements."""

from .base import BaseRole


class RequirementSummarizer(BaseRole):
    """Summarizes the requirements."""

    async def process(self, **kwargs) -> str:
        """Summarize the requirements."""
        try:
            requirements_doc = kwargs.get("requirements_doc", {})
            system_prompt = """
            You are a specialized requirements extraction assistant.
            Your task is to analyze large JSON files and extract all significant prompt requirements, constraints,
            and specifications it should follow according to the requirements document.

            PRIMARY OBJECTIVE: Extract every meaningful requirement from the provided JSON, organizing them into clear, actionable categories.
            ANALYSIS APPROACH:
            Parse the JSON structure systematically
            - Identify requirement patterns across all nested levels
            - Categorize findings by type and priority
            - Present results in a structured, scannable format

            What Constitutes a "Significant Requirement"
            Extract ANY element that specifies:
            - requirements
            - specifications
            - constraints
            - dependencies
            - expectations
            - any other relevant information

            Prioritize the requirements based on the following criteria:
            - The requirements that are most important to the prompt
            - The requirements that are most likely to be violated
            - The requirements that are most likely to be easy to check and verify

            Error Handling
            If the JSON is malformed or too large to process completely:
            - Extract requirements from the processable portions
            - Indicate which sections couldn't be analyzed without any further information
            """

            user_prompt_inputs = f"""
            Summarize the requirements in the following JSON and extract the requirements that are most important to the prompt:
            {requirements_doc}
            """

            user_prompt_output = """
            Return a JSON response with this exact structure:
            {
                "requirements": [
                    {
                        "requirement": "requirement 1",
                        "priority": "high" | "medium" | "low",
                        "category": "category of the requirement",
                        "reasoning": "reasoning for the priority"
                    },
                    {
                        "requirement": "requirement 2",
                        "priority": "high" | "medium" | "low",
                        "category": "category of the requirement",
                        "reasoning": "reasoning for the priority"
                    },
                    ...
                ]
            }
            """
            user_prompt = user_prompt_inputs + user_prompt_output
            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] FeedbackGenerator failed: {e}")
            return f"error: {e}"
