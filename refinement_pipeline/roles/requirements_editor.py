"""Requirements Editor role for editing requirements."""


from .base import BaseRole


class RequirementsEditor(BaseRole):
    """Edits requirements."""

    async def process(self, **kwargs) -> str:
        """Edit requirements."""
        try:
            requirements = kwargs.get("requirements", "")
            initial_prompt = kwargs.get("initial_prompt", "")
            edited_prompt = kwargs.get("edited_prompt", "")

            system_prompt = """
            You are an expert document editor specializing in requirements analysis and synchronization.

            Your task is to analyze an original requirements document alongside its corresponding prompt that has undergone multiple editing iterations, 
            then update the requirements to maintain consistency with the final prompt version.

            Process:
            1. Compare the original requirements with the edited prompt
            2. Identify specific changes, additions, or deletions in the prompt
            3. Determine which requirements need modification to reflect these changes
            4. Edit the requirements document to ensure alignment with the current prompt state
            5. Maintain the original structure and formatting style while incorporating necessary updates

            Focus on preserving the intent and scope of the original requirements while ensuring they accurately reflect all prompt modifications.
            """

            user_prompt_inputs = f"""
            Identify the changes that were made to the prompt and edit the requirements to reflect those changes.

            Initial Prompt:
            {initial_prompt}

            Edited Prompt:
            {edited_prompt}

            Requirements:
            {requirements}
            """
            user_prompt_output = """
            Return the edited requirements in the same format as the initial requirements with the changes applied.
            """
            user_prompt = user_prompt_inputs + user_prompt_output

            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] ResultComparator failed: {e}")
            return f"error: {e}"
