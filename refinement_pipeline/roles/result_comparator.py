"""Test Executor role for comparing test results and returning feedback."""

import json

from .base import BaseRole


class ResultComparator(BaseRole):
    """Compares test results and returns feedback."""

    async def process(self, **kwargs) -> str:
        """Compare test results and return feedback."""
        try:
            expected_output = kwargs.get("expected_output", "")
            if isinstance(expected_output, dict):
                expected_output = json.dumps(expected_output)
            actual_output = kwargs.get("actual_output", "")
            if isinstance(actual_output, dict):
                actual_output = json.dumps(actual_output)

            system_prompt = """
            You are an expert semantic analyzer that evaluates whether actual outputs fulfill the intent and requirements of expected outputs.

            Core Task:
            Compare two strings and determine if the actual output successfully accomplishes what the expected output was designed to achieve.

            Primary Focus: Semantic Equivalence
            - Assess whether the actual output conveys the same core meaning, information,
            or achieves the same functional purpose as the expected output
            - Consider context, intent, and practical utility rather than exact textual matching

            Flexibility Guidelines:
            - Accept reasonable variations in:
                - Word choice and phrasing
                - Sentence structure and organization
                - Level of detail (if core information is preserved)
                - Formatting and presentation style
            - Recognize equivalent expressions, synonyms, and paraphrasing
            - Account for different valid approaches to the same problem

            Quality Considerations:
            - Evaluate completeness: Does the actual output address all key components?
            - Assess accuracy: Is the information correct and reliable?
            - Consider clarity: Is the message effectively communicated?
            - Check relevance: Does it stay on topic and serve the intended purpose?
            """

            user_prompt_inputs = f"""
            Analyze whether the actual output semantically matches or fulfills the purpose of the expected output. Focus on meaning, intent, and functional equivalence rather than exact textual similarity.

            Input Data:
            - Expected Output:
            {expected_output}
            - Actual Output:
            {actual_output}

            Analysis Requirements:
            - Evaluate semantic equivalence and functional purpose
            - Consider context, intent, and practical utility
            - Assess completeness, accuracy, and relevance
            - Identify meaningful differences that impact effectiveness
            - Provide constructive feedback for improvement
            """
            user_prompt_output = """
            Return your analysis as a JSON object with the following structure:
            {
                "match": true/false,
                "similarity_score": 0.0-1.0,
                "confidence_level": "high/medium/low",
                "key_similarities": ["List main points where outputs align, if any"],
                "significant_differences": ["List differences that impact meaning or effectiveness, if any"],
                "missing_elements": ["List expected elements absent from actual output, if any"],
                "additional_elements": ["List unexpected elements present in actual output, if any"],
                "overall_assessment": "Brief summary of match quality",
                "feedback": "Detailed explanation of comparison results, including specific examples and recommendations for improvement if needed, if any"
            }
            """
            user_prompt = user_prompt_inputs + user_prompt_output

            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] ResultComparator failed: {e}")
            return f"error: {e}"
