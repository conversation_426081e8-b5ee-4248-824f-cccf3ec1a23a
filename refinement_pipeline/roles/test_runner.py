"""Test Executor role for executing test cases."""

from .base import BaseRole


class TestRunner(BaseRole):
    """Executes test cases and returns results."""

    async def process(self, **kwargs) -> str:
        """Execute a test case and return results."""
        try:
            user_prompt = kwargs.get("user_prompt", "")
            system_prompt = kwargs.get("system_prompt", "")
            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] TestRunner failed: {e}")
            return f"error: {e}"
