"""Test Executor role for generating feedback for the test results."""

from .base import BaseRole


class FeedbackGenerator(BaseRole):
    """Generates feedback for the test results."""

    async def process(self, **kwargs) -> str:
        """Generate feedback for the test results."""
        try:
            system_message = kwargs.get("system_message", "")
            user_message = kwargs.get("user_message", "")
            validation_questions_results = kwargs.get("validation_questions_results", {})
            test_results = kwargs.get("test_results", [])

            system_prompt = """
            You are a prompt engineering specialist tasked with analyzing prompt performance and providing actionable improvement recommendations.

            Your Analysis Process:
            - Identify specific failure modes - What exactly went wrong? Where did the prompt fail to constrain or guide the model effectively?
            - Diagnose root causes - Are issues due to ambiguous instructions, missing context, poor structure, or inadequate examples?
            - Prioritize fixes - Focus on changes that will have the highest impact on performance

            Requirements for Your Feedback:
            - Be direct and specific - Point to exact phrases, missing elements, or structural problems
            - Provide concrete solutions - Don't just identify problems; offer precise rewording or additions
            - Include before/after examples - Show how your suggested changes would alter the output
            - Preserve critical elements - Explicitly call out any portions of the original prompt that must remain unchanged
            - Consider edge cases - Address how your improvements handle unusual inputs or boundary conditions
            """

            user_prompt_inputs = f"""
            Analyze the following prompt configuration, validation questions and test results to provide comprehensive improvement recommendations.

            Input Materials:
            - System Message:
            {system_message}

            - User Message:
            {user_message}

            - Validation Questions:
            {validation_questions_results}

            - Test Results:
            {test_results}

            Analysis Instructions:
            1. System Message Analysis
            Examine the system message for:
            - Unclear or conflicting instructions
            - Missing context or constraints
            - Inadequate role definition
            - Poor example quality or coverage
            - Structural issues that impede understanding

            2. User Message Analysis
            Evaluate the user message for:
            - Ambiguous task specifications
            - Missing required parameters or context
            - Inconsistency with system message expectations
            - Inadequate input formatting or structure

            3. Test Results Integration
            Use the test results to:
            - Identify specific failure patterns and their frequency
            - Correlate failures to specific prompt elements
            - Distinguish between systematic vs. edge case issues
            - Prioritize fixes based on impact on performance

            4. Validation Questions Integration
            Use the validation questions to:
            - Identify specific failure modes and their frequency
            - Correlate failures to specific prompt elements
            - Distinguish between systematic vs. edge case issues
            - Prioritize fixes based on impact on performance
            """

            user_prompt_output = """
            Return a JSON response with this exact structure:
            {
                "issues_present": true/false (true if there are any issues present in the prompt),
                "critical_issues": {
                    "system_prompt": "Specific problems with line/section references",
                    "user_prompt": "Specific problems with exact quotes",
                    "interaction_issues": "Problems arising from system/user prompt interaction"
                },
                "changes_suggested": {
                    "system_prompt": [
                        {
                            "location": "Specific section/line reference",
                            "current_text": "Exact text to change",
                            "proposed_text": "Replacement text",
                            "rationale": "Why this change addresses the identified issue",
                            "expected_impact": "How this should improve performance"
                        }
                    ],
                    "user_prompt": [
                        {
                            "location": "Specific section reference",
                            "current_text": "Exact text to change",
                            "proposed_text": "Replacement text",
                            "rationale": "Why this change addresses the identified issue",
                            "expected_impact": "How this should improve performance"
                        }
                    ]
                },
                "changes_blacklisted": [
                    {
                        "element": "Exact text or concept that cannot be changed",
                        "location": "System or user prompt",
                        "reason": "Why this element is critical to maintain"
                    }
                ],
                "test_result_analysis": {
                    "failure_patterns": [
                        "Specific patterns observed in test results"
                    ],
                    "success_factors": [
                        "Elements that worked well in successful cases"
                    ],
                    "edge_case_handling": [
                        "How suggested changes address boundary conditions"
                    ]
                },
                "validation_question_analysis": {
                    "failure_patterns": [
                        "Specific patterns observed in validation questions"
                    ],
                    "success_factors": [
                        "Elements that worked well in successful cases"
                    ],
                    "edge_case_handling": [
                        "How suggested changes address boundary conditions"
                    ]
                },
                "implementation_priority": [
                    {
                        "change_description": "Brief description of the change",
                        "priority_level": "High/Medium/Low",
                        "implementation_complexity": "Simple/Moderate/Complex",
                        "expected_improvement": "Quantitative or qualitative improvement estimate"
                    }
                ]
            }
            Quality Standards:
            - All recommendations must be traceable to specific test failures
            - Include exact quotes when referencing problematic text
            - Provide measurable success criteria where possible
            - Consider both immediate fixes and long-term prompt architecture improvements
            """
            user_prompt = user_prompt_inputs + user_prompt_output
            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] FeedbackGenerator failed: {e}")
            return f"error: {e}"
