"""Prompt Refiner role for refining prompts."""


from .base import BaseRole


class PromptRefiner(BaseRole):
    """Refines prompt according to feedback provided."""

    async def process(self, **kwargs) -> str:
        """Refine prompt according to feedback provided."""
        try:
            prompt = kwargs.get("prompt", "")
            feedback = kwargs.get("feedback", "")

            system_prompt = """
            You are a skilled prompt optimization specialist. Your objective is to enhance prompts based on provided feedback 
            while maintaining their original purpose and intent.

            Primary Responsibilities:
            - Analyze and improve both system and user prompts
            - Incorporate all actionable feedback suggestions
            - Preserve the core intent and functionality of the original prompt
            - Avoid implementing any modifications listed in the restricted changes section

            Guidelines:
            - Focus on clarity, precision, and effectiveness
            - Maintain the prompt's original scope and goals
            - Apply feedback systematically and thoroughly
            - Ensure all improvements align with the prompt's intended use case
            """

            user_prompt_inputs = f"""
            Enhance the provided prompt by incorporating the feedback while preserving its original intent and functionality.

            Input Requirements:
            - Prompt:
            {prompt}

            - Improvement Feedback:
            {feedback}

            Task Instructions:
            - Analyze the feedback thoroughly and identify all actionable improvements
            - Apply modifications systematically across all relevant sections
            - Maintain the prompt's core purpose and effectiveness
            - Ensure consistency and coherence throughout the refined version
            """

            user_prompt_output = """
            Output Format:
            Return a JSON object that mirrors the original prompt structure with all feedback-based improvements implemented. 
            Update every field that requires modification based on the provided feedback.

            Quality Standards:
            - All suggested changes must be reflected in the output
            - No restricted modifications should be applied
            - The refined prompt should demonstrate clear improvements in clarity, precision, or effectiveness
            - Maintain professional tone and appropriate formatting throughout
            """
            user_prompt = user_prompt_inputs + user_prompt_output

            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] ResultComparator failed: {e}")
            return f"error: {e}"
