"""Validation Questions Generator role for generating validation questions."""

from .base import BaseRole


class ValidationQuestionsGenerator(BaseRole):
    """Generates validation questions."""

    async def process(self, **kwargs) -> str:
        """Generate validation questions."""
        try:
            requirements = kwargs.get("requirements", {})
            system_prompt = """
            You are a validation questions generator that creates binary (yes/no) questions to evaluate prompt compliance against specific requirements.

            Core Function:
            Generate clear, actionable validation questions that can be answered with "yes" or "no" to determine whether a prompt meets all specified requirements.

            Question Guidelines:
            - Binary responses only: Every question must be answerable with a simple yes/no
            - Specific and measurable: Focus on concrete, observable elements rather than subjective assessments
            - Comprehensive coverage: Address all critical aspects of the requirements
            - Clear language: Use precise, unambiguous wording

            Question Examples:

            Content Verification:
            - Does the prompt contain [specific element/instruction]?
            - Does the prompt include examples of [required format/behavior]?
            - Does the prompt specify [particular constraint/limitation]?

            Structure and Format
            - Does the prompt follow the [required structure/format]?
            - Are the instructions organized in [specified manner]?
            - Does the prompt use the required [tags/formatting/syntax]?

            Compliance Checking
            - Does the prompt adhere to the [specific guideline/rule]?
            - Does the prompt avoid [prohibited content/behavior]?
            - Is the prompt compliant with [standard/policy]?

            Completeness Assessment
            - Does the prompt address all [required topics/areas]?
            - Are all mandatory sections present in the prompt?
            - Does the prompt cover [specific use cases/scenarios]?

            Not all questions have to be from the above categories or examples.
            """

            user_prompt_inputs = f"""
            Generate comprehensive validation questions to evaluate prompt compliance against the following requirements:
            Requirements:
            {requirements}

            Instructions:
            - Create binary (yes/no) validation questions that cover all aspects of the requirements
            - Ensure questions are specific, measurable, and unambiguous
            - Address different validation categories: content verification, structure/format, compliance checking, and completeness assessment
            - Generate sufficient questions to thoroughly evaluate compliance (typically 8-15 questions depending on requirement complexity)
            - Each question should be evaluable independently by different reviewers with consistent results
            """

            user_prompt_output = """
            Return your response as valid JSON with this exact structure:
            {
                "questions": [
                    "question 1",
                    "question 2",
                    ...
                ],
                "metadata": {
                    "total_questions": 12,
                    "coverage_areas": [
                        "content_verification",
                        "structure_format",
                        "compliance_checking",
                        "completeness_assessment"
                    ],
                    "requirements_analyzed": 4
                }
            }

            Quality Criteria:
            - Questions must be answerable with definitive yes/no responses
            - Each question should test a distinct aspect of the requirements
            - Avoid overlapping or redundant questions
            - Focus on observable, objective criteria rather than subjective judgments
            """

            user_prompt = user_prompt_inputs + user_prompt_output
            return await self.call_llm(user_prompt, system_prompt)
        except Exception as e:
            print(f"[ERROR] FeedbackGenerator failed: {e}")
            return f"error: {e}"
