"""Base role class for Test Case Chunker roles."""

import asyncio
from abc import ABC, abstractmethod
from typing import Any

import openai


class BaseRole(ABC):
    """Base class for all roles in Test Case Chunker."""

    def __init__(self, config: dict[str, Any]) -> None:
        self.config = config
        self.model = config.get("model", "gpt-4o-mini")
        self.temperature = config.get("temperature", 1.0)
        self.max_tokens = config.get("max_tokens", 10000)
        self.description = config.get("description", "")

    async def call_llm(self, user_prompt: str, system_prompt: str, **kwargs) -> str:
        """Make LLM call with rate limiting."""
        try:
            # Rate limiting - shorter delay for parallel processing
            await asyncio.sleep(0.05)  # Reduced delay for parallel calls

            response = await openai.AsyncOpenAI().chat.completions.create(
                model=self.model,
                messages=[{"role": "system", "content": system_prompt}, {"role": "user", "content": user_prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
            )

            return response.choices[0].message.content or ""

        except Exception as e:
            print(f"[ERROR] LLM call failed: {e}")
            raise

    @abstractmethod
    async def process(self, **kwargs) -> Any:
        """Process the input and return result."""

    def get_config(self) -> dict[str, Any]:
        """Get role configuration."""
        return {"model": self.model, "temperature": self.temperature, "max_tokens": self.max_tokens, "description": self.description}
