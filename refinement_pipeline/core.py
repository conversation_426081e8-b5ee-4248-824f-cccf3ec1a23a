"""Core Refinement Pipeline module."""

import asyncio
import json
import os
import sys
from typing import Any, Optional

from refinement_pipeline.roles.prompt_question_executor import PromptQuestionExecutor
from refinement_pipeline.roles.requirements_editor import RequirementsEditor
from refinement_pipeline.roles.validation_questions_generator import ValidationQuestionsGenerator
from utils.json_parser import parse_json

sys.path.insert(0, os.path.dirname(__file__))
from refinement_pipeline.config import Config
from refinement_pipeline.models import PromptFeedback, TestResult
from refinement_pipeline.roles.feedback_generator import FeedbackGenerator
from refinement_pipeline.roles.prompt_refiner import PromptRefiner
from refinement_pipeline.roles.requirement_summarizer import RequirementSummarizer
from refinement_pipeline.roles.result_comparator import ResultComparator
from refinement_pipeline.roles.test_runner import TestRunner
from test_case_chunker.models import ChunkedTestCase


class RefinementPipeline:
    """Main class for executing refinement pipeline."""

    def __init__(self, config_path: Optional[str] = None) -> None:
        self.config = Config(config_path)
        self.test_runner = TestRunner(
            self.config.get_role_config("TestRunner"),
        )
        self.result_comparator = ResultComparator(
            self.config.get_role_config("ResultComparator"),
        )
        self.feedback_generator = FeedbackGenerator(
            self.config.get_role_config("FeedbackGenerator"),
        )
        self.prompt_refiner = PromptRefiner(
            self.config.get_role_config("PromptRefiner"),
        )
        self.requirement_summarizer = RequirementSummarizer(
            self.config.get_role_config("RequirementSummarizer"),
        )
        self.validation_questions_generator = ValidationQuestionsGenerator(
            self.config.get_role_config("ValidationQuestionsGenerator"),
        )
        self.validation_questions_executor = PromptQuestionExecutor(
            self.config.get_role_config("PromptQuestionExecutor"),
        )
        self.requirements_editor = RequirementsEditor(
            self.config.get_role_config("RequirementsEditor"),
        )

    async def edit_requirements(
        self, requirements_doc: dict[str, Any], initial_prompt: dict[str, Any], edited_prompt: dict[str, Any],
    ) -> dict[str, Any]:
        """Edit the requirements according to the feedback provided."""
        edited_requirements = await self.requirements_editor.process(
            requirements=requirements_doc,
            initial_prompt=initial_prompt,
            edited_prompt=edited_prompt,
        )
        return json.loads(parse_json(edited_requirements))

    async def refine_prompt(self, prompt: dict[str, Any], feedback: PromptFeedback) -> dict[str, Any]:
        """Refine the prompt according to the feedback provided."""
        prompt_str = json.dumps(prompt)
        feedback_str = json.dumps(feedback.prepare_for_refinement())
        refined_prompt = await self.prompt_refiner.process(prompt=prompt_str, feedback=feedback_str)
        return json.loads(parse_json(refined_prompt))

    async def generate_feedback(self, test_cases: list[ChunkedTestCase], requirements_doc: dict[str, Any], prompt: dict[str, Any]) -> PromptFeedback:
        """Generate feedback for the test cases."""
        print(f"[TEST_EXECUTOR] Generating feedback for {len(test_cases)} test cases")

        test_results, validation_questions_results = await asyncio.gather(
            self.execute_test_cases(test_cases),
            self.generate_and_execute_validation_questions(
                requirements_doc=requirements_doc,
                prompt=prompt,
            ),
        )
        feedback = await self.feedback_generator.process(
            system_message=prompt.get("system_message", ""),
            user_message=prompt.get("user_message", ""),
            test_results=test_results,
            validation_questions_results=validation_questions_results,
        )
        feedback = json.loads(parse_json(feedback))
        return PromptFeedback(
            system_message=prompt.get("system_message", ""),
            user_message=prompt.get("user_message", ""),
            test_results=test_results,
            issues_present=feedback.get("issues_present", False),
            critical_issues=feedback.get("critical_issues", {}),
            changes_suggested=feedback.get("changes_suggested", {}),
            changes_blacklisted=feedback.get("changes_blacklisted", []),
            test_result_analysis=feedback.get("test_result_analysis", {}),
            implementation_priority=feedback.get("implementation_priority", []),
        )

    async def generate_and_execute_validation_questions(self, requirements_doc: dict[str, Any], prompt: dict[str, Any]) -> dict[str, Any]:
        """Generate and execute validation questions.

        Args:
            requirements_doc: dict[str, Any] - requirements document
            prompt: dict[str, Any] - prompt to evaluate

        Returns:
            dict[str, Any] - validation questions results

        """
        requirements = await self.requirement_summarizer.process(requirements_doc=requirements_doc)
        validation_questions = await self.validation_questions_generator.process(requirements=requirements)
        validation_questions = json.loads(parse_json(validation_questions))
        validation_questions = validation_questions.get("questions", [])
        print(f"[VALIDATION_QUESTIONS_EXECUTOR] Executing {len(validation_questions)} validation questions")
        print(f"[DEBUG] Validation questions: {validation_questions}")
        validation_questions_results = await self.validation_questions_executor.process(
            provided_system_prompt=prompt.get("system_message", ""),
            provided_user_prompt=prompt.get("user_message", ""),
            questions=validation_questions,
        )
        return json.loads(parse_json(validation_questions_results))

    async def execute_test_cases(self, test_cases: list[ChunkedTestCase]) -> list[TestResult]:
        """Execute test cases and return test results."""
        print(f"[TEST_EXECUTOR] Executing {len(test_cases)} test cases")

        tasks = [self.execute_single_test_case(test_case) for test_case in test_cases]

        print(f"[TEST_EXECUTOR] Starting parallel execution of {len(test_cases)} test cases...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        test_results: list[TestResult] = []
        for result in results:
            if isinstance(result, TestResult):
                test_results.append(result)
            else:
                print(f"[TEST_EXECUTOR] Test case execution failed: {result}")

        print(f"[TEST_EXECUTOR] Completed execution. Found {len(test_results)} successful results")
        return test_results

    async def execute_single_test_case(self, test_case: ChunkedTestCase) -> TestResult:
        """Execute a single test case and return test result."""
        user_prompt = ""
        try:
            user_prompt = test_case.user_message

            for original_key, value in test_case.placeholders.items():
                user_prompt = user_prompt.replace(original_key, str(value))

        except Exception as e:
            print(f"[TEST_EXECUTOR] Error formatting user prompt: {e}")
            return TestResult(
                seed_id=test_case.seed_id,
                system_message=test_case.system_message,
                user_message=test_case.user_message,
                expected_output=test_case.seed_data.get("expected_output", ""),
                actual_output="",
                comparison_result={},
                status="error",
            )

        try:
            execution_result = await self.test_runner.process(
                system_prompt=test_case.system_message,
                user_message=user_prompt,
            )
            comparison_result = await self.result_comparator.process(
                expected_output=test_case.seed_data.get("expected_output", ""),
                actual_output=execution_result,
            )
            comparison_result = json.loads(parse_json(comparison_result))
            return TestResult(
                seed_id=test_case.seed_id,
                system_message=test_case.system_message,
                user_message=user_prompt,
                expected_output=test_case.seed_data.get("expected_output", ""),
                actual_output=execution_result,
                comparison_result=comparison_result,
                status="success",
            )
        except Exception as e:
            print(f"[TEST_EXECUTOR] Test case execution failed: {e}")
            return TestResult(
                seed_id=test_case.seed_id,
                system_message=test_case.system_message,
                user_message=user_prompt,
                expected_output=test_case.seed_data.get("expected_output", ""),
                actual_output=str(e),
                comparison_result={},
                status="error",
            )

    async def save_test_results(self, test_results: list[TestResult], output_path: str) -> None:
        """Save test results to file."""
        try:
            with open(output_path, "w") as f:
                json.dump([test_result.to_dict() for test_result in test_results], f, indent=2)
            print(f"[TEST_EXECUTOR] Saved {len(test_results)} test results to {output_path}")
        except Exception as e:
            print(f"[ERROR] Failed to save test results: {e}")

    async def save_prompt_feedback(self, prompt_feedback: PromptFeedback, output_path: str) -> None:
        """Save prompt feedback to file."""
        try:
            with open(output_path, "w") as f:
                json.dump(prompt_feedback.to_dict(), f, indent=2)
            print(f"[TEST_EXECUTOR] Saved prompt feedback to {output_path}")
        except Exception as e:
            print(f"[ERROR] Failed to save prompt feedback: {e}")
