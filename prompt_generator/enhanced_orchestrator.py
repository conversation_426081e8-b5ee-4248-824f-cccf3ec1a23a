"""Enhanced Orchestrator with adaptive workflows and domain-specific pipelines.

Oner: Provides advanced orchestration capabilities with adaptive workflows,
domain-specific pipelines, and sophisticated policy decisions based on context.

Pyramid Principle:
- Main: Adaptive workflow management and policy decisions
- Secondary: Domain-specific pipeline configuration
- Tertiary: Context-aware role selection and optimization

Requirements:
- Adaptive workflow selection based on task type
- Domain-specific pipeline configuration
- Context-aware policy decisions
- Performance monitoring and optimization
- Extensible workflow definitions
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from .config import load_config_safe
from .roles import discover_roles


class WorkflowType(Enum):
    """Supported workflow types with different optimization strategies."""
    STANDARD = "standard"  # Default workflow
    DOMAIN_SPECIFIC = "domain_specific"  # Domain-optimized workflow
    QUALITY_FOCUSED = "quality_focused"  # Quality-first workflow
    PERFORMANCE_OPTIMIZED = "performance_optimized"  # Speed-focused workflow
    COMPLIANCE_CRITICAL = "compliance_critical"  # Compliance-first workflow


@dataclass
class WorkflowConfig:
    """Configuration for different workflow types."""
    name: str
    description: str
    role_sequence: List[str]
    quality_threshold: float
    max_turns: int
    early_stop_conditions: List[str]
    optimization_goals: List[str]


class EnhancedOrchestrator:
    """Advanced orchestrator with adaptive workflows and domain-specific pipelines."""
    
    def __init__(self, 
                 target_score: float = 8.0,
                 max_turns: int = 10,
                 workflow_type: WorkflowType = WorkflowType.STANDARD,
                 domain: Optional[str] = None):
        self.target_score = target_score
        self.max_turns = max_turns
        self.workflow_type = workflow_type
        self.domain = domain
        self.roles = discover_roles()
        self.workflow_configs = self._get_workflow_configs()
        self.context = {}
    
    def _get_workflow_configs(self) -> Dict[WorkflowType, WorkflowConfig]:
        """Define workflow configurations for different optimization strategies."""
        configs = {
            WorkflowType.STANDARD: WorkflowConfig(
                name="Standard",
                description="Balanced workflow with quality and performance",
                role_sequence=["Writer", "OutputGuardian", "Critic", "Editor", "TokenOptimizer", "JSONEnforcer"],
                quality_threshold=8.0,
                max_turns=10,
                early_stop_conditions=["quality_met", "max_turns_reached"],
                optimization_goals=["quality", "performance", "clarity"]
            ),
            WorkflowType.DOMAIN_SPECIFIC: WorkflowConfig(
                name="Domain-Specific",
                description="Domain-optimized workflow with specialized roles",
                role_sequence=["Writer", "DomainSpecialist", "OutputGuardian", "Critic", "Editor", "QualityAssurance", "JSONEnforcer"],
                quality_threshold=8.5,
                max_turns=12,
                early_stop_conditions=["quality_met", "domain_optimized", "max_turns_reached"],
                optimization_goals=["domain_expertise", "quality", "compliance"]
            ),
            WorkflowType.QUALITY_FOCUSED: WorkflowConfig(
                name="Quality-Focused",
                description="Quality-first workflow with comprehensive testing",
                role_sequence=["Writer", "OutputGuardian", "Critic", "Editor", "QualityAssurance", "TokenOptimizer", "JSONEnforcer"],
                quality_threshold=9.0,
                max_turns=15,
                early_stop_conditions=["quality_met", "qa_passed", "max_turns_reached"],
                optimization_goals=["quality", "robustness", "safety"]
            ),
            WorkflowType.PERFORMANCE_OPTIMIZED: WorkflowConfig(
                name="Performance-Optimized",
                description="Speed-focused workflow with minimal iterations",
                role_sequence=["Writer", "OutputGuardian", "Critic", "Editor", "JSONEnforcer"],
                quality_threshold=7.0,
                max_turns=6,
                early_stop_conditions=["quality_met", "max_turns_reached"],
                optimization_goals=["speed", "efficiency", "clarity"]
            ),
            WorkflowType.COMPLIANCE_CRITICAL: WorkflowConfig(
                name="Compliance-Critical",
                description="Compliance-first workflow with extensive validation",
                role_sequence=["Writer", "OutputGuardian", "Critic", "Editor", "QualityAssurance", "DomainSpecialist", "JSONEnforcer"],
                quality_threshold=9.5,
                max_turns=20,
                early_stop_conditions=["quality_met", "compliance_verified", "qa_passed", "max_turns_reached"],
                optimization_goals=["compliance", "safety", "quality"]
            )
        }
        return configs
    
    def _detect_workflow_type(self, task_description: str) -> WorkflowType:
        """Automatically detect appropriate workflow type based on task description."""
        task_lower = task_description.lower()
        
        # Domain-specific keywords
        financial_keywords = ["financial", "investment", "valuation", "ebitda", "roi", "cash flow"]
        technical_keywords = ["api", "code", "software", "technical", "development", "system"]
        legal_keywords = ["legal", "compliance", "regulatory", "contract", "law", "regulation"]
        medical_keywords = ["medical", "health", "patient", "clinical", "diagnosis", "treatment"]
        
        # Quality-focused keywords
        quality_keywords = ["high quality", "precise", "accurate", "thorough", "comprehensive"]
        
        # Performance keywords
        performance_keywords = ["quick", "fast", "efficient", "simple", "brief"]
        
        # Compliance keywords
        compliance_keywords = ["compliance", "regulatory", "legal", "audit", "governance", "policy"]
        
        # Determine workflow type based on keywords
        if any(keyword in task_lower for keyword in compliance_keywords):
            return WorkflowType.COMPLIANCE_CRITICAL
        elif any(keyword in task_lower for keyword in quality_keywords):
            return WorkflowType.QUALITY_FOCUSED
        elif any(keyword in task_lower for keyword in performance_keywords):
            return WorkflowType.PERFORMANCE_OPTIMIZED
        elif any(keyword in task_lower for keyword in financial_keywords + technical_keywords + legal_keywords + medical_keywords):
            return WorkflowType.DOMAIN_SPECIFIC
        else:
            return WorkflowType.STANDARD
    
    def _adaptive_policy(self, state: Dict[str, Any]) -> Tuple[str | None, str]:
        """Adaptive policy that considers workflow type and context."""
        workflow_config = self.workflow_configs[self.workflow_type]
        
        # 1. Check early stop conditions
        if self._should_stop_early(state, workflow_config):
            return None, "Early stop condition met"
        
        # 2. Determine next role based on workflow sequence and current state
        next_role = self._get_next_role(state, workflow_config)
        
        if next_role is None:
            return None, "No suitable role found for current state"
        
        # 3. Generate reason for role selection
        reason = self._generate_role_reason(next_role, state, workflow_config)
        
        return next_role, reason
    
    def _should_stop_early(self, state: Dict[str, Any], config: WorkflowConfig) -> bool:
        """Check if early stop conditions are met, but ensure Critic and JSONEnforcer always run."""
        # CRITICAL: Never stop early if we haven't run Critic yet
        if "critic_score" not in state:
            return False
        
        # CRITICAL: Never stop early if we haven't run JSONEnforcer yet
        if "json_valid" not in state:
            return False
        
        # Now check other early stop conditions
        for condition in config.early_stop_conditions:
            if condition == "quality_met" and state.get("critic_score", 0) >= config.quality_threshold:
                return True
            elif condition == "max_turns_reached" and state.get("turn_count", 0) >= config.max_turns:
                return True
            elif condition == "domain_optimized" and state.get("domain_optimized", False):
                return True
            elif condition == "qa_passed" and state.get("qa_passed", False):
                return True
            elif condition == "compliance_verified" and state.get("compliance_verified", False):
                return True
        
        return False
    
    def _get_next_role(self, state: Dict[str, Any], config: WorkflowConfig) -> str | None:
        """Get the next role based on workflow sequence and current state."""
        # If no draft exists, start with Writer
        if "draft" not in state:
            return "Writer"
        
        # CRITICAL: If we're approaching max_turns, prioritize essential roles
        turn_count = state.get("turn_count", 0)
        max_turns = config.max_turns
        
        # If we're in the last 2 turns, prioritize Critic and JSONEnforcer
        if turn_count >= max_turns - 2:
            # Always run Critic if not done
            if "critic_score" not in state:
                return "Critic"
            # Always run JSONEnforcer if not done
            if "json_valid" not in state:
                return "JSONEnforcer"
            # If both are done, we can stop
            return None
        
        # Check if we need domain specialization
        if (self.workflow_type == WorkflowType.DOMAIN_SPECIFIC and 
            "domain_optimized" not in state and 
            "Writer" in [h.get("role") for h in state.get("history", [])]):
            return "DomainSpecialist"
        
        # Check if we need quality assurance
        if (self.workflow_type in [WorkflowType.QUALITY_FOCUSED, WorkflowType.COMPLIANCE_CRITICAL] and
            "qa_passed" not in state and
            "Editor" in [h.get("role") for h in state.get("history", [])]):
            return "QualityAssurance"
        
        # Check if we need JSON formatting (always last)
        if "json_valid" not in state and "draft" in state:
            # Rule: If JSONEnforcer would run but no critic_score, run Critic first
            if "critic_score" not in state:
                return "Critic"
            return "JSONEnforcer"
        
        # Standard role selection logic
        if "output_guardian_pass" not in state:
            return "OutputGuardian"
        elif "critic_score" not in state:
            return "Critic"
        elif state.get("critic_score", 0) < config.quality_threshold:
            if state.get("_last_role") == "Editor":
                return "Critic"
            else:
                return "Editor"
        elif "token_saving_pct" not in state:
            return "TokenOptimizer"
        
        return None
    
    def _generate_role_reason(self, role: str, state: Dict[str, Any], config: WorkflowConfig) -> str:
        """Generate a reason for selecting the role."""
        reasons = {
            "Writer": "Create initial draft",
            "DomainSpecialist": f"Optimize for {self.domain or 'specific'} domain",
            "OutputGuardian": "Validate output format compliance",
            "Critic": "Evaluate quality and provide feedback",
            "Editor": "Improve draft based on feedback",
            "QualityAssurance": "Perform comprehensive quality testing",
            "TokenOptimizer": "Optimize token usage",
            "JSONEnforcer": "Format output as standardized JSON"
        }
        
        base_reason = reasons.get(role, f"Execute {role} role")
        
        # Add workflow-specific context
        if self.workflow_type == WorkflowType.QUALITY_FOCUSED and role == "QualityAssurance":
            base_reason += " (quality-focused workflow)"
        elif self.workflow_type == WorkflowType.DOMAIN_SPECIFIC and role == "DomainSpecialist":
            base_reason += f" (domain-specific workflow for {self.domain})"
        
        return base_reason
    
    def run(self, task_description: str) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Execute enhanced orchestration with adaptive workflow."""
        # Auto-detect workflow type if not specified
        if self.workflow_type == WorkflowType.STANDARD:
            self.workflow_type = self._detect_workflow_type(task_description)
        
        # Initialize state with workflow context
        state: Dict[str, Any] = {
            "task": task_description,
            "target_score": self.target_score,
            "workflow_type": self.workflow_type.value,
            "domain": self.domain,
            "turn_count": 0,
            "history": []
        }
        
        return self.run_with_state(state)
    
    def run_with_state(self, state: Dict[str, Any]) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Execute enhanced orchestration with pre-initialized state."""
        # Auto-detect workflow type if not specified
        if self.workflow_type == WorkflowType.STANDARD:
            self.workflow_type = self._detect_workflow_type(state.get("task", ""))
        
        # Ensure basic state structure
        if "turn_count" not in state:
            state["turn_count"] = 0
        if "history" not in state:
            state["history"] = []
        if "target_score" not in state:
            state["target_score"] = self.target_score
        if "workflow_type" not in state:
            state["workflow_type"] = self.workflow_type.value
        if "domain" not in state:
            state["domain"] = self.domain
        
        history: List[Dict[str, Any]] = state["history"]
        workflow_config = self.workflow_configs[self.workflow_type]
        
        # Update max_turns based on workflow configuration
        self.max_turns = min(self.max_turns, workflow_config.max_turns)
        
        # Check if we should use enhanced writer
        use_enhanced_writer = state.get("use_enhanced_writer", False)
        
        for turn in range(state["turn_count"] + 1, self.max_turns + 1):
            state["turn_count"] = turn
            
            role_name, reason = self._adaptive_policy(state)
            if role_name is None:
                state["termination_reason"] = reason
                break
            
            # Use enhanced writer if specified and this is the first turn
            if use_enhanced_writer and role_name == "Writer" and turn == 1:
                role_name = "EnhancedWriter"
            
            role = self.roles.get(role_name)
            if role is None:
                raise KeyError(f"Role '{role_name}' not found. Ensure it exists in roles package.")
            
            # Execute role
            output = role.eval(state)
            state.update(output.to_state_update())
            state["_last_role"] = role_name
            
            # Record history
            history_entry = {
                "turn": turn,
                "role": role_name,
                "reason": reason,
                "workflow_type": self.workflow_type.value,
                "output_log": state.get("log", ""),
            }
            history.append(history_entry)
            state["history"] = history
            
            # Clear single-use log
            state.pop("log", None)
        
        return state, history 