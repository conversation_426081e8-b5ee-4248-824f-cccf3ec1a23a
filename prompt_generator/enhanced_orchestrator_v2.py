"""
Enhanced Orchestrator v2 with Requirements-Aware Prompt Generation.

This enhanced version can handle both simple string input and requirements-enhanced input
from the requirements document generator.
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional, Tuple, Union
from enum import Enum
from dataclasses import dataclass

from .enhanced_input_handler import <PERSON>hanced<PERSON>n<PERSON><PERSON><PERSON><PERSON>, EnhancedPromptInput
from .enhanced_orchestrator import EnhancedOrchestrator, WorkflowType
from .roles import discover_roles
from .roles.enhanced_writer import EnhancedWriter


class InputType(Enum):
    """Types of input that can be processed."""
    SIMPLE = "simple"
    ENHANCED = "enhanced"
    REQUIREMENTS_DOCUMENT = "requirements_document"


@dataclass
class EnhancedOrchestrationResult:
    """Result of enhanced orchestration with requirements awareness."""
    
    # Core results
    final_prompt: str
    state: Dict[str, Any]
    history: List[Dict[str, Any]]
    
    # Requirements context
    requirements_context: Optional[Dict[str, Any]] = None
    domain_context: Optional[Dict[str, Any]] = None
    quality_context: Optional[Dict[str, Any]] = None
    
    # Input analysis
    input_type: InputType = InputType.SIMPLE
    enhanced_input: Optional[EnhancedPromptInput] = None
    
    # Workflow information
    workflow_type: Optional[WorkflowType] = None
    
    # Performance metrics
    execution_time: float = 0.0
    alignment_score: Optional[float] = None


class EnhancedOrchestratorV2:
    """Enhanced orchestrator that can handle requirements-aware prompt generation."""
    
    def __init__(self, 
                 target_score: float = 8.0,
                 max_turns: int = 10,
                 workflow_type: WorkflowType = WorkflowType.STANDARD,
                 domain: Optional[str] = None):
        self.target_score = target_score
        self.max_turns = max_turns
        self.workflow_type = workflow_type
        self.domain = domain
        
        # Initialize components
        self.input_handler = EnhancedInputHandler()
        self.base_orchestrator = EnhancedOrchestrator(
            target_score=target_score,
            max_turns=max_turns,
            workflow_type=workflow_type,
            domain=domain
        )
        
        # Enhanced roles
        self.enhanced_roles = {
            "EnhancedWriter": EnhancedWriter()
        }
    
    def run(self, input_data: Union[str, Dict[str, Any], Any]) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Run enhanced orchestration with requirements awareness.
        
        Args:
            input_data: Can be a simple string, enhanced dict, or RequirementsDocument
            
        Returns:
            Tuple of (state, history)
        """
        # Determine input type and create enhanced input
        input_type = self._determine_input_type(input_data)
        enhanced_input = self.input_handler.create_enhanced_input(input_data)
        
        # Create enhanced state
        enhanced_state = self.input_handler.create_enhanced_state(enhanced_input)
        
        # Use enhanced writer if we have requirements context
        if input_type != InputType.SIMPLE:
            enhanced_state["use_enhanced_writer"] = True
        
        # Run the base orchestration with enhanced state
        state, history = self.base_orchestrator.run_with_state(enhanced_state)
        
        return state, history
    
    def run_with_enhanced_result(self, input_data: Union[str, Dict[str, Any], Any]) -> EnhancedOrchestrationResult:
        """
        Run enhanced orchestration and return detailed result.
        
        Args:
            input_data: Can be a simple string, enhanced dict, or RequirementsDocument
            
        Returns:
            EnhancedOrchestrationResult: Detailed result with requirements context
        """
        import time
        
        start_time = time.time()
        
        # Determine input type
        input_type = self._determine_input_type(input_data)
        enhanced_input = self.input_handler.create_enhanced_input(input_data)
        
        # Run orchestration
        state, history = self.run(input_data)
        
        execution_time = time.time() - start_time
        
        # Extract final prompt
        final_prompt = state.get("draft", "")
        
        # Create result
        result = EnhancedOrchestrationResult(
            final_prompt=final_prompt,
            state=state,
            history=history,
            requirements_context=state.get("requirements_context"),
            domain_context=state.get("domain_context"),
            quality_context=state.get("quality_context"),
            input_type=input_type,
            enhanced_input=enhanced_input,
            workflow_type=self.workflow_type,
            execution_time=execution_time
        )
        
        return result
    
    def _determine_input_type(self, input_data: Union[str, Dict[str, Any], Any]) -> InputType:
        """Determine the type of input provided."""
        if isinstance(input_data, str):
            return InputType.SIMPLE
        elif self.input_handler.is_enhanced_input(input_data):
            return InputType.ENHANCED
        else:
            # Assume it's a requirements document
            return InputType.REQUIREMENTS_DOCUMENT
    
    def get_workflow_recommendation(self, input_data: Union[str, Dict[str, Any], Any]) -> WorkflowType:
        """Get workflow recommendation based on enhanced input."""
        enhanced_input = self.input_handler.create_enhanced_input(input_data)
        
        # Use domain context for workflow recommendation
        domain = enhanced_input.domain
        complexity = enhanced_input.complexity_level
        
        if domain in ["financial", "medical", "legal"]:
            return WorkflowType.DOMAIN_SPECIFIC
        elif complexity == "enterprise":
            return WorkflowType.COMPLIANCE_CRITICAL
        elif complexity == "simple":
            return WorkflowType.PERFORMANCE_OPTIMIZED
        else:
            return WorkflowType.STANDARD


# Enhanced API functions
def run_enhanced_orchestrator_v2(
    input_data: Union[str, Dict[str, Any], Any],
    *,
    target_score: float = 8.0,
    max_turns: int = 10,
    workflow_type: WorkflowType = WorkflowType.STANDARD,
    domain: Optional[str] = None
) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Run enhanced orchestrator v2 with requirements awareness.
    
    Args:
        input_data: Can be a simple string, enhanced dict, or RequirementsDocument
        target_score: Target quality score
        max_turns: Maximum number of turns
        workflow_type: Type of workflow to use
        domain: Domain for domain-specific workflows
        
    Returns:
        Tuple of (state, history)
    """
    orchestrator = EnhancedOrchestratorV2(
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=workflow_type,
        domain=domain
    )
    
    return orchestrator.run(input_data)


def run_enhanced_orchestrator_v2_with_result(
    input_data: Union[str, Dict[str, Any], Any],
    *,
    target_score: float = 8.0,
    max_turns: int = 10,
    workflow_type: WorkflowType = WorkflowType.STANDARD,
    domain: Optional[str] = None
) -> EnhancedOrchestrationResult:
    """
    Run enhanced orchestrator v2 and return detailed result.
    
    Args:
        input_data: Can be a simple string, enhanced dict, or RequirementsDocument
        target_score: Target quality score
        max_turns: Maximum number of turns
        workflow_type: Type of workflow to use
        domain: Domain for domain-specific workflows
        
    Returns:
        EnhancedOrchestrationResult: Detailed result with requirements context
    """
    orchestrator = EnhancedOrchestratorV2(
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=workflow_type,
        domain=domain
    )
    
    return orchestrator.run_with_enhanced_result(input_data)


def run_requirements_aware_prompt_generation(
    input_data: Union[str, Dict[str, Any], Any],
    *,
    target_score: float = 8.5,
    max_turns: int = 12,
    enable_analytics: bool = True  # Ignored for now
) -> EnhancedOrchestrationResult:
    """
    Run requirements-aware prompt generation with enhanced context.
    
    Args:
        input_data: Can be a simple string, enhanced dict, or RequirementsDocument
        target_score: Target quality score
        max_turns: Maximum number of turns
        
    Returns:
        EnhancedOrchestrationResult: Detailed result
    """
    # Determine workflow type based on input
    orchestrator = EnhancedOrchestratorV2()
    workflow_type = orchestrator.get_workflow_recommendation(input_data)
    
    return run_enhanced_orchestrator_v2_with_result(
        input_data=input_data,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=workflow_type
    ) 


def get_workflow_recommendation(
    input_data: Union[str, Dict[str, Any], Any],
    *,
    target_score: float = 8.0,
    max_turns: int = 10,
    domain: Optional[str] = None
) -> Dict[str, Any]:
    """
    Top-level function to get workflow recommendation for API use.
    Returns a dict with recommended_workflow, reasoning, confidence, and alternatives.
    """
    orchestrator = EnhancedOrchestratorV2(
        target_score=target_score,
        max_turns=max_turns,
        domain=domain
    )
    workflow_type = orchestrator.get_workflow_recommendation(input_data)
    # Simple reasoning and confidence for demo
    reasoning = f"Recommended workflow based on input analysis: {workflow_type.value}."
    confidence = 0.9 if workflow_type != WorkflowType.STANDARD else 0.7
    alternatives = [w.value for w in WorkflowType if w != workflow_type]
    return {
        "recommended_workflow": workflow_type.value,
        "reasoning": reasoning,
        "confidence": confidence,
        "alternative_workflows": alternatives
    } 


def generate_analytics_report(file_path: str) -> Dict[str, Any]:
    """
    Generate analytics report from saved data.
    This is a placeholder implementation for API compatibility.
    """
    # Mock analytics report for demo purposes
    return {
        "report": {
            "total_prompts_generated": 0,
            "average_quality_score": 8.5,
            "workflow_distribution": {
                "standard": 0,
                "domain_specific": 0,
                "quality_focused": 0,
                "performance_optimized": 0,
                "compliance_critical": 0
            },
            "execution_time_stats": {
                "average": 0.0,
                "min": 0.0,
                "max": 0.0
            }
        },
        "insights": [
            "No analytics data available yet",
            "Run more prompt generations to gather analytics"
        ],
        "recommendations": [
            "Generate more prompts to build analytics",
            "Consider different workflow types for variety",
            "Monitor quality scores for optimization"
        ]
    } 