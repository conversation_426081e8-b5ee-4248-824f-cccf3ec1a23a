"""Orchestrator – decides which role should act next based on current state.

Oner: Manages the multi-agent workflow by determining which role should execute
next based on the current state and quality metrics.

Pyramid Principle:
- Main: Policy-driven role selection and workflow management
- Secondary: State tracking and quality threshold enforcement
- Tertiary: Turn counting and termination logic

Requirements:
- Deterministic policy function for role selection
- Quality score tracking and threshold enforcement
- Turn limit management
- State persistence and history tracking
- Infinite loop prevention
- Backward compatibility with old API
"""

from __future__ import annotations

from typing import Any, Dict, Tuple

from .enhanced_orchestrator import EnhancedOrchestrator, WorkflowType
from .config import load_config_safe

# Load defaults from config.yaml
config = load_config_safe()
DEFAULT_TARGET_SCORE = config.get("target_score", 8.0)
DEFAULT_MAX_TURNS = config.get("max_turns", 10)


class Orchestrator:
    """Manages conversational turns between virtual teammates.
    
    This is a backward-compatible wrapper around the enhanced orchestrator.
    """

    def __init__(self, target_score: float = DEFAULT_TARGET_SCORE, max_turns: int = DEFAULT_MAX_TURNS):
        self.target_score = target_score
        self.max_turns = max_turns
        # Create enhanced orchestrator with standard workflow
        self.enhanced_orchestrator = EnhancedOrchestrator(
            target_score=target_score,
            max_turns=max_turns,
            workflow_type=WorkflowType.STANDARD
        )

    def run(self, task_description: str) -> Tuple[Dict[str, Any], list[Dict[str, Any]]]:
        """Execute orchestration loop.

        Returns final *state* and *history* list.
        """
        # Use enhanced orchestrator with standard workflow
        state, history = self.enhanced_orchestrator.run(task_description)
        return state, history

    def _policy(self, state: Dict[str, Any]) -> Tuple[str | None, str]:
        """Deterministic policy function.

        Returns (next_role_name | None, reason)
        """
        # Delegate to enhanced orchestrator's policy
        return self.enhanced_orchestrator._adaptive_policy(state) 