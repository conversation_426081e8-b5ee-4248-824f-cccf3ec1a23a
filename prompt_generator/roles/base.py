"""
Base classes and utilities for role implementations.

Oner: Defines the abstract base class and common utilities that all role
implementations must inherit from and use.

Pyramid Principle:
- Main: Abstract base class definition and common interfaces
- Secondary: LLM calling utilities and error handling
- Tertiary: Mock response generation and configuration integration

Requirements:
- Abstract base class for all roles
- Standardized output format with RoleOutput dataclass
- LLM calling utilities with error handling
- Mock response generation for demo mode
- Configuration integration for API keys and models
"""

from __future__ import annotations

import abc
from dataclasses import dataclass, field
import os
import sys
import time
from typing import Any, Dict, List, Optional

# Local import guarded to avoid circularity – resolved at runtime
from ..config import settings
from pydantic import BaseModel
from openai import OpenAI
# OpenAI v1.0+ uses different import paths
from openai import (
    AuthenticationError,
    RateLimitError, 
    APIConnectionError,
    APIError,
    BadRequestError
)

# Global variable to track last API call time
_last_api_call_time = 0.0
_api_call_delay = 1.0  # Delay in seconds between API calls

def _rate_limit_delay():
    """Add delay between API calls to avoid rate limiting."""
    global _last_api_call_time
    current_time = time.time()
    time_since_last_call = current_time - _last_api_call_time
    
    if time_since_last_call < _api_call_delay:
        delay_needed = _api_call_delay - time_since_last_call
        print(f"[RATE LIMIT] Waiting {delay_needed:.2f}s to avoid rate limiting...")
        time.sleep(delay_needed)
    
    _last_api_call_time = time.time()

__all__ = [
    "BaseRole",
    "RoleOutput", 
    "Role",
]


@dataclass
class RoleOutput:
    """Standardised output from a role evaluation.

    Each role may add arbitrary keys to update shared *state* but MUST include
    a `log` key with human-readable notes explaining what it did. That ensures
    the orchestrator can always display something meaningful.
    
    Examples:
        >>> output = RoleOutput({"draft": "New prompt", "log": "Created draft"})
        >>> output.to_state_update()
        {'draft': 'New prompt', 'log': 'Created draft'}
        
        >>> empty_output = RoleOutput()
        >>> empty_output.to_state_update()
        {}
    """

    data: Dict[str, Any] = field(default_factory=dict)

    def to_state_update(self) -> Dict[str, Any]:
        """Return key/value pairs that will be merged into orchestration state."""
        return self.data


class BaseRole(abc.ABC):
    """Abstract base class every role must subclass."""

    name: str

    def __init__(self, name: str | None = None):
        self.name = name or self.__class__.__name__

    # System prompt inserted before user messages for LLM-based roles
    system_prompt: str | None = None

    # OpenAI model identifier. Subclasses can override via __init__ later
    model: str = "" # Will be set from config based on role name

    # Temperature for generation (where applicable) - now configurable per role
    temperature: float = 0.7  # Default fallback, will be overridden by config

    # Max tokens for generation - will be overridden by config
    max_tokens: int = 0  # Will be set from config based on role name

    @abc.abstractmethod
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        """Run the role on current *state* and return updates.

        Subclasses should NOT mutate *state* in-place; instead return a
        RoleOutput whose ``data`` will be merged by the orchestrator.
        """

    # Helper -----------------------------------------------------------------
    def _call_llm(self, messages: list[dict[str, str]], **kwargs) -> str:
        """Utility wrapper to send ChatCompletion request with rate limiting.

        Raises an error if API is unavailable or misconfigured.
        """
        if not settings.openai_api_key or len(settings.openai_api_key) < 20:
            raise RuntimeError("OpenAI API key is missing or invalid. Please set it in your config.yaml or environment.")
        
        # Add rate limiting delay
        _rate_limit_delay()
        
        try:
            client = OpenAI(api_key=settings.openai_api_key)
            
            # Get role-specific model, token, and temperature configuration
            model_to_use = kwargs.get("model", settings.get_role_model(self.name))
            max_tokens = kwargs.get("max_tokens", settings.get_role_tokens(self.name))
            temperature = kwargs.get("temperature", settings.get_role_temperature(self.name))
            print(f"[DEBUG] Role: {self.name} | Loaded temperature from config: {settings.get_role_temperature(self.name)} | Model: {model_to_use}")
            print(f"[LLM CALL] Role: {self.name} | Model: {model_to_use} | max_tokens: {max_tokens} | temperature: {temperature}")
            
            # Use max_completion_tokens for newer models, max_tokens for older ones
            if model_to_use.startswith("o4-") or model_to_use.startswith("gpt-4o"):
                response = client.chat.completions.create(
                    model=model_to_use,
                    messages=messages,
                    temperature=temperature,
                    max_completion_tokens=max_tokens,
                )
            else:
                response = client.chat.completions.create(
                    model=model_to_use,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
            return response.choices[0].message.content.strip()
            
        except (AuthenticationError, BadRequestError) as e:
            print(f"API Error: {str(e)}")
            raise
        except (APIError, APIConnectionError, RateLimitError) as e:
            print(f"API Error: {str(e)}")
            raise
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            raise
    # ------------------------------------------------------------------------ 

class Role(BaseModel):
    name: str
    description: str
    model: str = ""  # Will be set from config based on role name
    temperature: float = 0.7  # Default fallback, will be overridden by config
    max_tokens: int = 0  # Will be set from config based on role name
    system_prompt: str = ""
    
    model_config = {"strict": True, "extra": "forbid"}
    __schema_version__ = "1.0.0"  # Track schema changes for compatibility
    
    def __init__(self, **data: Any) -> None:
        super().__init__(**data)
        self.client = OpenAI(api_key=settings.openai_api_key)
    
    def _call_llm(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Call the LLM with the given messages and rate limiting."""
        if not settings.openai_api_key or len(settings.openai_api_key) < 20:
            raise RuntimeError("OpenAI API key is missing or invalid. Please set it in your config.yaml or environment.")
        
        # Add rate limiting delay
        _rate_limit_delay()
        
        try:
            # Get role-specific model, token, and temperature configuration
            model_to_use = kwargs.get("model", self.model)
            max_tokens = kwargs.get("max_tokens", settings.get_role_tokens(self.name))
            temperature = kwargs.get("temperature", settings.get_role_temperature(self.name))
            
            # Use max_completion_tokens for newer models, max_tokens for older ones
            if model_to_use.startswith("o4-") or model_to_use.startswith("gpt-4o"):
                response = self.client.chat.completions.create(
                    model=model_to_use,
                    messages=messages,
                    temperature=temperature,
                    max_completion_tokens=max_tokens,
                )
            else:
                response = self.client.chat.completions.create(
                    model=model_to_use,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
            return response.choices[0].message.content
            
        except (AuthenticationError, BadRequestError) as e:
            print(f"API Error: {str(e)}")
            raise
        except (APIError, APIConnectionError, RateLimitError) as e:
            print(f"API Error: {str(e)}")
            raise
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            raise
    
    def eval(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate the current state and return the next state."""
        raise NotImplementedError("Subclasses must implement eval()") 