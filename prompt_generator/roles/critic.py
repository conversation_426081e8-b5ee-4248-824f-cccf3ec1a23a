"""Critic role - evaluates quality of the prompt and suggests improvements.

Oner: Evaluates prompt quality using standardized criteria and provides
actionable feedback for improvement with numerical scoring.

Pyramid Principle:
- Main: Quality evaluation and scoring system
- Secondary: Feedback generation and score extraction
- Tertiary: Score validation and range enforcement

Requirements:
- Standardized evaluation criteria (clarity, specificity, context, format, constraints)
- Numerical scoring system (0-10 scale)
- Actionable feedback generation
- Score extraction and validation
- Range enforcement and error handling
"""

import re
from typing import Any, Dict, Optional

from .base import BaseRole, RoleOutput


class Critic(BaseRole):
    system_prompt = """You are a Prompt Engineering Critic with expertise in creating effective prompts for LLMs.\nYour task is to evaluate the quality of a prompt based on the following criteria:\n\n1. Clarity: Is the prompt clear about what is being asked?\n2. Specificity: Does it include enough details to guide the response?\n3. Context: Does it provide necessary background information?\n4. Format guidance: Does it specify how the answer should be structured?\n5. Constraints: Does it include relevant constraints or guardrails?\n\nYou MUST assign a score from 0-10 with one decimal place (e.g., 7.5).\nYou MUST include \"Score: X.Y/10\" at the end of your feedback.\n\nIf you ever suggest a revised prompt, you MUST use the following format, with NO extra commentary or formatting outside these tags:\n   <s>SYSTEM_MESSAGE</s>\n   <user>USER_MESSAGE</user>\nWhere SYSTEM_MESSAGE must declare role, tone, constraints, and USER_MESSAGE must include task, placeholders, output format marker, and constraints."""

    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        # 1. Check if draft exists for evaluation
        draft = state.get("draft", "")
        if not draft:
            return RoleOutput({"log": "Critic skipped – no draft to evaluate."})

        # 2. Prepare user prompt with task and draft
        user_prompt = (
            f"Task: {state.get('task', 'No task provided')}\n\n"
            f"Draft prompt to evaluate:\n\n{draft}\n\n"
            f"Please evaluate this prompt on a scale of 0-10 (with one decimal place), "
            f"and provide specific feedback for improvement. End with 'Score: X.Y/10'."
        )
        
        # 3. Call LLM for evaluation
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        feedback = self._call_llm(messages)
        
        # 4. Extract score using regex - look for "Score: X.Y/10" pattern
        score_match = re.search(r"Score:\s*(\d+\.\d+)\/10", feedback)
        score: Optional[float] = None
        
        # 5. Parse and validate score
        if score_match:
            try:
                score = float(score_match.group(1))
            except (ValueError, IndexError):
                # Handle invalid score format
                print("Warning: Could not parse score from critic feedback")
                score = 5.0  # Default score for invalid feedback
        else:
            # If score not found, use a fallback
            print("Warning: No score found in critic feedback")
            score = 5.0  # Default score for missing feedback
            feedback += "\nScore: 7.0/10"
        
        # 6. Validate score range and clamp if necessary
        if score is not None:
            if score < 0 or score > 10:
                print(f"Warning: Score {score} out of range 0-10, clamping")
                score = max(0, min(10, score))
        
        # 7. Return evaluation results
        return RoleOutput({
            "critic_feedback": feedback,
            "critic_score": score,
            "log": f"Critic evaluated prompt. Score: {score}/10"
        }) 