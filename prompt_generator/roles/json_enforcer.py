"""JSONEnforcer role – ensures consistent JSON output format for all prompts.

Oner: Converts final prompts into a standardized JSON format with consistent
structure for easy parsing and integration with downstream systems.

Pyramid Principle:
- Main: JSON format enforcement and standardization
- Secondary: Metadata extraction and validation
- Tertiary: Schema validation and error handling

Requirements:
- Consistent JSON structure for all prompts
- Metadata extraction and inclusion
- Schema validation and error handling
- Backward compatibility with existing prompts
- Extensible format for future enhancements
"""

import json
import re
from typing import Any, Dict, Optional
from dataclasses import dataclass

from .base import BaseRole, RoleOutput


@dataclass
class PromptMetadata:
    """Metadata extracted from the prompt."""
    role: str
    tone: str
    domain: Optional[str]
    output_format: str
    constraints: list[str]
    placeholders: list[str]
    estimated_tokens: int
    quality_score: Optional[float]


class JSONEnforcer(BaseRole):
    """Enforces consistent JSON output format for all prompts."""
    
    def __init__(self):
        super().__init__()
        self.required_fields = [
            "system_message",
            "user_message", 
            "metadata",
            "version"
        ]
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        """Convert final prompt to standardized JSON format."""
        draft = state.get("draft", "")
        if not draft:
            return RoleOutput({"log": "JSONEnforcer skipped – no draft to format."})
        
        # Check if draft is severely incomplete
        if len(draft.strip()) < 50:
            return RoleOutput({
                "draft": draft,
                "json_valid": False,
                "json_errors": ["Draft is too short or incomplete"],
                "log": "JSONEnforcer failed - draft is too short or incomplete"
            })
        
        # Extract metadata from the prompt
        metadata = self._extract_metadata(draft, state)
        
        # Parse system and user messages
        system_message, user_message = self._parse_messages(draft)
        
        # Check if messages are too short (indicating truncation)
        if len(system_message.strip()) < 20 or len(user_message.strip()) < 20:
            return RoleOutput({
                "draft": draft,
                "json_valid": False,
                "json_errors": ["Messages appear to be truncated"],
                "log": "JSONEnforcer failed - messages appear to be truncated"
            })
        
        # Create standardized JSON structure
        json_prompt = self._create_json_structure(
            system_message=system_message,
            user_message=user_message,
            metadata=metadata,
            state=state
        )
        
        # Validate JSON structure
        validation_result = self._validate_json_structure(json_prompt)
        
        return RoleOutput({
            "draft": json.dumps(json_prompt, indent=2),
            "json_prompt": json_prompt,
            "json_valid": validation_result["valid"],
            "json_errors": validation_result.get("errors", []),
            "log": f"JSONEnforcer formatted prompt. Valid: {validation_result['valid']}"
        })
    
    def _extract_metadata(self, draft: str, state: Dict[str, Any]) -> PromptMetadata:
        """Extract metadata from the prompt content."""
        # Extract role and tone
        role_match = re.search(r'You are\s+([^,\.]+)', draft, re.IGNORECASE)
        role = role_match.group(1).strip() if role_match else "Assistant"
        
        tone_match = re.search(r'(professional|friendly|formal|casual|technical)', draft, re.IGNORECASE)
        tone = tone_match.group(1).lower() if tone_match else "professional"
        
        # Extract domain from state or content
        domain = state.get("domain") or self._extract_domain_from_content(draft)
        
        # Extract output format
        output_format = self._extract_output_format(draft)
        
        # Extract constraints
        constraints = self._extract_constraints(draft)
        
        # Extract placeholders
        placeholders = re.findall(r'\{\{[^}]+\}\}', draft)
        
        # Estimate tokens
        estimated_tokens = len(draft.split()) * 1.3  # Rough estimation
        
        # Get quality score if available
        quality_score = state.get("critic_score")
        
        return PromptMetadata(
            role=role,
            tone=tone,
            domain=domain,
            output_format=output_format,
            constraints=constraints,
            placeholders=placeholders,
            estimated_tokens=int(estimated_tokens),
            quality_score=quality_score
        )
    
    def _extract_domain_from_content(self, draft: str) -> Optional[str]:
        """Extract domain from prompt content."""
        domain_keywords = {
            "financial": ["financial", "investment", "valuation", "ebitda", "roi", "cash flow"],
            "technical": ["api", "code", "software", "technical", "development", "system"],
            "medical": ["medical", "health", "patient", "clinical", "diagnosis", "treatment"],
            "legal": ["legal", "compliance", "regulatory", "contract", "law", "regulation"],
            "creative": ["creative", "brand", "marketing", "content", "storytelling"],
            "educational": ["educational", "teaching", "learning", "instruction", "curriculum"]
        }
        
        draft_lower = draft.lower()
        for domain, keywords in domain_keywords.items():
            if any(keyword in draft_lower for keyword in keywords):
                return domain
        
        return None
    
    def _extract_output_format(self, draft: str) -> str:
        """Extract output format from prompt."""
        if "JSON_OUTPUT:" in draft:
            return "json"
        elif "MARKDOWN_OUTPUT:" in draft:
            return "markdown"
        elif "table" in draft.lower() or "tabular" in draft.lower():
            return "table"
        elif "code" in draft.lower() or "programming" in draft.lower():
            return "code"
        else:
            return "text"
    
    def _extract_constraints(self, draft: str) -> list[str]:
        """Extract constraints from prompt."""
        constraints = []
        
        # Look for constraint patterns
        constraint_patterns = [
            r"must\s+([^\.]+)",
            r"should\s+([^\.]+)", 
            r"constraint[s]?\s*:\s*([^\.]+)",
            r"requirement[s]?\s*:\s*([^\.]+)"
        ]
        
        for pattern in constraint_patterns:
            matches = re.findall(pattern, draft, re.IGNORECASE)
            constraints.extend([match.strip() for match in matches])
        
        # Add common constraints based on content
        if "accurate" in draft.lower() or "precise" in draft.lower():
            constraints.append("accuracy_required")
        if "cite" in draft.lower() or "source" in draft.lower():
            constraints.append("citations_required")
        
        return list(set(constraints))  # Remove duplicates
    
    def _parse_messages(self, draft: str) -> tuple[str, str]:
        """Parse system and user messages from draft."""
        # Check if draft is complete (has proper closing tags)
        if not self._is_complete_prompt(draft):
            # Try to fix incomplete prompt
            fixed_draft = self._fix_incomplete_prompt(draft)
            if fixed_draft != draft:
                draft = fixed_draft
                print(f"[JSONEnforcer] Fixed incomplete prompt")
        
        # Try to find system and user message separators
        system_match = re.search(r'<s>([\s\S]*?)</s>', draft)
        user_match = re.search(r'<user>([\s\S]*?)</user>', draft)
        
        if system_match and user_match:
            system_message = system_match.group(1).strip()
            user_message = user_match.group(1).strip()
        else:
            # Fallback: try to split on common patterns
            parts = draft.split('\n\n', 1)
            if len(parts) >= 2:
                system_message = parts[0].strip()
                user_message = parts[1].strip()
            else:
                # Last resort: treat as user message only
                system_message = "You are a helpful assistant."
                user_message = draft.strip()
        
        return system_message, user_message
    
    def _is_complete_prompt(self, draft: str) -> bool:
        """Check if the prompt has proper opening and closing tags."""
        has_system_open = '<s>' in draft
        has_system_close = '</s>' in draft
        has_user_open = '<user>' in draft
        has_user_close = '</user>' in draft
        
        # Check if tags are properly paired
        system_open_count = draft.count('<s>')
        system_close_count = draft.count('</s>')
        user_open_count = draft.count('<user>')
        user_close_count = draft.count('</user>')
        
        return (has_system_open and has_system_close and 
                has_user_open and has_user_close and
                system_open_count == system_close_count and
                user_open_count == user_close_count)
    
    def _fix_incomplete_prompt(self, draft: str) -> str:
        """Attempt to fix an incomplete prompt by adding missing closing tags."""
        fixed_draft = draft
        
        # Add missing </s> tag if <s> exists but </s> doesn't
        if '<s>' in draft and '</s>' not in draft:
            # Find the last <s> tag and add </s> before the next <user> tag
            last_s_pos = draft.rfind('<s>')
            user_pos = draft.find('<user>')
            if user_pos > last_s_pos:
                fixed_draft = (draft[:user_pos] + '</s>\n' + draft[user_pos:])
        
        # Add missing </user> tag if <user> exists but </user> doesn't
        if '<user>' in draft and '</user>' not in draft:
            fixed_draft += '\n</user>'
        
        return fixed_draft
    
    def _create_json_structure(self, system_message: str, user_message: str, 
                             metadata: PromptMetadata, state: Dict[str, Any]) -> Dict[str, Any]:
        """Create standardized JSON structure."""
        # Debug: Check what critic_score we're getting
        critic_score = state.get("critic_score", 0)
        print(f"[JSONEnforcer DEBUG] critic_score from state: {critic_score}")
        print(f"[JSONEnforcer DEBUG] state keys: {list(state.keys())}")
        
        return {
            "version": "1.0.0",
            "timestamp": state.get("timestamp", ""),
            "workflow_type": state.get("workflow_type", "standard"),
            "system_message": system_message,
            "user_message": user_message,
            "metadata": {
                "role": metadata.role,
                "tone": metadata.tone,
                "domain": metadata.domain,
                "output_format": metadata.output_format,
                "constraints": metadata.constraints,
                "placeholders": metadata.placeholders,
                "estimated_tokens": metadata.estimated_tokens,
                "quality_score": metadata.quality_score,
                "token_savings": state.get("token_saving_pct", 0),
                "qa_passed": state.get("qa_passed", False),
                "domain_optimized": state.get("domain_optimized", False)
            },
            "execution_info": {
                "total_turns": len(state.get("history", [])),
                "roles_used": [h.get("role") for h in state.get("history", [])],
                "termination_reason": state.get("termination_reason", ""),
                "target_score": state.get("target_score", 8.0),
                "final_score": critic_score
            }
        }
    
    def _validate_json_structure(self, json_prompt: Dict[str, Any]) -> Dict[str, Any]:
        """Validate JSON structure and required fields."""
        errors = []
        
        # Check required fields
        for field in self.required_fields:
            if field not in json_prompt:
                errors.append(f"Missing required field: {field}")
        
        # Check metadata structure
        if "metadata" in json_prompt:
            metadata = json_prompt["metadata"]
            required_metadata = ["role", "tone", "output_format"]
            for field in required_metadata:
                if field not in metadata:
                    errors.append(f"Missing required metadata field: {field}")
        
        # Check message content
        if "system_message" in json_prompt and not json_prompt["system_message"].strip():
            errors.append("System message is empty")
        
        if "user_message" in json_prompt and not json_prompt["user_message"].strip():
            errors.append("User message is empty")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        } 