"""TokenOptimizer – suggests ways to reduce token usage in the prompt.

Oner: Optimizes prompt token usage while preserving semantic intent and
critical information, applying optimization only when significant savings are available.

Pyramid Principle:
- Main: Token optimization with semantic preservation
- Secondary: Savings threshold evaluation and application
- Tertiary: Token estimation and metrics calculation

Requirements:
- Token usage reduction without semantic loss
- Configurable savings thresholds
- Token estimation and metrics calculation
- Preservation of critical information and constraints
- Clear feedback on optimization decisions
"""

import re
from typing import Any, Dict, Optional

from ..config import load_config_safe
from .base import BaseRole, RoleOutput

# Load configuration
config = load_config_safe()
token_optimizer_config = config.get("token_optimizer", {})

# Load thresholds if configured, otherwise None (indicating not configured)
MIN_SAVINGS_PCT = token_optimizer_config.get("min_savings_pct") if "min_savings_pct" in token_optimizer_config else None
MIN_TOKENS_SAVED = token_optimizer_config.get("min_tokens_saved") if "min_tokens_saved" in token_optimizer_config else None
MAX_SAVINGS_PCT = token_optimizer_config.get("max_savings_pct") if "max_savings_pct" in token_optimizer_config else None

# If no thresholds are configured, set defaults
if MIN_SAVINGS_PCT is None and MIN_TOKENS_SAVED is None:
    MIN_SAVINGS_PCT = 10  # Default 10% if nothing is configured


class TokenOptimizer(BaseRole):
    system_prompt = (
        "You are **Prompt-Compressor-General-v1**, a token-efficiency specialist.\n"
        "=============  TASK  =============\n"
        "Rewrite the draft prompt so it uses as few tokens as possible **without** changing:\n"
        "• semantic intent\n"
        "• role titles and wrapper tags (<s>...</s>, <user>...</user>, placeholders {{…}})\n"
        "• explicit constraints (domain accuracy, refusal rules)\n"
        "• expected-output description and section headers\n"
        "• citation-style instruction\n"
        "\n"
        "=============  RULES  =============\n"
        "1. Remove superfluous words, redundancies, and filler phrases.\n"
        "2. Keep bullets or numbered lists when they improve clarity ≥ tokens saved.\n"
        "3. Do **NOT** introduce abbreviations that a downstream LLM might misinterpret.\n"
        "4. Preserve line breaks that delimit sections; do not collapse everything into one paragraph.\n"
        "5. Return **only** the compressed prompt pair—no commentary, no JSON.\n"
        "6. Preserve critical meaning - do not remove essential information just to save tokens.\n"
        "\n"
        "=============  OUTPUT FORMAT REQUIREMENTS  =============\n"
        "You MUST return the prompt in this EXACT format, with NO extra commentary, explanation, or formatting outside these tags:\n"
        "   <s>SYSTEM_MESSAGE</s>\n"
        "   <user>USER_MESSAGE</user>\n"
        "Where SYSTEM_MESSAGE must declare:\n"
        "   • Role and expertise\n"
        "   • Tone and style\n"
        "   • Key constraints and requirements\n"
        "And USER_MESSAGE must include:\n"
        "   • Clear task description\n"
        "   • Placeholders in {{double_curly_braces}}\n"
        "   • Output format specification (e.g., JSON_OUTPUT: if JSON is required)\n"
        "   • Specific constraints and guardrails\n"
        "Do NOT add any additional text, comments, or formatting outside these tags.\n"
    )

    def estimate_tokens(self, text: str) -> int:
        # crude token estimate: 4 chars ≈ 1 token
        return max(1, len(text) // 4)

    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        # 1. Get current draft from state
        draft = state.get("draft")
        if not draft:
            return RoleOutput({"log": "TokenOptimizer skipped – no draft."})

        # 2. Calculate original token count
        original_tokens = self.estimate_tokens(draft)
        
        # 3. Prepare messages for LLM call
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": draft},
        ]
        
        # 4. Call LLM for compression
        compressed = self._call_llm(messages)
        compressed_tokens = self.estimate_tokens(compressed)
        
        # 5. Calculate savings metrics
        tokens_saved = original_tokens - compressed_tokens
        saving_pct = (tokens_saved / original_tokens) * 100
        
        # 6. Check which thresholds to apply
        should_apply_optimization = True
        threshold_messages = []
        
        # 7. Check percentage threshold if configured
        if MIN_SAVINGS_PCT is not None and saving_pct < MIN_SAVINGS_PCT:
            should_apply_optimization = False
            threshold_messages.append(f"{saving_pct:.1f}% below {MIN_SAVINGS_PCT}% threshold")
        
        # 8. Check absolute token threshold if configured
        if MIN_TOKENS_SAVED is not None and tokens_saved < MIN_TOKENS_SAVED:
            should_apply_optimization = False
            threshold_messages.append(f"{tokens_saved} tokens below {MIN_TOKENS_SAVED} threshold")
            
        # 9. Check maximum savings threshold if configured
        if MAX_SAVINGS_PCT is not None and saving_pct > MAX_SAVINGS_PCT:
            should_apply_optimization = False
            threshold_messages.append(f"{saving_pct:.1f}% exceeds {MAX_SAVINGS_PCT}% maximum - probable content loss")
            
        # 10. Apply optimization if thresholds are met
        if not should_apply_optimization:
            return RoleOutput({
                "token_saving_pct": saving_pct,
                "tokens_saved": tokens_saved,
                "log": f"TokenOptimizer: No changes applied. {', '.join(threshold_messages)}.",
            })

        # 11. Return optimized draft
        return RoleOutput({
            "draft": compressed,
            "token_saving_pct": saving_pct,
            "tokens_saved": tokens_saved,
            "log": f"TokenOptimizer reduced by {tokens_saved} tokens ({saving_pct:.1f}%).",
        }) 