"""DomainSpecialist role – provides domain-specific expertise and constraints.

Oner: Ensures prompts are optimized for specific domains with appropriate
terminology, constraints, and best practices for that field.

Pyramid Principle:
- Main: Domain-specific prompt optimization
- Secondary: Domain constraint enforcement and terminology validation
- Tertiary: Cross-domain compatibility and integration

Requirements:
- Domain-specific terminology and constraints
- Industry best practices integration
- Compliance and regulatory requirements
- Cross-domain compatibility
- Specialized output formats per domain
"""

from typing import Any, Dict, List, Optional
from enum import Enum

from .base import BaseRole, RoleOutput


class Domain(Enum):
    """Supported domains with their specific requirements."""
    FINANCIAL = "financial"
    TECHNICAL = "technical" 
    CREATIVE = "creative"
    LEGAL = "legal"
    MEDICAL = "medical"
    EDUCATIONAL = "educational"
    MARKETING = "marketing"
    RESEARCH = "research"
    SCIENTIFIC = "scientific"
    BUSINESS = "business"
    HEALTHCARE = "healthcare"
    ENVIRONMENTAL = "environmental"
    SOCIAL_MEDIA = "social_media"
    ECOMMERCE = "ecommerce"
    NONPROFIT = "nonprofit"
    GOVERNMENT = "government"
    ENTERTAINMENT = "entertainment"
    SPORTS = "sports"
    TRAVEL = "travel"
    FOOD = "food"


class DomainSpecialist(BaseRole):
    """Specialized role for domain-specific prompt optimization."""
    
    def __init__(self, domain: Domain = None):
        super().__init__()
        self.domain = domain or Domain.FINANCIAL  # Default to financial if none specified
        self.domain_configs = self._get_domain_configs()
    
    def _get_domain_configs(self) -> Dict[str, Any]:
        """Get domain-specific configurations and constraints."""
        configs = {
            Domain.FINANCIAL: {
                "terminology": ["GAAP", "IFRS", "EBITDA", "ROI", "DCF", "LTV", "ARR"],
                "constraints": [
                    "Never invent financial data",
                    "Always cite sources for market data",
                    "Use precise decimal places for financial figures",
                    "Include risk disclaimers where appropriate"
                ],
                "output_formats": ["JSON", "Markdown", "Excel"],
                "compliance": ["SOX", "GDPR", "Financial regulations"],
                "system_prompt": """You are a Financial Analysis Expert specializing in:
- Financial modeling and valuation
- Market analysis and research
- Due diligence and risk assessment
- Regulatory compliance and reporting

You must:
- Use precise financial terminology (GAAP/IFRS)
- Never invent or round financial figures
- Always cite data sources
- Include appropriate risk disclaimers"""
            },
            Domain.TECHNICAL: {
                "terminology": ["API", "SDK", "SDLC", "CI/CD", "DevOps", "Microservices"],
                "constraints": [
                    "Include version numbers for technical references",
                    "Specify platform/technology requirements",
                    "Provide code examples where appropriate",
                    "Include error handling considerations"
                ],
                "output_formats": ["JSON", "Markdown", "Code blocks"],
                "compliance": ["Security best practices", "Data privacy"],
                "system_prompt": """You are a Technical Architecture Expert specializing in:
- Software development and system design
- API design and documentation
- Security and performance optimization
- Technology stack recommendations

You must:
- Include specific version numbers and platforms
- Provide code examples where relevant
- Consider security and scalability implications
- Follow industry best practices and standards"""
            },
            Domain.CREATIVE: {
                "terminology": ["Brand voice", "Tone", "Persona", "Storytelling", "Engagement"],
                "constraints": [
                    "Maintain brand voice consistency",
                    "Consider target audience demographics",
                    "Include emotional appeal elements",
                    "Balance creativity with clarity"
                ],
                "output_formats": ["Markdown", "HTML", "Plain text"],
                "compliance": ["Brand guidelines", "Copyright"],
                "system_prompt": """You are a Creative Content Specialist specializing in:
- Brand voice and tone development
- Storytelling and narrative structure
- Audience engagement and emotional appeal
- Creative campaign development

You must:
- Maintain consistent brand voice and tone
- Consider target audience demographics
- Include emotional and engagement elements
- Balance creativity with clear communication"""
            },
            Domain.MEDICAL: {
                "terminology": ["Diagnosis", "Treatment", "Symptoms", "Prognosis", "Clinical"],
                "constraints": [
                    "Use precise medical terminology",
                    "Always cite medical sources",
                    "Include appropriate disclaimers",
                    "Consider patient privacy and HIPAA"
                ],
                "output_formats": ["JSON", "Markdown", "Medical reports"],
                "compliance": ["HIPAA", "Medical regulations"],
                "system_prompt": """You are a Medical Information Specialist specializing in:
- Medical research and analysis
- Clinical documentation
- Patient education materials
- Healthcare compliance

You must:
- Use precise medical terminology
- Always cite medical sources
- Include appropriate disclaimers
- Consider patient privacy and HIPAA"""
            },
            Domain.EDUCATIONAL: {
                "terminology": ["Curriculum", "Learning objectives", "Assessment", "Pedagogy"],
                "constraints": [
                    "Use clear, accessible language",
                    "Include learning objectives",
                    "Provide examples and explanations",
                    "Consider different learning styles"
                ],
                "output_formats": ["Markdown", "HTML", "Educational materials"],
                "compliance": ["Educational standards", "Accessibility"],
                "system_prompt": """You are an Educational Content Specialist specializing in:
- Curriculum development and design
- Learning material creation
- Educational assessment and evaluation
- Student engagement strategies

You must:
- Use clear, accessible language
- Include learning objectives
- Provide examples and explanations
- Consider different learning styles"""
            }
        }
        return configs.get(self.domain, configs[Domain.FINANCIAL])
    
    def _extract_domain_from_content(self, draft: str) -> Optional[str]:
        """Extract domain from prompt content."""
        domain_keywords = {
            "financial": ["financial", "investment", "valuation", "ebitda", "roi", "cash flow", "revenue", "profit"],
            "technical": ["api", "code", "software", "technical", "development", "system", "programming"],
            "medical": ["medical", "health", "patient", "clinical", "diagnosis", "treatment", "healthcare"],
            "legal": ["legal", "compliance", "regulatory", "contract", "law", "regulation"],
            "creative": ["creative", "brand", "marketing", "content", "storytelling", "design"],
            "educational": ["educational", "teaching", "learning", "instruction", "curriculum", "education"],
            "business": ["business", "strategy", "management", "operations", "corporate"],
            "scientific": ["scientific", "research", "experiment", "analysis", "data", "study"],
            "environmental": ["environmental", "sustainability", "climate", "green", "eco"],
            "social_media": ["social media", "social", "platform", "engagement", "viral"],
            "ecommerce": ["ecommerce", "online", "shopping", "retail", "digital"],
            "entertainment": ["entertainment", "media", "film", "music", "gaming"],
            "sports": ["sports", "athletic", "fitness", "training", "competition"],
            "travel": ["travel", "tourism", "vacation", "destination", "trip"],
            "food": ["food", "culinary", "restaurant", "cooking", "cuisine"]
        }
        
        draft_lower = draft.lower()
        for domain, keywords in domain_keywords.items():
            if any(keyword in draft_lower for keyword in keywords):
                return domain
        
        return None
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        """Apply domain-specific optimizations to the prompt."""
        draft = state.get("draft", "")
        if not draft:
            return RoleOutput({"log": "DomainSpecialist skipped – no draft to optimize."})
        
        config = self.domain_configs
        domain_name = self.domain.value
        
        # Create domain-specific optimization prompt
        system_prompt = f"""You are a {domain_name.title()} Domain Specialist.\n\nYour task is to optimize the given prompt for the {domain_name} domain by:\n1. Ensuring appropriate {domain_name} terminology is used\n2. Adding domain-specific constraints and requirements\n3. Optimizing output format for {domain_name} use cases\n4. Including relevant compliance considerations\n\nDomain-specific requirements:\n- Terminology: {', '.join(config['terminology'])}\n- Constraints: {'; '.join(config['constraints'])}\n- Output formats: {', '.join(config['output_formats'])}\n- Compliance: {', '.join(config['compliance'])}\n\n=============  OUTPUT FORMAT REQUIREMENTS  =============\nYou MUST return the prompt in this EXACT format, with NO extra commentary, explanation, or formatting outside these tags:\n   <s>SYSTEM_MESSAGE</s>\n   <user>USER_MESSAGE</user>\nWhere SYSTEM_MESSAGE must declare:\n   • The role that will EXECUTE the task (not a prompt engineer)\n   • Tone and style for task execution\n   • Key constraints and requirements for the task\nAnd USER_MESSAGE must include:\n   • Clear task description for the executor to perform\n   • Placeholders in {{double_curly_braces}}\n   • Output format specification (e.g., JSON_OUTPUT: if JSON is required)\n   • Specific constraints and guardrails for task execution\nIMPORTANT: The SYSTEM_MESSAGE should define the role that will EXECUTE the task, not a prompt engineer.\nDo NOT add any additional text, comments, or formatting outside these tags."""

        user_prompt = f"""Please optimize this prompt for the {domain_name} domain:\n\n{draft}\n\nFocus on:\n- Using appropriate {domain_name} terminology\n- Adding domain-specific constraints\n- Optimizing for {domain_name} use cases\n- Ensuring compliance with {domain_name} requirements"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        optimized_draft = self._call_llm(messages)
        
        return RoleOutput({
            "draft": optimized_draft,
            "domain_optimized": True,
            "domain": domain_name,
            "log": f"DomainSpecialist optimized prompt for {domain_name} domain.",
        }) 