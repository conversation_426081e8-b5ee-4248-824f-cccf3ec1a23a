"""
OutputGuardian – validates that the prompt instructs the model to reply in a specific format.

Oner: Ensures prompts include proper output format markers and are wrapped in <s>...</s> and <user>...</user> tags to maintain consistency and prevent downstream formatting issues.

Pyramid Principle:
- Main: Format validation and compliance checking
- Secondary: Marker detection and feedback generation
- Tertiary: Supported format type management

Requirements:
- Output format marker validation (JSON_OUTPUT:, MARKDOWN_OUTPUT:)
- <s>...</s> and <user>...</user> tag validation
- Clear feedback on compliance status
- Extensible format type support
- Integration with editor for automatic fixing
- Deterministic validation without LLM calls

Currently we support two formats:
1. JSON_OUTPUT: signals that the model should respond with a JSON object
2. MARKDOWN_OUTPUT: signals that the model should respond with Markdown format

In production you would load an external JSON Schema and run deterministic validation, 
but this keeps the demo light.
"""

from typing import Any, Dict, List

from .base import BaseRole, RoleOutput


class OutputGuardian(BaseRole):
    allowed_markers: List[str] = ["JSON_OUTPUT:", "MARKDOWN_OUTPUT:"]

    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        draft = state.get("draft", "")
        
        # Check for required tags
        has_system = "<s>" in draft and "</s>" in draft
        has_user = "<user>" in draft and "</user>" in draft
        # Check if any of the allowed markers are present
        found_markers = [marker for marker in self.allowed_markers if marker in draft]
        passes = has_system and has_user and len(found_markers) > 0
        
        if passes:
            marker_used = found_markers[0]
            feedback = f"✔ Draft contains required tags and marker '{marker_used}'."
        else:
            feedback = f"✖ Draft is missing required <s>...</s> or <user>...</user> tags, or must include one of: {', '.join(self.allowed_markers)}."
        
        return RoleOutput({
            "output_guardian_pass": passes,
            "output_guardian_feedback": feedback,
            "log": f"OutputGuardian check -> {feedback}",
        }) 