"""
Enhanced Writer role – produces the first draft prompt based on enhanced requirements context.

This enhanced version can handle requirements documents and generate more targeted,
requirements-aware prompts.
"""

from typing import Any, Dict

from .base import BaseRole, RoleOutput


class EnhancedWriter(BaseRole):
    system_prompt = (
        "You are **Enhanced-Prompt-Architect-General-v2**, a senior LLM-prompt engineer who specializes in "
        "creating requirements-aware prompts that address specific problems, meet defined objectives, "
        "and satisfy quality standards across any domain or industry. "
        "================== GUIDING PRINCIPLES =================="
        "1. **Requirements-First** – address the specific problem statement and meet all core objectives. "
        "2. **Domain-Aware** – adapt to the specific domain (financial, technical, medical, creative, educational, etc.). "
        "3. **Quality-Focused** – meet specified accuracy and completeness thresholds. "
        "4. **Stakeholder-Conscious** – consider the needs of all identified stakeholders. "
        "5. **Constraint-Respecting** – work within all specified constraints and limitations. "
        "6. **STRICT FORMAT** – You MUST output the prompt in the following exact structure: "
        "   <s>SYSTEM_MESSAGE</s> "
        "   <user>USER_MESSAGE</user> "
        "Where SYSTEM_MESSAGE must declare: "
        "   • The role that will EXECUTE the task (not a prompt engineer) "
        "   • Tone and style for task execution "
        "   • Key constraints and requirements for the task "
        "   • Quality standards for task execution "
        "And USER_MESSAGE must include: "
        "   • Clear task description for the executor to perform "
        "   • Placeholders in {{double_curly_braces}} "
        "   • Output format specification (matching workflow expectations) "
        "   • Specific constraints and guardrails for task execution "
        "   • Success criteria for task completion "
        "7. **Flexible data handling** – provide helpful responses even with limited data, using available information constructively. "
        "8. **Citations** – default to Markdown footnotes unless otherwise specified. "
        "=================== OUTPUT FORMAT REQUIREMENTS ================"
        "You MUST return the prompt in this EXACT format: "
        "   <s>You are [TASK_EXECUTOR_ROLE], a [EXPERTISE] specializing in [DOMAIN]. "
        "   [TONE] tone. [QUALITY_STANDARDS]. [CONSTRAINTS]</s> "
        "   <user>[PROBLEM_SPECIFIC_TASK] with placeholders {{PLACEHOLDER1}}, {{PLACEHOLDER2}}. "
        "   [OUTPUT_FORMAT] [SUCCESS_CRITERIA] [CONSTRAINTS]</user> "
        "IMPORTANT: The SYSTEM_MESSAGE should define the role that will EXECUTE the task, not a prompt engineer. "
        "Do NOT add any additional text, comments, or formatting outside these tags. "
    )

    def eval(self, state: Dict[str, Any]) -> RoleOutput:    
        # If draft already exists, do nothing
        if "draft" in state:
            return RoleOutput({"log": "EnhancedWriter skipped – draft already present."})

        # Get enhanced task description if available
        enhanced_task = state.get("enhanced_task", "")
        requirements_context = state.get("requirements_context", {})
        domain_context = state.get("domain_context", {})
        quality_context = state.get("quality_context", {})
        
        # Create enhanced user prompt
        user_prompt = self._create_enhanced_user_prompt(
            state.get("task", ""),
            enhanced_task,
            requirements_context,
            domain_context,
            quality_context
        )
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        draft = self._call_llm(messages)
        return RoleOutput({
            "draft": draft,
            "log": "EnhancedWriter produced requirements-aware draft prompt.",
        })
    
    def _create_enhanced_user_prompt(self, original_task: str, enhanced_task: str, 
                                   requirements_context: Dict[str, Any], 
                                   domain_context: Dict[str, Any],
                                   quality_context: Dict[str, Any]) -> str:
        """Create an enhanced user prompt that includes requirements context."""
        
        parts = []
        
        # Original task
        parts.append(f"ORIGINAL TASK: {original_task}")
        
        # Enhanced context if available
        if enhanced_task and enhanced_task != original_task:
            parts.append(f"\nENHANCED CONTEXT:\n{enhanced_task}")
        
        # Requirements context
        if requirements_context:
            parts.append("\nREQUIREMENTS CONTEXT:")
            if requirements_context.get("problem_statement"):
                parts.append(f"Problem: {requirements_context['problem_statement']}")
            if requirements_context.get("core_objectives"):
                parts.append(f"Objectives: {', '.join(requirements_context['core_objectives'])}")
            if requirements_context.get("key_requirements"):
                parts.append(f"Requirements: {', '.join(requirements_context['key_requirements'])}")
            if requirements_context.get("stakeholders"):
                parts.append(f"Stakeholders: {', '.join(requirements_context['stakeholders'])}")
            if requirements_context.get("success_criteria"):
                parts.append(f"Success Criteria: {', '.join(requirements_context['success_criteria'])}")
            if requirements_context.get("constraints"):
                parts.append(f"Constraints: {', '.join(requirements_context['constraints'])}")
        
        # Domain context
        if domain_context:
            parts.append("\nDOMAIN CONTEXT:")
            if domain_context.get("domain"):
                parts.append(f"Domain: {domain_context['domain']}")
            if domain_context.get("complexity_level"):
                parts.append(f"Complexity: {domain_context['complexity_level']}")
            if domain_context.get("priority_level"):
                parts.append(f"Priority: {domain_context['priority_level']}")
            if domain_context.get("industry"):
                parts.append(f"Industry: {domain_context['industry']}")
        
        # Quality context
        if quality_context:
            parts.append("\nQUALITY STANDARDS:")
            if quality_context.get("accuracy_threshold"):
                parts.append(f"Accuracy Threshold: {quality_context['accuracy_threshold']}")
            if quality_context.get("completeness_score"):
                parts.append(f"Completeness Score: {quality_context['completeness_score']}")
            if quality_context.get("validation_criteria"):
                parts.append(f"Validation Criteria: {', '.join(quality_context['validation_criteria'])}")
        
        # Instructions
        parts.append("\nINSTRUCTIONS:")
        parts.append("Generate a comprehensive prompt that:")
        parts.append("1. Addresses the specific problem and objectives")
        parts.append("2. Meets all key requirements")
        parts.append("3. Is appropriate for the specified domain and complexity")
        parts.append("4. Respects all constraints and quality standards")
        parts.append("5. Considers stakeholder needs")
        parts.append("6. Includes clear success criteria")
        
        return "\n".join(parts) 