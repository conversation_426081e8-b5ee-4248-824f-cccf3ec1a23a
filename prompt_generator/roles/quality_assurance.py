"""QualityAssurance role – comprehensive testing and validation of prompts.

Oner: Performs thorough quality checks including edge case testing, bias detection,
safety validation, and performance assessment to ensure robust prompts.

Pyramid Principle:
- Main: Comprehensive quality validation and testing
- Secondary: Edge case identification and bias detection
- Tertiary: Performance metrics and safety assessment

Requirements:
- Edge case testing and validation
- Bias and fairness detection
- Safety and compliance checking
- Performance benchmarking
- Comprehensive reporting
"""

import re
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from .base import BaseRole, RoleOutput


@dataclass
class QualityReport:
    """Comprehensive quality assessment report."""
    overall_score: float
    edge_cases_passed: int
    edge_cases_failed: int
    bias_detected: bool
    safety_issues: List[str]
    performance_metrics: Dict[str, float]
    recommendations: List[str]


class QualityAssurance(BaseRole):
    """Comprehensive quality validation and testing role."""
    
    def __init__(self):
        super().__init__()
        self.edge_cases = self._get_edge_cases()
        self.bias_indicators = self._get_bias_indicators()
        self.safety_checks = self._get_safety_checks()
    
    def _get_edge_cases(self) -> List[Dict[str, str]]:
        """Define edge cases to test prompt robustness."""
        return [
            {"name": "empty_input", "input": "", "expected": "error_handling"},
            {"name": "very_long_input", "input": "x" * 1000, "expected": "truncation"},
            {"name": "special_characters", "input": "!@#$%^&*()", "expected": "handling"},
            {"name": "unicode_characters", "input": "测试中文🎉", "expected": "support"},
            {"name": "malicious_input", "input": "<script>alert('xss')</script>", "expected": "sanitization"},
            {"name": "conflicting_instructions", "input": "Be brief but also provide detailed analysis", "expected": "resolution"},
            {"name": "ambiguous_placeholders", "input": "{{data}}", "expected": "clarification"},
        ]
    
    def _get_bias_indicators(self) -> List[str]:
        """Define indicators of potential bias in prompts."""
        return [
            "gender-specific language",
            "racial or ethnic stereotypes",
            "age-based assumptions",
            "cultural bias",
            "socioeconomic assumptions",
            "geographic bias",
            "ability/disability assumptions"
        ]
    
    def _get_safety_checks(self) -> List[str]:
        """Define safety and compliance checks."""
        return [
            "data_privacy_compliance",
            "security_considerations",
            "regulatory_compliance",
            "ethical_guidelines",
            "harm_prevention",
            "transparency_requirements"
        ]
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        """Perform comprehensive quality assessment."""
        draft = state.get("draft", "")
        if not draft:
            return RoleOutput({"log": "QualityAssurance skipped – no draft to test."})
        
        # 1. Edge case testing
        edge_case_results = self._test_edge_cases(draft)
        
        # 2. Bias detection
        bias_analysis = self._detect_bias(draft)
        
        # 3. Safety validation
        safety_analysis = self._validate_safety(draft)
        
        # 4. Performance assessment
        performance_metrics = self._assess_performance(draft)
        
        # 5. Generate comprehensive report
        quality_report = self._generate_quality_report(
            edge_case_results, bias_analysis, safety_analysis, performance_metrics
        )
        
        # 6. Determine if QA passes
        qa_passed = (
            quality_report.overall_score >= 7.0 and
            quality_report.edge_cases_failed <= 2 and
            not quality_report.bias_detected and
            len(quality_report.safety_issues) <= 1
        )
        
        return RoleOutput({
            "qa_report": quality_report,
            "qa_passed": qa_passed,
            "qa_score": quality_report.overall_score,
            "log": f"QualityAssurance completed. Score: {quality_report.overall_score}/10, Passed: {qa_passed}"
        })
    
    def _test_edge_cases(self, draft: str) -> Dict[str, Any]:
        """Test prompt with various edge cases."""
        system_prompt = """You are a Prompt Testing Specialist. Test the given prompt with edge cases and report:
1. How well the prompt handles each edge case
2. Whether it provides appropriate error handling
3. Any potential issues or improvements needed

For each edge case, respond with:
- PASS: Prompt handles the case appropriately
- FAIL: Prompt has issues with this case
- IMPROVE: Prompt could be better but is acceptable

Return a JSON object with results for each edge case."""

        edge_case_inputs = [case["input"] for case in self.edge_cases]
        user_prompt = f"""Test this prompt with the following edge cases:

PROMPT:
{draft}

EDGE CASES TO TEST:
{chr(10).join([f"- {case['name']}: '{case['input']}'" for case in self.edge_cases])}

Evaluate how well the prompt handles each case and return results as JSON."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        response = self._call_llm(messages)
        
        # Parse results (simplified for demo)
        passed = 0
        failed = 0
        for case in self.edge_cases:
            if case["name"] in response.lower():
                if "pass" in response.lower():
                    passed += 1
                elif "fail" in response.lower():
                    failed += 1
        
        return {
            "passed": passed,
            "failed": failed,
            "total": len(self.edge_cases),
            "results": response
        }
    
    def _detect_bias(self, draft: str) -> Dict[str, Any]:
        """Detect potential bias in the prompt."""
        system_prompt = """You are a Bias Detection Specialist. Analyze the prompt for potential bias indicators:
1. Gender-specific language or assumptions
2. Racial, ethnic, or cultural bias
3. Age-based assumptions
4. Socioeconomic bias
5. Geographic or regional bias
6. Ability/disability assumptions

Return a JSON object with:
- bias_detected: boolean
- bias_types: list of detected bias types
- recommendations: list of improvement suggestions"""

        user_prompt = f"""Analyze this prompt for potential bias:

{draft}

Check for any language or assumptions that might introduce bias."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        response = self._call_llm(messages)
        
        # Simplified parsing for demo
        bias_detected = any(indicator in response.lower() for indicator in self.bias_indicators)
        
        return {
            "bias_detected": bias_detected,
            "analysis": response
        }
    
    def _validate_safety(self, draft: str) -> Dict[str, Any]:
        """Validate safety and compliance aspects."""
        system_prompt = """You are a Safety and Compliance Specialist. Check the prompt for:
1. Data privacy and security considerations
2. Regulatory compliance requirements
3. Ethical guidelines adherence
4. Harm prevention measures
5. Transparency requirements

Return a JSON object with:
- safety_issues: list of identified issues
- compliance_status: overall compliance assessment
- recommendations: list of safety improvements"""

        user_prompt = f"""Analyze this prompt for safety and compliance:

{draft}

Check for any safety, privacy, or compliance issues."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        response = self._call_llm(messages)
        
        # Simplified parsing for demo
        safety_issues = []
        for check in self.safety_checks:
            if check.replace("_", " ") in response.lower():
                safety_issues.append(check)
        
        return {
            "safety_issues": safety_issues,
            "analysis": response
        }
    
    def _assess_performance(self, draft: str) -> Dict[str, float]:
        """Assess performance characteristics of the prompt."""
        # Calculate basic metrics
        word_count = len(draft.split())
        char_count = len(draft)
        placeholder_count = len(re.findall(r'\{\{[^}]+\}\}', draft))
        
        # Estimate complexity score (0-10)
        complexity_score = min(10, (word_count / 100) + (placeholder_count * 2))
        
        # Estimate clarity score (0-10) - simplified
        clarity_score = 7.0  # Default quality score
        
        return {
            "word_count": word_count,
            "char_count": char_count,
            "placeholder_count": placeholder_count,
            "complexity_score": complexity_score,
            "clarity_score": clarity_score
        }
    
    def _generate_quality_report(self, edge_results: Dict, bias_results: Dict, 
                                safety_results: Dict, performance: Dict) -> QualityReport:
        """Generate comprehensive quality report."""
        # Calculate overall score
        edge_score = (edge_results["passed"] / edge_results["total"]) * 10
        bias_score = 10 if not bias_results["bias_detected"] else 5
        safety_score = 10 if len(safety_results["safety_issues"]) == 0 else 7
        performance_score = performance["clarity_score"]
        
        overall_score = (edge_score + bias_score + safety_score + performance_score) / 4
        
        recommendations = []
        if edge_results["failed"] > 0:
            recommendations.append("Improve edge case handling")
        if bias_results["bias_detected"]:
            recommendations.append("Address potential bias issues")
        if safety_results["safety_issues"]:
            recommendations.append("Address safety and compliance issues")
        if performance["complexity_score"] > 7:
            recommendations.append("Consider simplifying the prompt")
        
        return QualityReport(
            overall_score=overall_score,
            edge_cases_passed=edge_results["passed"],
            edge_cases_failed=edge_results["failed"],
            bias_detected=bias_results["bias_detected"],
            safety_issues=safety_results["safety_issues"],
            performance_metrics=performance,
            recommendations=recommendations
        ) 