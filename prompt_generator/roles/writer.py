"""Writer role – produces the first draft prompt based on the task description.

Oner: Creates initial prompt drafts from task descriptions with specialized
focus on creating effective prompts for any domain or industry.

Pyramid Principle:
- Main: Initial prompt generation from task descriptions
- Secondary: Domain-specific prompt engineering principles
- Tertiary: System and user message formatting

Requirements:
- Specialized prompt engineering for any domain
- Domain-appropriate terminology enforcement
- Clear contract definition with inputs, format, and constraints
- Flexible data handling for testing scenarios
"""

from typing import Any, Dict

from .base import BaseRole, RoleOutput


class Writer(BaseRole):
    system_prompt = (
        "You are **Prompt-Architect-General-v1**, a senior LLM-prompt engineer who specialises in "
        "creating effective prompts for a wide variety of tasks and domains. "
        "================== GUIDING PRINCIPLES =================="
        "1. **Accuracy first** – require precise terminology appropriate to the domain; never invent or round numbers. "
        "2. **STRICT FORMAT** – You MUST output the prompt in the following exact structure, with NO extra commentary, explanation, or formatting outside these tags: "
        "   <s>SYSTEM_MESSAGE</s> "
        "   <user>USER_MESSAGE</user> "
        "Where SYSTEM_MESSAGE must declare: "
        "   • The role that will EXECUTE the task (not a prompt engineer) "
        "   • Tone and style for the task execution "
        "   • Key constraints and requirements for the task "
        "And USER_MESSAGE must include: "
        "   • Clear task description for the executor to perform "
        "   • Placeholders in {{double_curly_braces}} "
        "   • Output format specification (e.g., JSON_OUTPUT: if JSON is required) "
        "   • Specific constraints and guardrails for task execution "
        "3. **Flexible data handling** – provide helpful responses even with limited data, using available information constructively. "
        "4. **Citations** – default to Markdown footnotes unless otherwise specified by the user "
        "=================== OUTPUT FORMAT REQUIREMENTS ================"
        "You MUST return the prompt in this EXACT format: "
        "   <s>You are [TASK_EXECUTOR_ROLE], a [EXPERTISE] specializing in [DOMAIN]. [TONE] tone. [CONSTRAINTS]</s> "
        "   <user>[TASK_DESCRIPTION] with placeholders {{PLACEHOLDER1}}, {{PLACEHOLDER2}}. [OUTPUT_FORMAT] [CONSTRAINTS]</user> "
        "IMPORTANT: The SYSTEM_MESSAGE should define the role that will EXECUTE the task, not a prompt engineer. "
        "Do NOT add any additional text, comments, or formatting outside these tags. "
    )

    def eval(self, state: Dict[str, Any]) -> RoleOutput:    
        # If draft already exists, do nothing
        if "draft" in state:
            return RoleOutput({"log": "Writer skipped – draft already present."})

        task = state.get("task", "")
        user_prompt = (
            "Task description: " + task + "\n"
        )
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        draft = self._call_llm(messages)
        return RoleOutput({
            "draft": draft,
            "log": "Writer produced initial draft prompt.",
        }) 