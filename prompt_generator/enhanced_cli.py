"""
Enhanced CLI for Requirements-Aware Prompt Generation.

Provides a command-line interface that can handle both simple string input
and requirements-enhanced input from the requirements document generator.
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Any, Dict, Optional

from .enhanced_orchestrator_v2 import (
    run_enhanced_orchestrator_v2,
    run_enhanced_orchestrator_v2_with_result,
    run_requirements_aware_prompt_generation,
    EnhancedOrchestrationResult
)
from .enhanced_input_handler import <PERSON>hancedInputHandler


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Enhanced Prompt Generator with Requirements Awareness",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Simple string input
  python -m prompt_generator.enhanced_cli "Write a prompt for analyzing financial data"
  
  # Enhanced input from JSON file
  python -m prompt_generator.enhanced_cli --input-file requirements.json
  
  # Requirements document input
  python -m prompt_generator.enhanced_cli --requirements-file requirements_doc.json
  
  # High-quality generation
  python -m prompt_generator.enhanced_cli "Medical diagnosis prompt" --quality-focused
        """
    )
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "task",
        nargs="?",
        help="Task description (simple string input)"
    )
    input_group.add_argument(
        "--input-file",
        type=Path,
        help="Path to JSON file with enhanced input"
    )
    input_group.add_argument(
        "--requirements-file",
        type=Path,
        help="Path to requirements document JSON file"
    )
    
    # Output options
    parser.add_argument(
        "--output-file",
        type=Path,
        help="Output file for the generated prompt"
    )
    parser.add_argument(
        "--output-format",
        choices=["prompt", "json", "detailed"],
        default="prompt",
        help="Output format (default: prompt)"
    )
    
    # Quality options
    parser.add_argument(
        "--target-score",
        type=float,
        default=8.5,
        help="Target quality score (default: 8.5)"
    )
    parser.add_argument(
        "--max-turns",
        type=int,
        default=12,
        help="Maximum number of turns (default: 12)"
    )
    parser.add_argument(
        "--quality-focused",
        action="store_true",
        help="Use quality-focused workflow"
    )
    parser.add_argument(
        "--domain",
        help="Specify domain for domain-specific optimization"
    )
    
    # Verbosity
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Verbose output"
    )
    
    args = parser.parse_args()
    
    try:
        # Prepare input data
        input_data = _prepare_input_data(args)
        
        # Run enhanced prompt generation
        result = run_requirements_aware_prompt_generation(
            input_data=input_data,
            target_score=args.target_score,
            max_turns=args.max_turns
        )
        
        # Output results
        _output_results(result, args)
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


def _prepare_input_data(args: argparse.Namespace) -> Any:
    """Prepare input data based on CLI arguments."""
    
    if args.task:
        # Simple string input
        return args.task
    
    elif args.input_file:
        # Enhanced JSON input
        with open(args.input_file, 'r') as f:
            return json.load(f)
    
    elif args.requirements_file:
        # Requirements document input
        with open(args.requirements_file, 'r') as f:
            return json.load(f)
    
    else:
        raise ValueError("No input provided")


def _output_results(result: EnhancedOrchestrationResult, args: argparse.Namespace):
    """Output results in the specified format."""
    
    if args.output_format == "prompt":
        # Output just the final prompt
        output = result.final_prompt
        
    elif args.output_format == "json":
        # Output as JSON
        output_data = {
            "prompt": result.final_prompt,
            "input_type": result.input_type.value,
            "execution_time": result.execution_time,
            "turn_count": len(result.history),
            "requirements_context": result.requirements_context,
            "domain_context": result.domain_context,
            "quality_context": result.quality_context
        }
        output = json.dumps(output_data, indent=2)
        
    elif args.output_format == "detailed":
        # Output detailed information
        output_parts = []
        output_parts.append("=" * 60)
        output_parts.append("ENHANCED PROMPT GENERATION RESULTS")
        output_parts.append("=" * 60)
        output_parts.append(f"Input Type: {result.input_type.value}")
        output_parts.append(f"Execution Time: {result.execution_time:.2f}s")
        output_parts.append(f"Turn Count: {len(result.history)}")
        output_parts.append("")
        
        if result.requirements_context:
            output_parts.append("REQUIREMENTS CONTEXT:")
            for key, value in result.requirements_context.items():
                if value:
                    output_parts.append(f"  {key}: {value}")
            output_parts.append("")
        
        if result.domain_context:
            output_parts.append("DOMAIN CONTEXT:")
            for key, value in result.domain_context.items():
                if value:
                    output_parts.append(f"  {key}: {value}")
            output_parts.append("")
        
        output_parts.append("GENERATED PROMPT:")
        output_parts.append("-" * 40)
        output_parts.append(result.final_prompt)
        output_parts.append("-" * 40)
        
        if args.verbose:
            output_parts.append("")
            output_parts.append("EXECUTION HISTORY:")
            for entry in result.history:
                output_parts.append(f"  Turn {entry['turn']}: {entry['role']} - {entry['reason']}")
        
        output = "\n".join(output_parts)
    
    # Write to file or stdout
    if args.output_file:
        with open(args.output_file, 'w') as f:
            f.write(output)
        print(f"Results written to {args.output_file}")
    else:
        print(output)


if __name__ == "__main__":
    main() 