"""Analytics and performance monitoring for the prompt generator system.

Oner: Provides comprehensive analytics and performance monitoring capabilities
to track role effectiveness, workflow performance, and optimization insights.

Pyramid Principle:
- Main: Performance metrics collection and analysis
- Secondary: Role effectiveness tracking and optimization insights
- Tertiary: Workflow comparison and trend analysis

Requirements:
- Performance metrics collection
- Role effectiveness analysis
- Workflow comparison capabilities
- Optimization insights generation
- Trend analysis and reporting
"""

from __future__ import annotations

import json
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

from .config import load_config_safe


@dataclass
class RoleMetrics:
    """Metrics for individual role performance."""
    role_name: str
    execution_count: int
    average_execution_time: float
    success_rate: float
    quality_improvement: float
    token_savings: float
    domain_optimizations: int


@dataclass
class WorkflowMetrics:
    """Metrics for workflow performance."""
    workflow_type: str
    total_executions: int
    average_turns: float
    average_quality_score: float
    success_rate: float
    average_execution_time: float
    role_effectiveness: Dict[str, RoleMetrics]


@dataclass
class OptimizationInsight:
    """Insight about optimization opportunities."""
    insight_type: str
    description: str
    impact_score: float
    recommendation: str
    applicable_workflows: List[str]


class AnalyticsEngine:
    """Comprehensive analytics engine for performance monitoring and optimization."""
    
    def __init__(self, metrics_file: Optional[Path] = None):
        self.metrics_file = metrics_file or Path("prompt_generator_metrics.json")
        self.metrics = self._load_metrics()
        self.session_start = time.time()
        self.current_session = {
            "start_time": datetime.now().isoformat(),
            "executions": [],
            "role_performance": {},
            "workflow_performance": {}
        }
    
    def _load_metrics(self) -> Dict[str, Any]:
        """Load existing metrics from file."""
        if self.metrics_file.exists():
            try:
                with open(self.metrics_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Warning: Could not load metrics file: {e}")
        return {
            "executions": [],
            "role_metrics": {},
            "workflow_metrics": {},
            "optimization_insights": []
        }
    
    def _save_metrics(self):
        """Save metrics to file."""
        try:
            with open(self.metrics_file, 'w') as f:
                json.dump(self.metrics, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save metrics: {e}")
    
    def record_execution(self, 
                        task_description: str,
                        workflow_type: str,
                        state: Dict[str, Any],
                        history: List[Dict[str, Any]],
                        execution_time: float):
        """Record execution metrics."""
        execution_data = {
            "timestamp": datetime.now().isoformat(),
            "task_description": task_description,
            "workflow_type": workflow_type,
            "final_quality_score": state.get("critic_score", 0),
            "total_turns": len(history),
            "execution_time": execution_time,
            "roles_used": [h["role"] for h in history],
            "termination_reason": state.get("termination_reason", "unknown"),
            "domain": state.get("domain"),
            "token_savings": state.get("token_saving_pct", 0),
            "qa_passed": state.get("qa_passed", False),
            "domain_optimized": state.get("domain_optimized", False)
        }
        
        self.metrics["executions"].append(execution_data)
        self.current_session["executions"].append(execution_data)
        
        # Update role metrics
        self._update_role_metrics(history, state, execution_time)
        
        # Update workflow metrics
        self._update_workflow_metrics(workflow_type, state, history, execution_time)
        
        # Generate insights
        self._generate_optimization_insights()
        
        # Save metrics
        self._save_metrics()
    
    def _update_role_metrics(self, history: List[Dict[str, Any]], 
                           state: Dict[str, Any], execution_time: float):
        """Update metrics for each role used in the execution."""
        for entry in history:
            role_name = entry["role"]
            
            if role_name not in self.metrics["role_metrics"]:
                self.metrics["role_metrics"][role_name] = {
                    "execution_count": 0,
                    "total_execution_time": 0,
                    "quality_improvements": [],
                    "token_savings": [],
                    "domain_optimizations": 0
                }
            
            metrics = self.metrics["role_metrics"][role_name]
            metrics["execution_count"] += 1
            metrics["total_execution_time"] += execution_time / len(history)  # Approximate per-role time
            
            # Track quality improvements
            if role_name == "Critic" and "critic_score" in state:
                metrics["quality_improvements"].append(state["critic_score"])
            
            # Track token savings
            if role_name == "TokenOptimizer" and "token_saving_pct" in state:
                metrics["token_savings"].append(state["token_saving_pct"])
            
            # Track domain optimizations
            if role_name == "DomainSpecialist" and state.get("domain_optimized", False):
                metrics["domain_optimizations"] += 1
    
    def _update_workflow_metrics(self, workflow_type: str, state: Dict[str, Any],
                               history: List[Dict[str, Any]], execution_time: float):
        """Update metrics for workflow performance."""
        if workflow_type not in self.metrics["workflow_metrics"]:
            self.metrics["workflow_metrics"][workflow_type] = {
                "total_executions": 0,
                "total_turns": 0,
                "total_quality_scores": [],
                "total_execution_times": [],
                "successful_executions": 0
            }
        
        metrics = self.metrics["workflow_metrics"][workflow_type]
        metrics["total_executions"] += 1
        metrics["total_turns"] += len(history)
        metrics["total_quality_scores"].append(state.get("critic_score", 0))
        metrics["total_execution_times"].append(execution_time)
        
        # Consider execution successful if quality score meets target
        if state.get("critic_score", 0) >= state.get("target_score", 8.0):
            metrics["successful_executions"] += 1
    
    def _generate_optimization_insights(self):
        """Generate optimization insights based on collected metrics."""
        insights = []
        
        # Analyze role effectiveness
        for role_name, metrics in self.metrics["role_metrics"].items():
            if metrics["execution_count"] >= 5:  # Only analyze roles with sufficient data
                avg_quality = sum(metrics["quality_improvements"]) / len(metrics["quality_improvements"]) if metrics["quality_improvements"] else 0
                avg_tokens = sum(metrics["token_savings"]) / len(metrics["token_savings"]) if metrics["token_savings"] else 0
                
                # Generate role-specific insights
                if role_name == "Critic" and avg_quality < 7.0:
                    insights.append(OptimizationInsight(
                        insight_type="role_effectiveness",
                        description=f"Critic role shows low average quality scores ({avg_quality:.1f})",
                        impact_score=0.8,
                        recommendation="Consider improving Critic prompts or adding more specific evaluation criteria",
                        applicable_workflows=["all"]
                    ))
                
                elif role_name == "TokenOptimizer" and avg_tokens < 5.0:
                    insights.append(OptimizationInsight(
                        insight_type="optimization_opportunity",
                        description=f"TokenOptimizer shows low average savings ({avg_tokens:.1f}%)",
                        impact_score=0.6,
                        recommendation="Review token optimization thresholds or improve optimization strategies",
                        applicable_workflows=["performance_optimized", "standard"]
                    ))
        
        # Analyze workflow performance
        for workflow_type, metrics in self.metrics["workflow_metrics"].items():
            if metrics["total_executions"] >= 3:
                success_rate = metrics["successful_executions"] / metrics["total_executions"]
                avg_turns = metrics["total_turns"] / metrics["total_executions"]
                avg_quality = sum(metrics["total_quality_scores"]) / len(metrics["total_quality_scores"])
                
                if success_rate < 0.7:
                    insights.append(OptimizationInsight(
                        insight_type="workflow_performance",
                        description=f"{workflow_type} workflow has low success rate ({success_rate:.1%})",
                        impact_score=0.9,
                        recommendation="Review workflow configuration or role sequence",
                        applicable_workflows=[workflow_type]
                    ))
                
                if avg_turns > 8 and workflow_type == "performance_optimized":
                    insights.append(OptimizationInsight(
                        insight_type="efficiency",
                        description=f"{workflow_type} workflow uses too many turns ({avg_turns:.1f})",
                        impact_score=0.7,
                        recommendation="Optimize workflow for faster convergence",
                        applicable_workflows=[workflow_type]
                    ))
        
        self.metrics["optimization_insights"] = [asdict(insight) for insight in insights]
    
    def get_role_effectiveness_report(self) -> Dict[str, RoleMetrics]:
        """Generate role effectiveness report."""
        role_metrics = {}
        
        for role_name, metrics in self.metrics["role_metrics"].items():
            if metrics["execution_count"] > 0:
                avg_execution_time = metrics["total_execution_time"] / metrics["execution_count"]
                avg_quality = sum(metrics["quality_improvements"]) / len(metrics["quality_improvements"]) if metrics["quality_improvements"] else 0
                avg_tokens = sum(metrics["token_savings"]) / len(metrics["token_savings"]) if metrics["token_savings"] else 0
                
                role_metrics[role_name] = RoleMetrics(
                    role_name=role_name,
                    execution_count=metrics["execution_count"],
                    average_execution_time=avg_execution_time,
                    success_rate=1.0,  # Simplified for demo
                    quality_improvement=avg_quality,
                    token_savings=avg_tokens,
                    domain_optimizations=metrics["domain_optimizations"]
                )
        
        return role_metrics
    
    def get_workflow_performance_report(self) -> Dict[str, WorkflowMetrics]:
        """Generate workflow performance report."""
        workflow_metrics = {}
        
        for workflow_type, metrics in self.metrics["workflow_metrics"].items():
            if metrics["total_executions"] > 0:
                avg_turns = metrics["total_turns"] / metrics["total_executions"]
                avg_quality = sum(metrics["total_quality_scores"]) / len(metrics["total_quality_scores"])
                success_rate = metrics["successful_executions"] / metrics["total_executions"]
                avg_execution_time = sum(metrics["total_execution_times"]) / len(metrics["total_execution_times"])
                
                workflow_metrics[workflow_type] = WorkflowMetrics(
                    workflow_type=workflow_type,
                    total_executions=metrics["total_executions"],
                    average_turns=avg_turns,
                    average_quality_score=avg_quality,
                    success_rate=success_rate,
                    average_execution_time=avg_execution_time,
                    role_effectiveness=self.get_role_effectiveness_report()
                )
        
        return workflow_metrics
    
    def get_optimization_insights(self) -> List[OptimizationInsight]:
        """Get current optimization insights."""
        insights = []
        for insight_data in self.metrics.get("optimization_insights", []):
            insights.append(OptimizationInsight(**insight_data))
        return insights
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        return {
            "session_duration": time.time() - self.session_start,
            "total_executions": len(self.metrics["executions"]),
            "role_effectiveness": self.get_role_effectiveness_report(),
            "workflow_performance": self.get_workflow_performance_report(),
            "optimization_insights": self.get_optimization_insights(),
            "recent_executions": self.metrics["executions"][-10:]  # Last 10 executions
        } 