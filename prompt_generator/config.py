"""Configuration utilities for the prompt generator system.

Oner: Manages configuration loading, validation, and settings for the prompt
generator with support for YAML files, environment variables, and defaults.

Pyramid Principle:
- Main: Configuration management and settings validation
- Secondary: YAML file loading and environment variable handling
- Tertiary: API key management and demo mode fallbacks

Requirements:
- YAML configuration file support
- Environment variable integration
- OpenAI API key validation
- Demo mode for testing without API keys
- Model settings configuration
"""

from __future__ import annotations

import os
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from pydantic_settings import BaseSettings

# Change default config path to point to the subfolder
DEFAULT_CONFIG_PATH = Path(os.getenv("PROMPTGEN_CONFIG", "prompt_generator/config.yaml"))

# Global config cache
_loaded_config = None

__all__ = [
    "load_config",
    "load_config_safe", 
    "Settings",
    "settings",
    "DEFAULT_CONFIG_PATH",
]


def load_config(path: Optional[os.PathLike | str] = None) -> Dict[str, Any]:
    """Load YAML configuration file if it exists.

    Raises an error if no config file present.
    """
    global _loaded_config
    path = Path(path or DEFAULT_CONFIG_PATH)
    if not path.exists():
        raise RuntimeError(f"Config file {path} not found. Please create config.yaml with your OpenAI API key.")
    with path.open("r", encoding="utf-8") as fh:
        config = yaml.safe_load(fh) or {}
    print(f"[CONFIG] Loaded config from: {path.resolve()}")
    print(f"[CONFIG] role_temperatures: {config.get('role_temperatures')}")
    _loaded_config = config
    return config


def load_config_safe(path: Optional[os.PathLike | str] = None) -> Dict[str, Any]:
    """Load YAML configuration file with error handling.
    
    This is the main function that handles exceptions and provides fallbacks.
    """
    try:
        return load_config(path)
    except Exception as e:
        print(f"Error loading config file: {e}")
        return {}


class Settings(BaseSettings):
    """Configuration settings for the prompt generator system.
    
    Examples:
        >>> settings = Settings()
        >>> settings.openai_api_key = "test-key"
        >>> settings.default_model
        'o4-mini-2025-04-16'
    """
    
    openai_api_key: str = ""
    default_model: str = "o4-mini-2025-04-16"  # Default AI model (fallback)
    reasoning_effort: str = "medium"  # Default value
    max_completion_tokens: int = 4000  # Reduced to avoid context length issues
    # demo_mode: bool = False  # Removed: always require real API key

    # Role-specific model configurations (will be loaded from YAML)
    role_models: Dict[str, str] = {}
    
    # Role-specific token limits (will be loaded from YAML)
    role_tokens: Dict[str, int] = {}
    
    # Role-specific temperature settings (will be loaded from YAML)
    role_temperatures: Dict[str, float] = {}

    model_config = {
        "strict": True, 
        "extra": "forbid", 
        # "frozen": True,  # Removed to allow updates
        "env_prefix": "OPENAI_",
        "env_file": ".env"
    }
    __schema_version__ = "1.0.0"  # Track schema changes for compatibility

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Always load config from YAML and override defaults
        config = load_config_safe()
        if config.get("api"):
            api_cfg = config["api"]
            if api_cfg.get("openai_api_key"):
                self.openai_api_key = api_cfg["openai_api_key"]
            if api_cfg.get("default_model"):
                self.default_model = api_cfg["default_model"]
            if api_cfg.get("max_completion_tokens"):
                self.max_completion_tokens = api_cfg["max_completion_tokens"]
        if config.get("role_models"):
            self.role_models.update(config["role_models"])
        if config.get("role_tokens"):
            self.role_tokens.update(config["role_tokens"])
        if config.get("role_temperatures"):
            self.role_temperatures.update(config["role_temperatures"])
        print(f"[SETTINGS] role_temperatures after load: {self.role_temperatures}")

    def get_role_model(self, role_name: str) -> str:
        """Get the model for a specific role."""
        # First try role-specific model, then default model, then fallback
        return self.role_models.get(role_name, self.default_model)
    
    def get_role_tokens(self, role_name: str) -> int:
        """Get the token limit for a specific role."""
        # Use role-specific tokens, fallback to default max_completion_tokens
        return self.role_tokens.get(role_name, self.max_completion_tokens)
    
    def get_role_temperature(self, role_name: str) -> float:
        """Get the temperature for a specific role."""
        # First try role-specific temperature, then default to 0.7
        return self.role_temperatures.get(role_name, 0.7)
    
    # Additional configuration sections
    workflow_settings: Dict[str, Any] = {}
    domain_settings: Dict[str, Any] = {}
    analytics_settings: Dict[str, Any] = {}
    output_settings: Dict[str, Any] = {}
    logging_settings: Dict[str, Any] = {}
    
    def get_workflow_config(self, workflow_name: str) -> Dict[str, Any]:
        """Get configuration for a specific workflow."""
        return self.workflow_settings.get(workflow_name, {})
    
    def get_domain_config(self, domain_name: str) -> Dict[str, Any]:
        """Get configuration for a specific domain."""
        return self.domain_settings.get(domain_name, {})
    
    def get_analytics_config(self) -> Dict[str, Any]:
        """Get analytics configuration."""
        return self.analytics_settings
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output format configuration."""
        return self.output_settings
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.logging_settings

    def load_from_yaml(self, path: Optional[os.PathLike | str] = None):
        cfg = load_config_safe(path)
        if cfg.get("openai_api_key"):
            self.openai_api_key = cfg["openai_api_key"]
        else:
            # Try to get from environment
            env_api_key = os.environ.get("OPENAI_API_KEY")
            if env_api_key:
                self.openai_api_key = env_api_key
            else:
                raise RuntimeError("No OpenAI API key found in config or environment. Please set OPENAI_API_KEY.")
        
        # Load model settings if available
        if cfg.get("model_settings"):
            model_settings = cfg["model_settings"]
            if "reasoning_effort" in model_settings:
                self.reasoning_effort = model_settings["reasoning_effort"]
            if "max_completion_tokens" in model_settings:
                # Make sure we accept the higher limit for o3 models
                tokens = model_settings["max_completion_tokens"]
                self.max_completion_tokens = tokens
        
        # Load role-specific configurations if available
        if cfg.get("role_models"):
            self.role_models.update(cfg["role_models"])
        
        if cfg.get("role_tokens"):
            self.role_tokens.update(cfg["role_tokens"])
        
        if cfg.get("role_temperatures"):
            self.role_temperatures.update(cfg["role_temperatures"])
        
        # Load workflow settings if available
        if cfg.get("workflow_settings"):
            self.workflow_settings = cfg["workflow_settings"]
        
        # Load domain settings if available
        if cfg.get("domain_settings"):
            self.domain_settings = cfg["domain_settings"]
        
        # Load analytics settings if available
        if cfg.get("analytics"):
            self.analytics_settings = cfg["analytics"]
        
        # Load output settings if available
        if cfg.get("output_settings"):
            self.output_settings = cfg["output_settings"]
        
        # Load logging settings if available
        if cfg.get("logging"):
            self.logging_settings = cfg["logging"]


settings = Settings()
settings.load_from_yaml() 