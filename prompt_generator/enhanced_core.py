"""Enhanced high-level API with analytics integration and advanced workflows.

Oner: Provides an enhanced API that integrates domain-specific roles, adaptive
workflows, and comprehensive analytics for advanced prompt generation.

Pyramid Principle:
- Main: Enhanced API with workflow and analytics integration
- Secondary: Domain-specific and quality-focused workflows
- Tertiary: Performance monitoring and optimization insights

Requirements:
- Integration with enhanced orchestrator
- Analytics tracking and reporting
- Domain-specific workflow support
- Quality-focused optimization
- Performance monitoring capabilities
"""

from __future__ import annotations

import time
from typing import Tu<PERSON>, Dict, Any, Optional, List
from pathlib import Path

from .enhanced_orchestrator import EnhancedOrchestrator, WorkflowType
from .analytics import AnalyticsEngine


def run_enhanced_orchestrator(
    task_description: str,
    *,
    target_score: float = 8.0,
    max_turns: int = 10,
    workflow_type: WorkflowType = WorkflowType.STANDARD,
    domain: Optional[str] = None,
    enable_analytics: bool = True,
    analytics_file: Optional[Path] = None
) -> Tuple[Dict[str, Any], List[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """Run the enhanced orchestrator with analytics tracking.
    
    Returns (state, history, analytics_report).
    """
    # Initialize analytics if enabled
    analytics = None
    if enable_analytics:
        analytics = AnalyticsEngine(analytics_file)
    
    # Create enhanced orchestrator
    orchestrator = EnhancedOrchestrator(
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=workflow_type,
        domain=domain
    )
    
    # Record start time
    start_time = time.time()
    
    # Execute orchestration
    state, history = orchestrator.run(task_description)
    
    # Calculate execution time
    execution_time = time.time() - start_time
    
    # Record analytics if enabled
    analytics_report = None
    if analytics:
        analytics.record_execution(
            task_description=task_description,
            workflow_type=workflow_type.value,
            state=state,
            history=history,
            execution_time=execution_time
        )
        analytics_report = analytics.generate_performance_report()
    
    return state, history, analytics_report


def run_domain_specific_workflow(
    task_description: str,
    domain: str,
    *,
    target_score: float = 8.5,
    max_turns: int = 12,
    enable_analytics: bool = True
) -> Tuple[Dict[str, Any], List[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """Run domain-specific workflow for specialized tasks."""
    return run_enhanced_orchestrator(
        task_description=task_description,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=WorkflowType.DOMAIN_SPECIFIC,
        domain=domain,
        enable_analytics=enable_analytics
    )


def run_quality_focused_workflow(
    task_description: str,
    *,
    target_score: float = 9.0,
    max_turns: int = 15,
    enable_analytics: bool = True
) -> Tuple[Dict[str, Any], List[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """Run quality-focused workflow for high-quality requirements."""
    return run_enhanced_orchestrator(
        task_description=task_description,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=WorkflowType.QUALITY_FOCUSED,
        enable_analytics=enable_analytics
    )


def run_compliance_critical_workflow(
    task_description: str,
    *,
    target_score: float = 9.5,
    max_turns: int = 20,
    enable_analytics: bool = True
) -> Tuple[Dict[str, Any], List[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """Run compliance-critical workflow for regulatory requirements."""
    return run_enhanced_orchestrator(
        task_description=task_description,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=WorkflowType.COMPLIANCE_CRITICAL,
        enable_analytics=enable_analytics
    )


def run_performance_optimized_workflow(
    task_description: str,
    *,
    target_score: float = 7.0,
    max_turns: int = 6,
    enable_analytics: bool = True
) -> Tuple[Dict[str, Any], List[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """Run performance-optimized workflow for fast execution."""
    return run_enhanced_orchestrator(
        task_description=task_description,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=WorkflowType.PERFORMANCE_OPTIMIZED,
        enable_analytics=enable_analytics
    )


def get_workflow_recommendation(task_description: str) -> WorkflowType:
    """Get workflow recommendation based on task description."""
    orchestrator = EnhancedOrchestrator()
    return orchestrator._detect_workflow_type(task_description)


def generate_analytics_report(analytics_file: Optional[Path] = None) -> Dict[str, Any]:
    """Generate comprehensive analytics report."""
    analytics = AnalyticsEngine(analytics_file)
    return analytics.generate_performance_report()


def get_optimization_insights(analytics_file: Optional[Path] = None) -> List[Dict[str, Any]]:
    """Get optimization insights from analytics."""
    analytics = AnalyticsEngine(analytics_file)
    insights = analytics.get_optimization_insights()
    return [insight.__dict__ for insight in insights] 