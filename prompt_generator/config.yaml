# Prompt Generator Configuration
# This file configures all aspects of the prompt generation system

# API Configuration
api:
  openai_api_key: ""  # Set your OpenAI API key here or use environment variable OPENAI_API_KEY
  default_model: "o4-mini-2025-04-16"  # Default AI model (fallback)
  max_completion_tokens: 4000  # Reduced to avoid context length issues

# Role-specific models
# Each role can use a different model optimized for its specific task
role_models:
  Writer: "o4-mini-2025-04-16"        # Creative generation
  Editor: "o4-mini-2025-04-16"        # Refinement and editing
  Critic: "o4-mini-2025-04-16"        # Evaluation and scoring
  TokenOptimizer: "o4-mini-2025-04-16" # Optimization tasks
  OutputGuardian: "o4-mini-2025-04-16" # Validation and formatting
  DomainSpecialist: "o4-mini-2025-04-16" # Domain expertise
  QualityAssurance: "o4-mini-2025-04-16" # Comprehensive QA
  JSONEnforcer: "o4-mini-2025-04-16"   # Format conversion
  EnhancedWriter: "o4-mini-2025-04-16"  # Requirements-aware prompt generation

# Role-specific token limits
# Reduced to prevent truncation and ensure complete responses
role_tokens:
  Writer: 5000       
  Editor: 5000        
  Critic: 2500        
  TokenOptimizer: 3000 
  OutputGuardian: 2000 
  DomainSpecialist: 3000 
  QualityAssurance: 3500 
  JSONEnforcer: 1500   
  EnhancedWriter: 4000 

# Role-specific temperature settings
# Higher values (0.8-1.0) for creative tasks, lower values (0.1-0.3) for precise tasks
role_temperatures:
  Writer: 1.0         # Creative generation
  Editor: 1.0         # Balanced refinement
  Critic: 1.0         # Precise evaluation
  TokenOptimizer: 1.0 # Balanced optimization
  OutputGuardian: 1.0 # Very precise validation
  DomainSpecialist: 1.0 # Domain expertise
  QualityAssurance: 1.0 # Comprehensive QA
  JSONEnforcer: 1.0   # Very precise formatting
  EnhancedWriter: 1.0  # Requirements-aware generation

# Orchestration settings
target_score: 8.0
max_turns: 10

# Workflow settings
workflow_settings:
  standard:
    description: "Balanced workflow with quality and performance"
    role_sequence: ["Writer", "OutputGuardian", "Critic", "Editor", "TokenOptimizer", "JSONEnforcer"]
    quality_threshold: 8.0
    max_turns: 10
  domain_specific:
    description: "Domain-optimized workflow with specialized roles"
    role_sequence: ["Writer", "DomainSpecialist", "OutputGuardian", "Critic", "Editor", "QualityAssurance", "JSONEnforcer"]
    quality_threshold: 8.5
    max_turns: 12
  quality_focused:
    description: "Quality-first workflow with comprehensive testing"
    role_sequence: ["Writer", "OutputGuardian", "Critic", "Editor", "QualityAssurance", "TokenOptimizer", "JSONEnforcer"]
    quality_threshold: 9.0
    max_turns: 15
  performance_optimized:
    description: "Speed-focused workflow with minimal iterations"
    role_sequence: ["Writer", "OutputGuardian", "Critic", "Editor", "JSONEnforcer"]
    quality_threshold: 7.0
    max_turns: 6
  compliance_critical:
    description: "Compliance-first workflow with extensive validation"
    role_sequence: ["Writer", "OutputGuardian", "Critic", "Editor", "QualityAssurance", "DomainSpecialist", "JSONEnforcer"]
    quality_threshold: 9.5
    max_turns: 20

# Domain configurations
domains:
  financial:
    keywords: ["financial", "investment", "valuation", "ebitda", "roi", "cash flow", "revenue", "profit"]
    specializations: ["financial analysis", "investment strategies", "risk assessment"]
  technical:
    keywords: ["api", "code", "software", "technical", "development", "system", "programming"]
    specializations: ["software development", "system architecture", "technical documentation"]
  creative:
    keywords: ["creative", "artistic", "design", "storytelling", "narrative", "branding"]
    specializations: ["creative writing", "brand development", "content creation"]
  medical:
    keywords: ["medical", "health", "patient", "clinical", "diagnosis", "treatment", "healthcare"]
    specializations: ["medical analysis", "clinical documentation", "healthcare protocols"]
  legal:
    keywords: ["legal", "compliance", "regulatory", "contract", "law", "regulation", "governance"]
    specializations: ["legal documentation", "compliance reporting", "regulatory analysis"]

# Analytics settings
analytics:
  enabled: true
  track_performance: true
  track_quality_scores: true
  track_role_usage: true
  save_reports: true
  report_directory: "analytics_reports"

# Output format settings
output_format:
  json_enabled: true
  include_metadata: true
  include_analytics: true
  format_validation: true

# Logging settings
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: false
  console_logging: true 