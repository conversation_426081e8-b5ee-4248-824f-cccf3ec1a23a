"""High-level API for external callers.

Oner: Provides a simple, clean interface for external code to use the prompt
generator system without needing to understand the internal orchestration.

Pyramid Principle:
- Main: Simplified API for prompt generation
- Secondary: Enhanced orchestrator integration
- Tertiary: Backward compatibility and parameter validation

Requirements:
- Simple function interface for external callers
- Default parameter handling
- Tuple return format (state, history)
- Type hints for all parameters and returns
- Backward compatibility with old API
"""

from __future__ import annotations

import logging
logger = logging.getLogger(__name__)

from typing import Tuple, Dict, Any

from .enhanced_core import run_enhanced_orchestrator
from .enhanced_orchestrator import WorkflowType


def run_orchestrator(task_description: str, *, target_score: float = 8.0, max_turns: int = 10) -> Tuple[Dict[str, Any], list[Dict[str, Any]]]:
    """Run the default orchestrator for *task_description* and return (*state*, *history*).
    
    This is a backward-compatible wrapper around the enhanced orchestrator.
    """
    state, history, _ = run_enhanced_orchestrator(
        task_description=task_description,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=WorkflowType.STANDARD,
        enable_analytics=False  # Disable analytics for backward compatibility
    )
    logger.info(f"Orchestrator run completed. State: {state}, History: {history}")
    return state, history 