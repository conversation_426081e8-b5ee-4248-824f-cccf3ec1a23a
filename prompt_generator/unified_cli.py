"""Unified CLI for the prompt generator system.

Oner: Provides a single CLI entry point that combines old and enhanced functionality,
with the enhanced CLI as the primary interface and backward compatibility for old commands.

Pyramid Principle:
- Main: Unified CLI with enhanced functionality
- Secondary: Backward compatibility and deprecation warnings
- Tertiary: Rich terminal output and analytics integration

Requirements:
- Single entry point for all CLI functionality
- Enhanced CLI as primary interface
- Backward compatibility for old commands
- Deprecation warnings and migration guidance
- Rich terminal output and analytics
"""

from __future__ import annotations

import warnings
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console

# Remove all Typer CLI imports and commands

app = typer.Typer(add_help_option=True, pretty_exceptions_enable=False)
console = Console()


def _show_enhancement_info():
    """Show information about enhanced features."""
    console.print("[bold blue]Enhanced Prompt Generator CLI[/bold blue]")
    console.print("Available workflow types: standard, domain_specific, quality_focused, performance_optimized, compliance_critical")
    console.print("Use --help for more information on each command.")


# Remove all Typer CLI commands


def main() -> None:
    """Main entry point for unified CLI."""
    app()


if __name__ == "__main__":
    main() 