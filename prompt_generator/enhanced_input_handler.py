"""
Enhanced Input Handler for Requirements-Aware Prompt Generation.

Handles enhanced input from requirements documents and converts it to a format
that the prompt generator can use effectively.
"""

from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
import json

from requirements_doc_generator.models import RequirementsDocument


@dataclass
class EnhancedPromptInput:
    """Enhanced input structure for requirements-aware prompt generation."""
    
    # Original input
    original_prompt: str
    
    # Requirements context
    problem_statement: Optional[str] = None
    core_objectives: Optional[list] = None
    key_requirements: Optional[list] = None
    stakeholders: Optional[list] = None
    success_criteria: Optional[list] = None
    constraints: Optional[list] = None
    
    # Domain context
    domain: Optional[str] = None
    complexity_level: Optional[str] = None
    priority_level: Optional[str] = None
    
    # Workflow context
    input_format: Optional[str] = None
    output_format: Optional[str] = None
    processing_steps: Optional[list] = None
    
    # Quality context
    accuracy_threshold: Optional[float] = None
    completeness_score: Optional[float] = None
    validation_criteria: Optional[list] = None
    
    # Additional metadata
    industry: Optional[str] = None
    regulatory_requirements: Optional[list] = None


class EnhancedInputHandler:
    """Handles enhanced input from requirements documents."""
    
    def __init__(self):
        self.supported_domains = {
            "financial", "technical", "medical", "legal", "creative", 
            "professional", "educational", "scientific", "marketing"
        }
    
    def create_enhanced_input(self, input_data: Union[str, Dict[str, Any], RequirementsDocument]) -> EnhancedPromptInput:
        """
        Create enhanced input from various input formats.
        
        Args:
            input_data: Can be a simple string, enhanced dict, or RequirementsDocument
            
        Returns:
            EnhancedPromptInput: Structured enhanced input
        """
        if isinstance(input_data, str):
            return self._from_simple_string(input_data)
        elif isinstance(input_data, dict):
            return self._from_enhanced_dict(input_data)
        elif isinstance(input_data, RequirementsDocument):
            return self._from_requirements_document(input_data)
        else:
            raise ValueError(f"Unsupported input type: {type(input_data)}")
    
    def _from_simple_string(self, task_description: str) -> EnhancedPromptInput:
        """Convert simple string to enhanced input with basic defaults."""
        return EnhancedPromptInput(
            original_prompt=task_description,
            problem_statement=f"Need to {task_description.lower()}",
            core_objectives=[f"Successfully {task_description.lower()}"],
            key_requirements=["Quality output", "Clear communication"],
            domain="general",
            complexity_level="moderate",
            priority_level="medium"
        )
    
    def _from_enhanced_dict(self, enhanced_dict: Dict[str, Any]) -> EnhancedPromptInput:
        """Convert enhanced dictionary to structured input."""
        return EnhancedPromptInput(
            original_prompt=enhanced_dict.get("original_prompt", ""),
            problem_statement=enhanced_dict.get("problem_statement"),
            core_objectives=enhanced_dict.get("core_objectives"),
            key_requirements=enhanced_dict.get("key_requirements"),
            stakeholders=enhanced_dict.get("stakeholders"),
            success_criteria=enhanced_dict.get("success_criteria"),
            constraints=enhanced_dict.get("constraints"),
            domain=enhanced_dict.get("domain"),
            complexity_level=enhanced_dict.get("complexity_level"),
            priority_level=enhanced_dict.get("priority_level"),
            input_format=enhanced_dict.get("input_format"),
            output_format=enhanced_dict.get("output_format"),
            processing_steps=enhanced_dict.get("processing_steps"),
            accuracy_threshold=enhanced_dict.get("accuracy_threshold"),
            completeness_score=enhanced_dict.get("completeness_score"),
            validation_criteria=enhanced_dict.get("validation_criteria"),
            industry=enhanced_dict.get("industry"),
            regulatory_requirements=enhanced_dict.get("regulatory_requirements")
        )
    
    def _from_requirements_document(self, requirements_doc: RequirementsDocument) -> EnhancedPromptInput:
        """Convert RequirementsDocument to enhanced input."""
        req_doc = requirements_doc.requirements_doc
        workflow = requirements_doc.workflow_expectations
        quality = requirements_doc.quality_metrics
        
        return EnhancedPromptInput(
            original_prompt=requirements_doc.original_prompt,
            problem_statement=req_doc.problem_statement,
            core_objectives=req_doc.core_objectives,
            key_requirements=req_doc.key_requirements,
            stakeholders=req_doc.stakeholders,
            success_criteria=req_doc.success_criteria,
            constraints=req_doc.constraints,
            domain=req_doc.domain,
            complexity_level=req_doc.complexity_level.value if req_doc.complexity_level else None,
            priority_level=req_doc.priority_level.value if req_doc.priority_level else None,
            input_format=workflow.input_format,
            output_format=workflow.output_format,
            processing_steps=workflow.processing_steps,
            accuracy_threshold=quality.accuracy_threshold,
            completeness_score=quality.completeness_score,
            validation_criteria=quality.validation_criteria,
            industry=req_doc.industry,
            regulatory_requirements=req_doc.regulatory_requirements
        )
    
    def create_enhanced_task_description(self, enhanced_input: EnhancedPromptInput) -> str:
        """
        Create an enhanced task description that includes requirements context.
        
        Args:
            enhanced_input: The enhanced input
            
        Returns:
            str: Enhanced task description for the prompt generator
        """
        parts = []
        
        # Original prompt
        parts.append(f"ORIGINAL REQUEST: {enhanced_input.original_prompt}")
        
        # Problem context
        if enhanced_input.problem_statement:
            parts.append(f"PROBLEM: {enhanced_input.problem_statement}")
        
        # Objectives
        if enhanced_input.core_objectives:
            objectives_str = ", ".join(enhanced_input.core_objectives)
            parts.append(f"OBJECTIVES: {objectives_str}")
        
        # Requirements
        if enhanced_input.key_requirements:
            requirements_str = ", ".join(enhanced_input.key_requirements)
            parts.append(f"REQUIREMENTS: {requirements_str}")
        
        # Domain and complexity
        if enhanced_input.domain:
            parts.append(f"DOMAIN: {enhanced_input.domain}")
        if enhanced_input.complexity_level:
            parts.append(f"COMPLEXITY: {enhanced_input.complexity_level}")
        
        # Quality standards
        if enhanced_input.accuracy_threshold:
            parts.append(f"ACCURACY THRESHOLD: {enhanced_input.accuracy_threshold}")
        if enhanced_input.completeness_score:
            parts.append(f"COMPLETENESS SCORE: {enhanced_input.completeness_score}")
        
        # Constraints
        if enhanced_input.constraints:
            constraints_str = ", ".join(enhanced_input.constraints)
            parts.append(f"CONSTRAINTS: {constraints_str}")
        
        # Success criteria
        if enhanced_input.success_criteria:
            success_str = ", ".join(enhanced_input.success_criteria)
            parts.append(f"SUCCESS CRITERIA: {success_str}")
        
        return "\n".join(parts)
    
    def create_enhanced_state(self, enhanced_input: EnhancedPromptInput) -> Dict[str, Any]:
        """
        Create enhanced state for the prompt generator.
        
        Args:
            enhanced_input: The enhanced input
            
        Returns:
            Dict: Enhanced state with requirements context
        """
        state = {
            "task": enhanced_input.original_prompt,
            "enhanced_task": self.create_enhanced_task_description(enhanced_input),
            "requirements_context": {
                "problem_statement": enhanced_input.problem_statement,
                "core_objectives": enhanced_input.core_objectives,
                "key_requirements": enhanced_input.key_requirements,
                "stakeholders": enhanced_input.stakeholders,
                "success_criteria": enhanced_input.success_criteria,
                "constraints": enhanced_input.constraints
            },
            "domain_context": {
                "domain": enhanced_input.domain,
                "complexity_level": enhanced_input.complexity_level,
                "priority_level": enhanced_input.priority_level,
                "industry": enhanced_input.industry
            },
            "workflow_context": {
                "input_format": enhanced_input.input_format,
                "output_format": enhanced_input.output_format,
                "processing_steps": enhanced_input.processing_steps
            },
            "quality_context": {
                "accuracy_threshold": enhanced_input.accuracy_threshold,
                "completeness_score": enhanced_input.completeness_score,
                "validation_criteria": enhanced_input.validation_criteria
            },
            "regulatory_requirements": enhanced_input.regulatory_requirements
        }
        
        return state
    
    def is_enhanced_input(self, input_data: Union[str, Dict[str, Any], RequirementsDocument]) -> bool:
        """
        Check if the input is enhanced (not just a simple string).
        
        Args:
            input_data: The input to check
            
        Returns:
            bool: True if enhanced, False if simple
        """
        if isinstance(input_data, str):
            return False
        elif isinstance(input_data, dict):
            return any(key in input_data for key in ["requirements_context", "domain_context", "problem_statement"])
        elif isinstance(input_data, RequirementsDocument):
            return True
        else:
            return False 