"""Command-line interface for prompt generator.

Oner: Provides a rich CLI interface for the prompt generator system with interactive
features, detailed logging, and multiple output formats.

Pyramid Principle:
- Main: CLI orchestration with rich terminal output
- Secondary: Enhanced CLI integration
- Tertiary: File I/O, logging, and report generation

Requirements:
- Rich terminal formatting and syntax highlighting
- Side-by-side comparison views
- HTML report generation
- Detailed logging capabilities
- Interactive confirmation prompts
- Backward compatibility with old CLI
"""

from __future__ import annotations

import json
import warnings
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console

from .enhanced_orchestrator_v2 import run_requirements_aware_prompt_generation, EnhancedOrchestrationResult
from .enhanced_orchestrator import EnhancedOrchestrator, WorkflowType

app = typer.Typer(add_help_option=True, pretty_exceptions_enable=False)
console = Console()


def _show_deprecation_warning():
    """Show deprecation warning for old CLI."""
    warnings.warn(
        "The old CLI is deprecated. Use the enhanced CLI instead: "
        "python -m prompt_generator.enhanced_cli orchestrate_enhanced",
        DeprecationWarning,
        stacklevel=2
    )


@app.command()
def orchestrate(
    task: str = typer.Option(..., "--task", "-t", help="Task description for which to generate/refine a prompt."),
    target_score: float = typer.Option(8.0, help="Target critic score required to stop (0-10)."),
    max_turns: int = typer.Option(10, help="Maximum turns before giving up."),
    save: Optional[Path] = typer.Option(None, help="Optional path to save final prompt as markdown."),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Print detailed information about each step."),
    log_file: Optional[Path] = typer.Option(None, "--log", "-l", help="Save detailed logs to a text file."),
    side_by_side: bool = typer.Option(False, "--side-by-side", "-s", help="Show side-by-side view of prompt changes."),
    html_report: Optional[Path] = typer.Option(None, "--html", "-h", help="Generate HTML report of prompt evolution."),
    show_requirements: bool = typer.Option(False, "--show-requirements", help="Show requirements document generation process."),
):
    """Run the orchestrator interactively and display the evolution of the prompt.
    
    DEPRECATED: Use the enhanced CLI instead:
    python -m prompt_generator.enhanced_cli orchestrate_enhanced
    """
    _show_deprecation_warning()
    
    # Use enhanced orchestrator v2
    if show_requirements:
        # First generate requirements document
        from requirements_doc_generator import RequirementsGenerator
        
        print("=" * 60)
        print("GENERATING REQUIREMENTS DOCUMENT")
        print("=" * 60)
        
        req_generator = RequirementsGenerator()
        requirements_doc = req_generator.generate_json(task)
        
        print("REQUIREMENTS DOCUMENT:")
        print("-" * 40)
        print(json.dumps(requirements_doc, indent=2))
        print("-" * 40)
        print("")
        
        # Convert to enhanced input
        from requirements_doc_generator.prompt_integration import RequirementsToPromptConverter
        from requirements_doc_generator.models import RequirementsDocument, RequirementsDoc, WorkflowExpectations, QualityMetrics
        from requirements_doc_generator.models import ComplexityLevel, PriorityLevel
        
        # Convert JSON back to RequirementsDocument object
        req_doc_data = requirements_doc["requirements_doc"]
        workflow_data = requirements_doc["workflow_expectations"]
        quality_data = requirements_doc["quality_metrics"]
        
        # Create RequirementsDoc object
        req_doc = RequirementsDoc(
            problem_statement=req_doc_data.get("problem_statement", ""),
            core_objectives=req_doc_data.get("core_objectives", []),
            solution_approach=req_doc_data.get("solution_approach", ""),
            key_requirements=req_doc_data.get("key_requirements", []),
            functional_requirements=req_doc_data.get("functional_requirements", []),
            non_functional_requirements=req_doc_data.get("non_functional_requirements", []),
            constraints=req_doc_data.get("constraints", []),
            assumptions=req_doc_data.get("assumptions", []),
            dependencies=req_doc_data.get("dependencies", []),
            stakeholders=req_doc_data.get("stakeholders", []),
            success_criteria=req_doc_data.get("success_criteria", []),
            complexity_level=ComplexityLevel(req_doc_data.get("complexity_level", "moderate")),
            priority_level=PriorityLevel(req_doc_data.get("priority_level", "medium")),
            domain=req_doc_data.get("domain"),
            industry=req_doc_data.get("industry"),
            regulatory_requirements=req_doc_data.get("regulatory_requirements", [])
        )
        
        # Create WorkflowExpectations object
        workflow = WorkflowExpectations(
            input_format=workflow_data.get("input_format", ""),
            output_format=workflow_data.get("output_format", ""),
            input_validation_rules=workflow_data.get("input_validation_rules", []),
            output_validation_rules=workflow_data.get("output_validation_rules", []),
            processing_steps=workflow_data.get("processing_steps", []),
            decision_points=workflow_data.get("decision_points", []),
            error_handling=workflow_data.get("error_handling", {}),
            performance_expectations=workflow_data.get("performance_expectations", {}),
            scalability_requirements=workflow_data.get("scalability_requirements", {}),
            integration_points=workflow_data.get("integration_points", []),
            deployment_requirements=workflow_data.get("deployment_requirements", []),
            user_experience_goals=workflow_data.get("user_experience_goals", []),
            accessibility_requirements=workflow_data.get("accessibility_requirements", [])
        )
        
        # Create QualityMetrics object
        quality = QualityMetrics(
            accuracy_threshold=quality_data.get("accuracy_threshold", 0.9),
            precision_threshold=quality_data.get("precision_threshold", 0.85),
            recall_threshold=quality_data.get("recall_threshold", 0.85),
            completeness_score=quality_data.get("completeness_score", 0.9),
            relevance_score=quality_data.get("relevance_score", 0.85),
            consistency_score=quality_data.get("consistency_score", 0.9),
            response_time_threshold=quality_data.get("response_time_threshold", 2.0),
            throughput_requirements=quality_data.get("throughput_requirements", {}),
            validation_criteria=quality_data.get("validation_criteria", []),
            acceptance_criteria=quality_data.get("acceptance_criteria", []),
            test_scenarios=quality_data.get("test_scenarios", []),
            quality_dimensions=quality_data.get("quality_dimensions", {}),
            risk_factors=quality_data.get("risk_factors", []),
            monitoring_metrics=quality_data.get("monitoring_metrics", []),
            feedback_mechanisms=quality_data.get("feedback_mechanisms", [])
        )
        
        # Create RequirementsDocument object
        requirements_document = RequirementsDocument(
            requirements_doc=req_doc,
            workflow_expectations=workflow,
            quality_metrics=quality,
            original_prompt=task,
            validation_status=requirements_doc.get("metadata", {}).get("validation_status", {}),
            validation_issues=requirements_doc.get("metadata", {}).get("validation_issues", [])
        )
        
        # Convert to enhanced input
        converter = RequirementsToPromptConverter()
        enhanced_input = converter.create_json_context(converter.convert_to_prompt_input(requirements_document))
        
        print("ENHANCED INPUT FROM REQUIREMENTS:")
        print("-" * 40)
        print(json.dumps(enhanced_input, indent=2))
        print("-" * 40)
        print("")
        
        print("=" * 60)
        print("GENERATING ENHANCED PROMPT")
        print("=" * 60)
        
        # Use enhanced input for prompt generation
        result = run_requirements_aware_prompt_generation(
            input_data=enhanced_input,
            target_score=target_score,
            max_turns=max_turns
        )
    else:
        # Use simple input for prompt generation
        result = run_requirements_aware_prompt_generation(
            input_data=task,
            target_score=target_score,
            max_turns=max_turns
        )
    
    # Display results
    if verbose:
        print("=" * 60)
        print("ENHANCED PROMPT GENERATION RESULTS")
        print("=" * 60)
        print(f"Input Type: {result.input_type.value}")
        print(f"Execution Time: {result.execution_time:.2f}s")
        print(f"Turn Count: {len(result.history)}")
        print("")
        
        if result.requirements_context:
            print("REQUIREMENTS CONTEXT:")
            for key, value in result.requirements_context.items():
                if value:
                    print(f"  {key}: {value}")
            print("")
        
        if result.domain_context:
            print("DOMAIN CONTEXT:")
            for key, value in result.domain_context.items():
                if value:
                    print(f"  {key}: {value}")
            print("")
    
    print("GENERATED PROMPT:")
    print("-" * 40)
    print(result.final_prompt)
    print("-" * 40)
    
    if save:
        save.write_text(result.final_prompt)
        print(f"Prompt saved to {save}")


def orchestrate_safe(
    task: str,
    target_score: float = 8.0,
    max_turns: int = 10,
    save: Optional[Path] = None,
    verbose: bool = False,
    log_file: Optional[Path] = None,
    side_by_side: bool = False,
    html_report: Optional[Path] = None,
) -> None:
    """Main function that handles exceptions for the orchestrate command."""
    try:
        orchestrate(
            task=task,
            target_score=target_score,
            max_turns=max_turns,
            save=save,
            verbose=verbose,
            log_file=log_file,
            side_by_side=side_by_side,
            html_report=html_report,
        )
    except Exception as e:
        console.print(f"[bold red]Error during orchestration: {e}[/bold red]")
        raise


def run_orchestrator_verbose(task_description: str, *, target_score: float = 8.0, 
                            max_turns: int = 10, verbose: bool = False, 
                            log_file: Optional[Path] = None, side_by_side: bool = False) -> tuple:
    """Run the orchestrator with optional verbose output and detailed logging.
    
    DEPRECATED: Use the enhanced CLI instead.
    """
    _show_deprecation_warning()
    
    # Use enhanced orchestrator directly
    from .enhanced_core import run_enhanced_orchestrator
    
    state, history, analytics_report = run_enhanced_orchestrator(
        task_description=task_description,
        target_score=target_score,
        max_turns=max_turns,
        workflow_type=WorkflowType.STANDARD,
        enable_analytics=False
    )
    
    return state, history


def _write_log(file_handle, text: str) -> None:
    """Write text to log file."""
    file_handle.write(text + "\n")
    file_handle.flush()


def generate_html_report(state: dict, history: list, output_path: Optional[Path] = None) -> Path:
    """Generate HTML report of prompt evolution.
    
    DEPRECATED: Use the enhanced CLI instead.
    """
    _show_deprecation_warning()
    
    # For backward compatibility, return a simple path
    if output_path is None:
        output_path = Path("prompt_evolution_report.html")
    
    # Create a simple HTML report
    html_content = f"""
    <html>
    <head><title>Prompt Evolution Report</title></head>
    <body>
        <h1>Prompt Evolution Report</h1>
        <h2>Final Prompt</h2>
        <pre>{state.get('draft', 'No prompt generated')}</pre>
        <h2>History</h2>
        <ul>
    """
    
    for entry in history:
        html_content += f"<li>Turn {entry.get('turn', 'N/A')}: {entry.get('role', 'Unknown')} - {entry.get('reason', 'No reason')}</li>"
    
    html_content += """
        </ul>
    </body>
    </html>
    """
    
    output_path.write_text(html_content)
    return output_path


def main() -> None:
    """Main entry point for CLI."""
    _show_deprecation_warning()
    app()


if __name__ == "__main__":
    main() 