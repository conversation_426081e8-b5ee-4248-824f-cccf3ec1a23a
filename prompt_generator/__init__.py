"""Prompt Generator package.
Custom orchestrator-based LLM prompt refinement toolkit.

Oner: Provides a complete prompt engineering toolkit with multi-agent orchestration
for generating, refining, and optimizing prompts.

Pyramid Principle:
- Main: High-level API for prompt generation
- Secondary: Multi-agent orchestration system
- Tertiary: Configuration and utility functions

Requirements:
- Simple API for external callers
- Multi-agent workflow management
- Configuration management
- CLI and web interfaces
- Extensible role system
"""

__all__ = [
    # Unified API (recommended)
    "run_orchestrator",  # Backward compatible
    "run_enhanced_orchestrator",  # New unified API
    "run_domain_specific_workflow",
    "run_quality_focused_workflow",
    "run_compliance_critical_workflow",
    "run_performance_optimized_workflow",
    "get_workflow_recommendation",
    "generate_analytics_report",
    "get_optimization_insights",
    
    # Configuration
    "load_config",
    "load_config_safe",
    
    # Orchestrators
    "Orchestrator",  # Backward compatible
    "EnhancedOrchestrator",  # New unified orchestrator
    "WorkflowType",
    
    # Base classes
    "BaseRole",
    "RoleOutput",
    
    # All roles (unified)
    "Writer",
    "Editor", 
    "Critic",
    "TokenOptimizer",
    "OutputGuardian",
    "DomainSpecialist",
    "QualityAssurance",
    "JSONEnforcer",
    
    # Analytics
    "AnalyticsEngine",
    "RoleMetrics",
    "WorkflowMetrics",
    "OptimizationInsight",
]

from .core import run_orchestrator  # noqa: E402
from .enhanced_core import (  # noqa: E402
    run_enhanced_orchestrator,
    run_domain_specific_workflow,
    run_quality_focused_workflow,
    run_compliance_critical_workflow,
    run_performance_optimized_workflow,
    get_workflow_recommendation,
    generate_analytics_report,
    get_optimization_insights,
)
from .config import load_config, load_config_safe  # noqa: E402
from .orchestrator import Orchestrator  # noqa: E402
from .enhanced_orchestrator import EnhancedOrchestrator, WorkflowType  # noqa: E402
from .roles.base import BaseRole, RoleOutput  # noqa: E402
from .roles.writer import Writer  # noqa: E402
from .roles.editor import Editor  # noqa: E402
from .roles.critic import Critic  # noqa: E402
from .roles.token_optimizer import TokenOptimizer  # noqa: E402
from .roles.output_guardian import OutputGuardian  # noqa: E402
from .roles.domain_specialist import DomainSpecialist  # noqa: E402
from .roles.quality_assurance import QualityAssurance  # noqa: E402
from .roles.json_enforcer import JSONEnforcer  # noqa: E402
from .analytics import AnalyticsEngine, RoleMetrics, WorkflowMetrics, OptimizationInsight  # noqa: E402

__version__ = "0.1.0" 