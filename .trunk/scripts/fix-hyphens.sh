#!/bin/bash
# Fix non-breaking hyphens in Python files
# This script replaces non-breaking hyphens (‑) with regular hyphens (-)

set -euo pipefail

# Check if file argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <file>"
    exit 1
fi

file="$1"

# Only process Python files
if [[ "$file" == *.py ]]; then
    # Replace non-breaking hyphens with regular hyphens
    sed -i '' 's/‑/-/g' "$file"
    echo "Fixed non-breaking hyphens in $file"
fi
