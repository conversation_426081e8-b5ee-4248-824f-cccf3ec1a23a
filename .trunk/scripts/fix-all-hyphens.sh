#!/bin/bash
# Fix non-breaking hyphens in all Python files in the repository

set -euo pipefail

echo "🔍 Finding Python files with non-breaking hyphens..."

# Find all Python files and fix hyphens
find . -name "*.py" -not -path "./.venv/*" -not -path "./venv/*" -not -path "./.trunk/*" | while read -r file; do
    if grep -q "‑" "$file" 2>/dev/null; then
        echo "  Fixing: $file"
        sed -i '' 's/‑/-/g' "$file"
    fi
done

echo "✅ All non-breaking hyphens fixed!"
echo ""
echo "Run 'trunk fmt' to format the files if needed."
