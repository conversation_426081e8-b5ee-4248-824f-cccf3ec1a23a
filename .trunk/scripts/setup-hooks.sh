#!/bin/bash
# Setup git hooks for automatic formatting and fixing

set -euo pipefail

echo "Setting up git hooks for auto-formatting..."

# Create pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Auto-format and fix files before commit

set -euo pipefail

# Get list of staged Python files
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.py$' || true)

if [ -n "$staged_files" ]; then
    echo "Auto-formatting Python files..."

    # Fix non-breaking hyphens first
    for file in $staged_files; do
        .trunk/scripts/fix-hyphens.sh "$file"
    done

    # Run trunk format on staged files
    trunk fmt $staged_files

    # Add the formatted files back to staging
    git add $staged_files

    echo "✅ Files formatted and re-staged"
fi
EOF

# Make the hook executable
chmod +x .git/hooks/pre-commit

echo "✅ Pre-commit hook installed!"
echo "Now files will be automatically formatted when you commit."
echo ""
echo "To disable temporarily: git commit --no-verify"
