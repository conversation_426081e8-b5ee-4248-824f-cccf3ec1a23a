import json
import re


def parse_json(s: str) -> str:
    try:
        s = re.sub(r"<think>.*?</think>", " ", s, flags=re.DOTALL).strip()
        idx = next(idx for idx, c in enumerate(s) if c in "[{")
        s = s[idx:]
        json.loads(s)
        return s
    except StopIteration:
        print(f"No JSON object found in the string: {s}")
        return ""
    except json.JSONDecodeError as e:
        return s[: e.pos]
