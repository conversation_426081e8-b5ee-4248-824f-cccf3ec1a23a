
# Default recipe - shows available commands
default:
    @just --list


setup:
   [ ! -d ".venv" ] && python -m venv .venv
   source .venv/bin/activate
   python3 -m pip install -r requirements.txt

run-demo:
 python run_full_flow_locally.py "I want to create a Industry 101 document that will use a industry branch as te input annd a couple companies that are in that industry, uppon which a detailed industry document should be created with all kpis and imporatant data"


# UI Development Commands
ui-dev:
    cd af_ui/task-viz && npm run dev

ui:
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    cd af_ui/task-viz && npm run dev &
    sleep 2
    open http://localhost:5173

ui-stop:
    lsof -ti:5173 | head -1 | xargs kill -9 2>/dev/null || echo "No UI server running"

ui-install:
    cd af_ui/task-viz && npm install

ui-build:
    cd af_ui/task-viz && npm run build

screenshot port="5173":
    cd af_ui && python3 screenshot_for_agent.py http://localhost:{{port}}

# Legacy server command (deprecated - use ui-start instead)
server:
    @echo "⚠️  Legacy command - use 'just ui-start' instead"
    just ui-start