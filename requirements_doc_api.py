"""
FastAPI wrapper for the Requirements Document Generator system.
Provides REST API endpoints for generating comprehensive requirements documents.
"""

import logging
# Configure logging to show logs from underlying modules
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)

from fastapi import Fast<PERSON>I, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uvicorn
import time
import json
from datetime import datetime

# Import the requirements document generator
from requirements_doc_generator import RequirementsGenerator

app = FastAPI(
    title="Requirements Document Generator API",
    description="AI-powered system for generating comprehensive requirements documents",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class RequirementsGenerationRequest(BaseModel):
    initial_prompt: str
    max_iterations: int = 3
    model: Optional[str] = None
    temperature: float = 0.7
    output_format: str = "json"  # json, markdown, both

class RequirementsGenerationResponse(BaseModel):
    requirements_doc: Dict[str, Any]
    workflow_expectations: Dict[str, Any]
    quality_metrics: Dict[str, Any]
    metadata: Dict[str, Any]
    markdown_output: Optional[str] = None
    execution_time: float
    iterations: int
    output_file: str

class RequirementsValidationRequest(BaseModel):
    requirements_doc: Dict[str, Any]

class RequirementsValidationResponse(BaseModel):
    is_valid: bool
    validation_issues: List[str]
    recommendations: List[str]
    completeness_score: float

def save_api_output(result: Dict[str, Any], execution_time: float, request_data: Dict[str, Any]) -> str:
    """Save API output to a single file that gets rewritten each run."""
    
    # Create comprehensive output structure
    api_output = {
        "metadata": {
            "api_name": "Requirements Document Generator API",
            "generated_at": datetime.now().isoformat(),
            "execution_time_seconds": execution_time,
            "api_version": "1.0.0",
            "request_data": request_data
        },
        "result": result,
        "summary": {
            "requirements_generated": True,
            "completeness_score": result.get("quality_metrics", {}).get("completeness_score", 0.0),
            "status": "completed"
        }
    }
    
    # Save to single file (overwrites each run)
    filename = "requirements_output.json"
    
    with open(filename, "w") as f:
        json.dump(api_output, f, indent=2)
    
    print(f"[API] Output saved to: {filename}")
    return filename

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "service": "Requirements Document Generator API",
        "status": "healthy",
        "version": "1.0.0",
        "endpoints": [
            "/generate-requirements",
            "/validate-requirements",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "requirements_doc_generator",
        "version": "1.0.0"
    }

@app.post("/generate-requirements", response_model=RequirementsGenerationResponse)
async def generate_requirements(request: RequirementsGenerationRequest):
    """Generate a comprehensive requirements document."""
    try:
        start_time = time.time()
        
        # Initialize the requirements generator
        generator = RequirementsGenerator()
        
        # Generate requirements document
        if request.output_format == "json":
            document = generator.generate_json(
                request.initial_prompt,
                max_iterations=request.max_iterations
            )
            markdown_output = None
        elif request.output_format == "markdown":
            document = generator.generate_markdown(
                request.initial_prompt,
                max_iterations=request.max_iterations
            )
            markdown_output = document
            # For markdown, we need to parse it back to get structured data
            # This is a simplified approach - in practice you might want to store both
            document = generator.generate_json(
                request.initial_prompt,
                max_iterations=request.max_iterations
            )
        else:  # both
            document = generator.generate_json(
                request.initial_prompt,
                max_iterations=request.max_iterations
            )
            markdown_output = generator.generate_markdown(
                request.initial_prompt,
                max_iterations=request.max_iterations
            )
        
        execution_time = time.time() - start_time
        
        # Prepare response data
        response_data = {
            "requirements_doc": document.get("requirements_doc", {}),
            "workflow_expectations": document.get("workflow_expectations", {}),
            "quality_metrics": document.get("quality_metrics", {}),
            "metadata": document.get("metadata", {}),
            "markdown_output": markdown_output,
            "execution_time": execution_time,
            "iterations": request.max_iterations
        }
        
        # Save output to file
        output_file = save_api_output(response_data, execution_time, request.dict())
        
        return RequirementsGenerationResponse(
            requirements_doc=document.get("requirements_doc", {}),
            workflow_expectations=document.get("workflow_expectations", {}),
            quality_metrics=document.get("quality_metrics", {}),
            metadata=document.get("metadata", {}),
            markdown_output=markdown_output,
            execution_time=execution_time,
            iterations=request.max_iterations,
            output_file=output_file
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Requirements generation failed: {str(e)}")

@app.post("/validate-requirements", response_model=RequirementsValidationResponse)
async def validate_requirements(request: RequirementsValidationRequest):
    """Validate a requirements document for completeness and consistency."""
    try:
        # Simple validation logic - in practice you might want more sophisticated validation
        requirements_doc = request.requirements_doc
        issues = []
        recommendations = []
        
        # Check for required fields
        required_fields = ["problem_statement", "core_objectives", "key_requirements"]
        for field in required_fields:
            if not requirements_doc.get(field):
                issues.append(f"Missing required field: {field}")
                recommendations.append(f"Add a {field} to the requirements document")
        
        # Check for empty arrays
        array_fields = ["core_objectives", "key_requirements", "stakeholders", "success_criteria"]
        for field in array_fields:
            if field in requirements_doc and not requirements_doc[field]:
                issues.append(f"Empty {field} array")
                recommendations.append(f"Add items to the {field} array")
        
        # Calculate completeness score
        total_fields = len(required_fields) + len(array_fields)
        filled_fields = sum(1 for field in required_fields if requirements_doc.get(field))
        filled_fields += sum(1 for field in array_fields if requirements_doc.get(field) and len(requirements_doc[field]) > 0)
        completeness_score = filled_fields / total_fields if total_fields > 0 else 0.0
        
        is_valid = len(issues) == 0 and completeness_score >= 0.8
        
        return RequirementsValidationResponse(
            is_valid=is_valid,
            validation_issues=issues,
            recommendations=recommendations,
            completeness_score=completeness_score
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Requirements validation failed: {str(e)}")

@app.get("/domains")
async def list_domains():
    """List supported domains for requirements generation."""
    return {
        "domains": [
            {"name": "financial", "description": "Financial analysis and investment"},
            {"name": "technical", "description": "Software development and technical systems"},
            {"name": "medical", "description": "Healthcare and medical applications"},
            {"name": "legal", "description": "Legal and compliance systems"},
            {"name": "creative", "description": "Creative content and design"},
            {"name": "enterprise", "description": "Enterprise software and business systems"}
        ]
    }

@app.get("/output-formats")
async def list_output_formats():
    """List available output formats."""
    return {
        "formats": [
            {"name": "json", "description": "Structured JSON output"},
            {"name": "markdown", "description": "Markdown formatted output"},
            {"name": "both", "description": "Both JSON and Markdown outputs"}
        ]
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8002) 