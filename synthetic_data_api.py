"""
FastAPI wrapper for the Synthetic Data Generator system.
Provides REST API endpoints for generating comprehensive test seeds and synthetic data.
"""

import logging
# Configure logging to show logs from underlying modules
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)

from fastapi import Fast<PERSON>I, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uvicorn
import time
import json
from datetime import datetime

# Import the synthetic data generator
from synthetic_data_generator.core import SeedGenerator

app = FastAPI(
    title="Synthetic Data Generator API",
    description="AI-powered system for generating comprehensive test seeds and synthetic data",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class SeedGenerationRequest(BaseModel):
    prompt_json: Dict[str, Any]
    requirements_doc: Dict[str, Any]
    max_iterations: int = 1
    seed_count_per_category: int = 10
    enable_seed_selection: bool = True
    categories: Optional[List[str]] = None

class TestSeed(BaseModel):
    input: str
    expected_output: str
    reasoning: str
    category: str
    metadata: Dict[str, Any]

class SeedGenerationResponse(BaseModel):
    seeds: List[TestSeed]
    categories: Dict[str, int]
    total_seeds: int
    execution_time: float
    iterations: int
    quality_metrics: Optional[Dict[str, Any]] = None
    output_file: str

class DataValidationRequest(BaseModel):
    seeds: List[TestSeed]
    requirements_doc: Dict[str, Any]

class DataValidationResponse(BaseModel):
    is_valid: bool
    validation_issues: List[str]
    quality_score: float
    coverage_score: float
    recommendations: List[str]

def save_api_output(result: Dict[str, Any], execution_time: float, request_data: Dict[str, Any]) -> str:
    """Save API output to a single file that gets rewritten each run."""
    
    # Create comprehensive output structure
    api_output = {
        "metadata": {
            "api_name": "Synthetic Data Generator API",
            "generated_at": datetime.now().isoformat(),
            "execution_time_seconds": execution_time,
            "api_version": "1.0.0",
            "request_data": request_data
        },
        "result": result,
        "summary": {
            "total_seeds": result.get("total_seeds", 0),
            "categories": result.get("categories", {}),
            "quality_score": result.get("quality_metrics", {}).get("overall_score", 0.0) if result.get("quality_metrics") else 0.0,
            "status": "completed"
        }
    }
    
    # Save to single file (overwrites each run)
    filename = "synthetic_data_output.json"
    
    with open(filename, "w") as f:
        json.dump(api_output, f, indent=2)
    
    print(f"[API] Output saved to: {filename}")
    return filename

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "service": "Synthetic Data Generator API",
        "status": "healthy",
        "version": "1.0.0",
        "endpoints": [
            "/generate-seeds",
            "/validate-data",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "synthetic_data_generator",
        "version": "1.0.0"
    }

@app.post("/generate-seeds", response_model=SeedGenerationResponse)
async def generate_seeds(request: SeedGenerationRequest):
    """Generate comprehensive test seeds from prompt and requirements."""
    try:
        start_time = time.time()
        
        # Initialize the seed generator
        generator = SeedGenerator()
        
        # Generate seeds
        result = await generator.generate_seeds(
            prompt_json=request.prompt_json,
            requirements_doc=request.requirements_doc,
            max_iterations=request.max_iterations
        )
        
        execution_time = time.time() - start_time
        
        # Convert seeds to response format
        seeds = []
        categories = {}
        
        # Extract seeds from the result structure
        all_seeds = []
        for category_data in result.get("seeds", {}).values():
            if isinstance(category_data, dict) and "seeds" in category_data:
                all_seeds.extend(category_data["seeds"])
        
        for seed in all_seeds:
            seeds.append(TestSeed(
                input=seed.get("input", ""),
                expected_output=seed.get("expected_output", ""),
                reasoning=seed.get("reasoning", ""),
                category=seed.get("category", "general"),
                metadata=seed.get("metadata", {})
            ))
            
            # Count seeds by category
            category = seed.get("category", "general")
            categories[category] = categories.get(category, 0) + 1
        
        # Prepare response data
        response_data = {
            "seeds": [seed.dict() for seed in seeds],
            "categories": categories,
            "total_seeds": len(seeds),
            "execution_time": execution_time,
            "iterations": request.max_iterations,
            "quality_metrics": result.get("metadata", {}).get("quality_result")
        }
        
        # Save output to file
        output_file = save_api_output(response_data, execution_time, request.dict())
        
        return SeedGenerationResponse(
            seeds=seeds,
            categories=categories,
            total_seeds=len(seeds),
            execution_time=execution_time,
            iterations=request.max_iterations,
            quality_metrics=result.get("metadata", {}).get("quality_result"),
            output_file=output_file
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Seed generation failed: {str(e)}")

@app.post("/validate-data", response_model=DataValidationResponse)
async def validate_data(request: DataValidationRequest):
    """Validate synthetic data for quality and coverage."""
    try:
        seeds = request.seeds
        requirements_doc = request.requirements_doc
        
        issues = []
        recommendations = []
        
        # Check for required fields in seeds
        for i, seed in enumerate(seeds):
            if not seed.input:
                issues.append(f"Seed {i+1}: Missing input")
            if not seed.expected_output:
                issues.append(f"Seed {i+1}: Missing expected output")
            if not seed.reasoning:
                issues.append(f"Seed {i+1}: Missing reasoning")
        
        # Check category distribution
        categories = {}
        for seed in seeds:
            category = seed.category
            categories[category] = categories.get(category, 0) + 1
        
        # Calculate quality metrics
        total_seeds = len(seeds)
        valid_seeds = sum(1 for seed in seeds if seed.input and seed.expected_output and seed.reasoning)
        quality_score = valid_seeds / total_seeds if total_seeds > 0 else 0.0
        
        # Calculate coverage score based on requirements
        requirements = requirements_doc.get("requirements_doc", {}).get("key_requirements", [])
        covered_requirements = 0
        for requirement in requirements:
            # Simple check - in practice you might want more sophisticated coverage analysis
            if any(requirement.lower() in seed.input.lower() or requirement.lower() in seed.expected_output.lower() 
                   for seed in seeds):
                covered_requirements += 1
        
        coverage_score = covered_requirements / len(requirements) if requirements else 1.0
        
        # Generate recommendations
        if quality_score < 0.9:
            recommendations.append("Improve seed quality by ensuring all required fields are populated")
        
        if coverage_score < 0.8:
            recommendations.append("Increase coverage by generating more diverse seeds")
        
        if len(categories) < 3:
            recommendations.append("Generate seeds across more categories for better diversity")
        
        return DataValidationResponse(
            is_valid=quality_score >= 0.8 and coverage_score >= 0.7,
            validation_issues=issues,
            quality_score=quality_score,
            coverage_score=coverage_score,
            recommendations=recommendations
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Data validation failed: {str(e)}")

@app.get("/categories")
async def list_categories():
    """List available test categories."""
    return {
        "categories": [
            "edge_cases",
            "complexity_levels", 
            "context_variations"
        ],
        "descriptions": {
            "edge_cases": "Test seeds that explore boundary conditions, error scenarios, and unusual inputs",
            "complexity_levels": "Test seeds across different complexity levels from simple to expert",
            "context_variations": "Test seeds with varying levels of context and background information"
        }
    }

@app.get("/seed-types")
async def list_seed_types():
    """List available seed types and their characteristics."""
    return {
        "seed_types": {
            "edge_cases": {
                "description": "Boundary testing and error scenarios",
                "characteristics": ["extreme values", "unusual formatting", "error conditions"]
            },
            "complexity_levels": {
                "description": "Different sophistication levels",
                "characteristics": ["simple", "moderate", "complex", "expert"]
            },
            "context_variations": {
                "description": "Context and background variations",
                "characteristics": ["no context", "minimal context", "rich context", "conflicting context"]
            }
        }
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003) 