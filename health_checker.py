"""
Health Check Script for the Prompt Engineering Ecosystem.
Pings all three FastAPI services and provides comprehensive status reports.
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime
import sys

@dataclass
class ServiceStatus:
    name: str
    url: str
    status: str
    response_time: float
    version: str = ""
    endpoints: List[str] = None
    error: str = ""

class HealthChecker:
    def __init__(self):
        self.services = {
            "prompt_generator": {
                "url": "http://localhost:8001",
                "name": "Prompt Generator API",
                "endpoints": ["/health", "/generate-prompt", "/workflow-recommendation"]
            },
            "requirements_doc": {
                "url": "http://localhost:8002", 
                "name": "Requirements Document Generator API",
                "endpoints": ["/health", "/generate-requirements", "/validate-requirements"]
            },
            "synthetic_data": {
                "url": "http://localhost:8003",
                "name": "Synthetic Data Generator API", 
                "endpoints": ["/health", "/generate-seeds", "/validate-data"]
            }
        }
        
    async def check_service(self, session: aiohttp.ClientSession, service_id: str, service_config: Dict[str, Any]) -> ServiceStatus:
        """Check the health of a single service."""
        start_time = time.time()
        
        try:
            # Check root endpoint
            async with session.get(f"{service_config['url']}/") as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    return ServiceStatus(
                        name=service_config['name'],
                        url=service_config['url'],
                        status="healthy",
                        response_time=response_time,
                        version=data.get('version', 'unknown'),
                        endpoints=data.get('endpoints', [])
                    )
                else:
                    return ServiceStatus(
                        name=service_config['name'],
                        url=service_config['url'],
                        status="unhealthy",
                        response_time=response_time,
                        error=f"HTTP {response.status}"
                    )
                    
        except aiohttp.ClientError as e:
            return ServiceStatus(
                name=service_config['name'],
                url=service_config['url'],
                status="unreachable",
                response_time=time.time() - start_time,
                error=str(e)
            )
        except Exception as e:
            return ServiceStatus(
                name=service_config['name'],
                url=service_config['url'],
                status="error",
                response_time=time.time() - start_time,
                error=str(e)
            )
    
    async def check_all_services(self) -> List[ServiceStatus]:
        """Check the health of all services concurrently."""
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
            tasks = []
            for service_id, service_config in self.services.items():
                task = self.check_service(session, service_id, service_config)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return [result for result in results if isinstance(result, ServiceStatus)]
    
    def print_status_report(self, statuses: List[ServiceStatus]):
        """Print a formatted status report."""
        print("=" * 80)
        print("🔍 PROMPT ENGINEERING ECOSYSTEM HEALTH CHECK")
        print("=" * 80)
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Overall status
        healthy_count = sum(1 for status in statuses if status.status == "healthy")
        total_count = len(statuses)
        
        if healthy_count == total_count:
            print("✅ ALL SERVICES HEALTHY")
        elif healthy_count > 0:
            print(f"⚠️  PARTIAL HEALTH: {healthy_count}/{total_count} services healthy")
        else:
            print("❌ ALL SERVICES UNHEALTHY")
        
        print()
        
        # Individual service status
        for status in statuses:
            status_icon = "✅" if status.status == "healthy" else "❌" if status.status == "unreachable" else "⚠️"
            print(f"{status_icon} {status.name}")
            print(f"   URL: {status.url}")
            print(f"   Status: {status.status.upper()}")
            print(f"   Response Time: {status.response_time:.3f}s")
            
            if status.version:
                print(f"   Version: {status.version}")
            
            if status.endpoints:
                print(f"   Endpoints: {', '.join(status.endpoints)}")
            
            if status.error:
                print(f"   Error: {status.error}")
            
            print()
        
        # Summary
        print("-" * 80)
        print("📊 SUMMARY")
        print("-" * 80)
        
        healthy_services = [s for s in statuses if s.status == "healthy"]
        unhealthy_services = [s for s in statuses if s.status != "healthy"]
        
        if healthy_services:
            print(f"✅ Healthy Services ({len(healthy_services)}):")
            for service in healthy_services:
                print(f"   • {service.name} ({service.response_time:.3f}s)")
        
        if unhealthy_services:
            print(f"❌ Unhealthy Services ({len(unhealthy_services)}):")
            for service in unhealthy_services:
                print(f"   • {service.name}: {service.error}")
        
        print()
        
        # Recommendations
        if unhealthy_services:
            print("🔧 RECOMMENDATIONS:")
            for service in unhealthy_services:
                print(f"   • Check if {service.name} is running on {service.url}")
                print(f"   • Verify network connectivity to {service.url}")
                print(f"   • Check service logs for errors")
        
        print("=" * 80)
    
    async def run_test_requests(self):
        """Run test requests to verify functionality."""
        print("\n🧪 TESTING API FUNCTIONALITY")
        print("-" * 40)
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            # Test Prompt Generator
            try:
                test_data = {
                    "task": "Create a simple email template",
                    "target_score": 8.0,
                    "max_turns": 3
                }
                async with session.post("http://localhost:8001/generate-prompt", json=test_data) as response:
                    if response.status == 200:
                        print("✅ Prompt Generator: Test request successful")
                    else:
                        print(f"❌ Prompt Generator: Test request failed (HTTP {response.status})")
            except Exception as e:
                print(f"❌ Prompt Generator: Test request failed - {e}")
            
            # Test Requirements Document Generator
            try:
                test_data = {
                    "initial_prompt": "Create a simple web application",
                    "max_iterations": 1,
                    "output_format": "json"
                }
                async with session.post("http://localhost:8002/generate-requirements", json=test_data) as response:
                    if response.status == 200:
                        print("✅ Requirements Generator: Test request successful")
                    else:
                        print(f"❌ Requirements Generator: Test request failed (HTTP {response.status})")
            except Exception as e:
                print(f"❌ Requirements Generator: Test request failed - {e}")
            
            # Test Synthetic Data Generator
            try:
                test_data = {
                    "prompt_json": {"system_message": "You are a helpful assistant"},
                    "requirements_doc": {"requirements_doc": {"key_requirements": ["test"]}},
                    "max_iterations": 1
                }
                async with session.post("http://localhost:8003/generate-seeds", json=test_data) as response:
                    if response.status == 200:
                        print("✅ Synthetic Data Generator: Test request successful")
                    else:
                        print(f"❌ Synthetic Data Generator: Test request failed (HTTP {response.status})")
            except Exception as e:
                print(f"❌ Synthetic Data Generator: Test request failed - {e}")

async def main():
    """Main function to run health checks."""
    checker = HealthChecker()
    
    # Run health checks
    statuses = await checker.check_all_services()
    checker.print_status_report(statuses)
    
    # Run test requests if all services are healthy
    healthy_count = sum(1 for status in statuses if status.status == "healthy")
    if healthy_count == len(statuses):
        await checker.run_test_requests()
    
    # Exit with appropriate code
    if healthy_count == len(statuses):
        print("\n🎉 All services are healthy and functional!")
        sys.exit(0)
    else:
        print(f"\n⚠️  {len(statuses) - healthy_count} service(s) are unhealthy")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 