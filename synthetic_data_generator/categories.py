"""
Test Categories for Synthetic Data Generator.

Defines the three main categories of test seeds and their characteristics.
"""

from enum import Enum
from typing import List, Dict, Any


class TestCategory(Enum):
    """Test categories for synthetic data generation."""
    EDGE_CASES = "edge_cases"
    COMPLEXITY_LEVELS = "complexity_levels"
    CONTEXT_VARIATIONS = "context_variations"


class TestCategories:
    """Manages test categories and their characteristics."""
    
    def __init__(self):
        self.categories = {
            TestCategory.EDGE_CASES: {
                "description": "Test cases that push the boundaries and test unusual scenarios",
                "characteristics": [
                    "extreme values",
                    "unusual formatting", 
                    "edge case scenarios",
                    "boundary testing",
                    "error conditions"
                ],
                "seed_count": 10
            },
            TestCategory.COMPLEXITY_LEVELS: {
                "description": "Test cases across different complexity levels",
                "characteristics": [
                    "simple inputs",
                    "moderate complexity",
                    "complex scenarios",
                    "expert-level requirements",
                    "enterprise-level needs"
                ],
                "seed_count": 10
            },
            TestCategory.CONTEXT_VARIATIONS: {
                "description": "Test cases with different context levels and types",
                "characteristics": [
                    "no context",
                    "minimal context",
                    "rich context",
                    "conflicting context",
                    "domain-specific context"
                ],
                "seed_count": 10
            }
        }
    
    def get_category_info(self, category: TestCategory) -> Dict[str, Any]:
        """Get information about a specific category."""
        return self.categories.get(category, {})
    
    def get_all_categories(self) -> List[TestCategory]:
        """Get all test categories."""
        return list(self.categories.keys())
    
    def get_seed_count(self, category: TestCategory) -> int:
        """Get the number of seeds to generate for a category."""
        return self.categories.get(category, {}).get("seed_count", 10) 