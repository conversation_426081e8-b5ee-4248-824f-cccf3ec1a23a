# Synthetic Data Generator Configuration - OPTIMIZED FOR SPEED

# Default model settings - Using faster models
default_model: "gpt-4o-mini"    # Fixed model name
default_temperature: 1.0    # Use default temperature for o4-mini

# Per-role model configurations - Optimized for speed
role_models:
  SeedAnalyzer:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 2000        # Reduced from 5000
    description: "Analyzes refined prompt structure and extracts key information"
  
  EdgeCaseGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 2000        # Reduced from 5000
    description: "Generates edge case test seeds with creative boundary testing"
  
  ComplexityGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 2000        # Reduced from 5000
    description: "Generates complexity level test seeds across different sophistication levels"
  
  ContextGenerator:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 2000        # Reduced from 5000
    description: "Generates context variation test seeds with different context levels"
  
  AlignmentValidator:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 1500        # Reduced from 4000
    description: "Validates alignment between seeds, requirements, and prompt"
  
  ValidationAgent:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 1500        # Reduced from 5000
    description: "Validates seed structure and quality"
  
  QualityAssessor:
    model: "gpt-4o-mini"
    temperature: 1.0        # Use default temperature
    max_tokens: 1500        # Reduced from 5000
    description: "Assesses overall quality of generated seeds"

# Workflow settings - OPTIMIZED FOR SPEED
workflow:
  max_iterations: 1
  calls_per_category: 6  # Reduced from 11
  final_seeds_per_category: 3  # Reduced from 10
  enable_individual_calls: true
  enable_parallel_calls: true
  max_workers: 3  # Reduced from 6
  max_llm_retries: 2
  api_call_delay: 1.0  # Delay in seconds between API calls to prevent rate limiting
  enable_alignment_validation: true
  enable_quality_assessment: true
  enable_seed_selection: true
  selection_criteria: ["quality_score", "uniqueness", "relevance"]

# Test categories configuration
categories:
  edge_cases:
    description: "Test cases that push boundaries and test unusual scenarios"
    characteristics:
      - extreme values
      - unusual formatting
      - edge case scenarios
      - boundary testing
      - error conditions
  
  complexity_levels:
    description: "Test cases across different complexity levels"
    characteristics:
      - simple inputs
      - moderate complexity
      - complex scenarios
      - expert-level requirements
      - enterprise-level needs
  
  context_variations:
    description: "Test cases with different context levels and types"
    characteristics:
      - no context
      - minimal context
      - rich context
      - conflicting context
      - domain-specific context

# Quality thresholds - RELAXED FOR SPEED
quality_thresholds:
  minimum_score: 6.0      # Reduced from 7.0 - Lower threshold for speed
  alignment_threshold: 0.6 # Reduced from 0.8 - Lower threshold for speed
  validation_threshold: 0.7 # Reduced from 0.9 - Lower threshold for speed

# Uniqueness calculation settings - OPTIMIZED FOR SPEED
uniqueness:
  use_semantic_similarity: false   # Disabled for speed - Skip semantic similarity
  similarity_threshold: 0.8        # Keep threshold
  embedding_method: "hash_based"   # Keep hash-based for speed
  fallback_to_simple: true         # Use simple comparison for speed

# Output settings - MINIMAL FOR SPEED
output:
  format: "json"                       # Keep JSON format
  include_metadata: false              # Disabled for speed - Skip metadata
  include_analysis: false              # Disabled for speed - Skip analysis
  include_validation_results: false    # Disabled for speed - Skip validation results
  include_quality_scores: false        # Disabled for speed - Skip quality scores

# Logging settings - MINIMAL FOR SPEED
logging:
  level: "WARNING"                     # Reduced from INFO - Less logging
  include_timestamps: false            # Disabled for speed - Skip timestamps
  include_role_execution: false        # Disabled for speed - Skip role execution logs
  include_iteration_details: false     # Disabled for speed - Skip iteration details 