"""
Roles for Synthetic Data Generator.

Specialized agents that work together to generate test seeds.
"""

import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .llm_client import LL<PERSON>lient


@dataclass
class SeedAnalysis:
    """Analysis of a refined prompt for seed generation."""
    placeholders: List[str]
    constraints: List[str]
    domain: str
    output_format: str
    role: str
    tone: str
    complexity_level: str
    key_requirements: List[str]
    system_message: str  # Add actual system message
    user_message: str    # Add actual user message


@dataclass
class TestSeed:
    """A single test seed with input and expected output."""
    input: str
    expected_output: str
    reasoning: str
    category: str
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert TestSeed to dictionary for JSON serialization."""
        return {
            "input": self.input,
            "expected_output": self.expected_output,
            "reasoning": self.reasoning,
            "category": self.category,
            "metadata": self.metadata
        }


@dataclass
class SeedGenerationResult:
    """Result from a seed generation agent."""
    seeds: List[TestSeed]
    analysis: Optional[str] = None
    feedback: Optional[str] = None


class SeedAnalyzer:
    """Analyzes refined prompt JSON to understand structure and requirements."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    def analyze_prompt(self, prompt_json: Dict[str, Any]) -> SeedAnalysis:
        """Analyze the refined prompt to extract key information."""
        
        # Extract basic information
        system_message = prompt_json.get("system_message", "")
        user_message = prompt_json.get("user_message", "")
        metadata = prompt_json.get("metadata", {})
        
        # Extract placeholders (e.g., {{N}}, {{year}})
        placeholders = self._extract_placeholders(user_message)
        
        # Extract constraints
        constraints = metadata.get("constraints", [])
        
        # Extract other metadata
        domain = metadata.get("domain", "general")
        output_format = metadata.get("output_format", "text")
        role = metadata.get("role", "assistant")
        tone = metadata.get("tone", "professional")
        complexity_level = metadata.get("complexity_level", "moderate")
        
        # Extract key requirements from system message
        key_requirements = self._extract_requirements(system_message)
        
        return SeedAnalysis(
            placeholders=placeholders,
            constraints=constraints,
            domain=domain,
            output_format=output_format,
            role=role,
            tone=tone,
            complexity_level=complexity_level,
            key_requirements=key_requirements,
            system_message=system_message,
            user_message=user_message
        )
    
    def _extract_placeholders(self, text: str) -> List[str]:
        """Extract placeholders like {{N}}, {{year}} from text."""
        import re
        pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(pattern, text)
        return [f"{{{{{match}}}}}" for match in matches]
    
    async def _extract_requirements(self, system_message: str) -> List[str]:
        """Extract key requirements from system message using LLM."""
        prompt = f"""
        Analyze this system message and extract the key requirements:
        
        {system_message}
        
        Return a JSON array of requirement keywords. Focus on:
        - Accuracy requirements
        - Format requirements  
        - Citation requirements
        - Quality standards
        - Constraints
        - Output format specifications
        
        Return only the JSON array, no other text.
        """
        
        try:
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=3
            )
            return response if isinstance(response, list) else []
        except Exception as e:
            print(f"[SeedAnalyzer] LLM extraction failed: {e}, using fallback")
            # Fallback to simple extraction
            requirements = []
            if "accuracy" in system_message.lower():
                requirements.append("accuracy")
            if "citation" in system_message.lower():
                requirements.append("citations")
            if "format" in system_message.lower():
                requirements.append("specific_format")
            return requirements


class EdgeCaseGenerator:
    """Generates edge case test seeds."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    async def generate_seeds(self, analysis: SeedAnalysis, count: int = 10) -> SeedGenerationResult:
        """Generate edge case seeds based on prompt analysis using LLM."""
        
        prompt = f"""
        You are a test scenario designer. Your job is to create test case specifications that define what kinds of inputs should be tested and what outputs are expected.
        
        ---
        SYSTEM MESSAGE:
        {analysis.system_message}
        USER MESSAGE:
        {analysis.user_message}
        DOMAIN: {analysis.domain}
        OUTPUT FORMAT: {analysis.output_format}
        PLACEHOLDERS: {analysis.placeholders}
        CONSTRAINTS: {analysis.constraints}
        KEY REQUIREMENTS: {analysis.key_requirements}
        ---
        
        Based on the ACTUAL PROMPT above, create test case scenarios that specify:
        - What kind of input should be provided to test the system (describe the input type, don't generate actual data)
        - What type of output the system should produce for that input (describe expected output format/content)
        - Focus on the prompt's placeholders and expected behavior
        - Think "IF this input type, THEN this output type"
        
        For each test scenario, provide:
        - input: Describe what kind of input to test (e.g., "valid industry name and 3-5 well-known companies")
        - expected_output: Describe what output format/content should be produced (e.g., "JSON with industry overview, company analysis, KPIs")
        - reasoning: Why this test scenario is important and what it validates
        - test_type: The type of test scenario (e.g., valid_input, edge_case, error_handling)
        
        CRITICAL: You must respond with ONLY valid JSON. No explanatory text, no markdown formatting.
        
        Return as JSON array with this structure:
        [
          {{
            "input": "description of input type to test",
            "expected_output": "description of expected output type",
            "reasoning": "why this test scenario is important",
            "test_type": "test_scenario_type"
          }}
        ]
        
        Validate your JSON before responding. Ensure all strings are properly quoted and all brackets are closed.
        """
        
        try:
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=3
            )
            
            seeds = []
            for item in response:
                seeds.append(TestSeed(
                    input=item.get("input", ""),
                    expected_output=item.get("expected_output", ""),
                    reasoning=item.get("reasoning", ""),
                    category="edge_cases",
                    metadata={"test_type": item.get("test_type", "general_edge_case")}
                ))
            
            return SeedGenerationResult(seeds=seeds[:count])
            
        except Exception as e:
            print(f"[EdgeCaseGenerator] LLM generation failed: {e}, using fallback")
            # Fallback to simple generation
            seeds = []
            for i in range(count):
                seeds.append(TestSeed(
                    input=f"Edge case input {i + 1}",
                    expected_output="Should handle edge case appropriately",
                    reasoning="Testing boundary conditions and unusual scenarios",
                    category="edge_cases",
                    metadata={"test_type": "general_edge_case"}
                ))
            return SeedGenerationResult(seeds=seeds)
    
    async def generate_single_seed(self, analysis: SeedAnalysis, seed_number: int, category: str, max_llm_retries: int = 3) -> Optional[TestSeed]:
        """Generate a single edge case test seed."""
        try:
            prompt = f"""
            You are a test scenario designer. Your job is to create a single test case specification that defines what kind of input should be tested and what output is expected.
            
            ---
            SYSTEM MESSAGE:
            {analysis.system_message}
            USER MESSAGE:
            {analysis.user_message}
            DOMAIN: {analysis.domain}
            OUTPUT FORMAT: {analysis.output_format}
            PLACEHOLDERS: {analysis.placeholders}
            CONSTRAINTS: {analysis.constraints}
            KEY REQUIREMENTS: {analysis.key_requirements}
            ---
            
            Based on the ACTUAL PROMPT above, create a test case scenario that specifies:
            - What kind of input should be provided to test the system (describe the input type, don't generate actual data)
            - What type of output the system should produce for that input (describe expected output format/content)
            - Focus on the prompt's placeholders and expected behavior
            - Think "IF this input type, THEN this output type"
            
            Provide ONE test scenario with:
            - input: Describe what kind of input to test (e.g., "valid industry name and 3-5 well-known companies")
            - expected_output: Describe what output format/content should be produced (e.g., "JSON with industry overview, company analysis, KPIs")
            - reasoning: Why this test scenario is important and what it validates
            - test_type: The type of test scenario (e.g., valid_input, edge_case, error_handling)
            
            CRITICAL: You must respond with ONLY valid JSON. No explanatory text, no markdown formatting.
            
            Return as JSON object with this structure:
            {{
              "input": "description of input type to test",
              "expected_output": "description of expected output type",
              "reasoning": "why this test scenario is important",
              "test_type": "test_scenario_type"
            }}
            
            Validate your JSON before responding. Ensure all strings are properly quoted and all brackets are closed.
            """
            
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=max_llm_retries
            )
            
            if isinstance(response, dict):
                return TestSeed(
                    input=response.get("input", ""),
                    expected_output=response.get("expected_output", ""),
                    reasoning=response.get("reasoning", ""),
                    category=category,
                    metadata={"test_type": response.get("test_type", "general_edge_case")}
                )
            elif isinstance(response, list) and len(response) > 0:
                item = response[0]
                return TestSeed(
                    input=item.get("input", ""),
                    expected_output=item.get("expected_output", ""),
                    reasoning=item.get("reasoning", ""),
                    category=category,
                    metadata={"test_type": item.get("test_type", "general_edge_case")}
                )
            else:
                print(f"[EdgeCaseGenerator] Invalid response format: {type(response)}")
                return None
                
        except Exception as e:
            print(f"[EdgeCaseGenerator] Single seed generation failed: {e}")
            return None


class ComplexityGenerator:
    """Generates complexity-based test seeds."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    async def generate_seeds(self, analysis: SeedAnalysis, count: int = 10) -> SeedGenerationResult:
        """Generate complexity-based seeds using LLM."""
        
        prompt = f"""
        You are a test designer for a general-purpose analysis tool. Your job is to create a single complexity-based test seed that challenges the tool's ability to handle sophisticated scenarios and requirements across various domains.
        
        ---
        SYSTEM MESSAGE:
        {analysis.system_message}
        USER MESSAGE:
        {analysis.user_message}
        DOMAIN: {analysis.domain}
        OUTPUT FORMAT: {analysis.output_format}
        PLACEHOLDERS: {analysis.placeholders}
        CONSTRAINTS: {analysis.constraints}
        KEY REQUIREMENTS: {analysis.key_requirements}
        ---
        
        Create a complexity test that targets:
        - Multi-step analysis or processing
        - Complex data structures or nested objects
        - Multiple requirements or conflicting information
        - Advanced or rare scenarios (e.g., multi-language, multi-currency)
        - Large datasets or complex calculations
        
        Provide ONE seed with:
        - input: The test input (realistic data, scenario, or user query)
        - expected_output: What the tool should produce (including detailed analysis, warnings, or errors)
        - reasoning: Why this is a complexity test and what it challenges
        - complexity: The complexity level (simple/moderate/high/very_high)
        
        CRITICAL: You must respond with ONLY valid JSON. No explanatory text, no markdown formatting.
        
        Return as JSON object with this structure:
        {{
          "input": "test input",
          "expected_output": "expected behavior",
          "reasoning": "why this is a complexity test",
          "complexity": "complexity_level"
        }}
        
        Validate your JSON before responding. Ensure all strings are properly quoted and all brackets are closed.
        """
        
        try:
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=3
            )
            
            if isinstance(response, dict):
                return TestSeed(
                    input=response.get("input", ""),
                    expected_output=response.get("expected_output", ""),
                    reasoning=response.get("reasoning", ""),
                    category="complexity",
                    metadata={"complexity": response.get("complexity", "moderate")}
                )
            else:
                print(f"[ComplexityGenerator] Invalid response format: {type(response)}")
                return None
                
        except Exception as e:
            print(f"[ComplexityGenerator] Single seed generation failed: {e}")
            return None


class ContextGenerator:
    """Generates context-based test seeds."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    async def generate_seeds(self, analysis: SeedAnalysis, count: int = 10) -> SeedGenerationResult:
        """Generate context-based seeds using LLM."""
        
        prompt = f"""
        You are a test designer for a general-purpose analysis tool. Your job is to create context-based test seeds with complete, realistic data that challenge the tool's ability to handle different contexts and scenarios.
        
        ---
        SYSTEM MESSAGE:
        {analysis.system_message}
        USER MESSAGE:
        {analysis.user_message}
        DOMAIN: {analysis.domain}
        OUTPUT FORMAT: {analysis.output_format}
        PLACEHOLDERS: {analysis.placeholders}
        CONSTRAINTS: {analysis.constraints}
        KEY REQUIREMENTS: {analysis.key_requirements}
        ---
        
        Create seeds that test different contexts with COMPLETE, REALISTIC data for the ACTUAL PROMPT above:
        1. General context: Broad, cross-domain scenarios with complete datasets
        2. Specific domain context: Industry-specific scenarios (e.g., healthcare, education, retail) with realistic domain data
        3. Regulatory context: Different countries, compliance regimes, or reporting standards with proper information
        4. Operational context: High-volume, high-complexity, or crisis events with realistic operational data
        5. Temporal context: Time-sensitive scenarios (e.g., end-of-period, post-event) with complete temporal data
        
        For each seed, provide:
        - input: The test input (realistic data, scenario, or user query)
        - expected_output: What the tool should produce (including context-aware analysis, warnings, or errors)
        - reasoning: Why this is a context test and what it challenges
        - context_level: The context type (general/specific/regulatory/operational/temporal)
        
        CRITICAL: You must respond with ONLY valid JSON. No explanatory text, no markdown formatting.
        
        Return as JSON array with this structure:
        [
          {{
            "input": "test input",
            "expected_output": "expected behavior",
            "reasoning": "why this is a context test",
            "context_level": "context_type"
          }}
        ]
        
        Validate your JSON before responding. Ensure all strings are properly quoted and all brackets are closed.
        """
        
        try:
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=3
            )
            
            seeds = []
            for item in response:
                seeds.append(TestSeed(
                    input=item.get("input", ""),
                    expected_output=item.get("expected_output", ""),
                    reasoning=item.get("reasoning", ""),
                    category="context",
                    metadata={"context_level": item.get("context_level", "general")}
                ))
            
            return SeedGenerationResult(seeds=seeds[:count])
            
        except Exception as e:
            print(f"[ContextGenerator] LLM generation failed: {e}, using fallback")
            # Fallback to simple generation
            seeds = []
            for i in range(count):
                seeds.append(TestSeed(
                    input=f"Context input {i + 1}",
                    expected_output="Should handle context appropriately",
                    reasoning="Testing different context scenarios",
                    category="context",
                    metadata={"context_level": "general"}
                ))
            return SeedGenerationResult(seeds=seeds)
    
    async def generate_single_seed(self, analysis: SeedAnalysis, seed_number: int, category: str, max_llm_retries: int = 3) -> Optional[TestSeed]:
        """Generate a single context-based test seed."""
        try:
            prompt = f"""
            You are a test designer for a general-purpose analysis tool. Your job is to create a single context-based test seed that challenges the tool's ability to handle different contexts and scenarios.
            
            ---
            SYSTEM MESSAGE:
            {analysis.system_message}
            USER MESSAGE:
            {analysis.user_message}
            DOMAIN: {analysis.domain}
            OUTPUT FORMAT: {analysis.output_format}
            PLACEHOLDERS: {analysis.placeholders}
            CONSTRAINTS: {analysis.constraints}
            KEY REQUIREMENTS: {analysis.key_requirements}
            ---
            
            Create a context test that targets:
            - Industry-specific or regulatory scenarios
            - Operational complexity or crisis events
            - Time-sensitive or reporting period scenarios
            
            Provide ONE seed with:
            - input: The test input (realistic data, scenario, or user query)
            - expected_output: What the tool should produce (including context-aware analysis, warnings, or errors)
            - reasoning: Why this is a good test case and what it validates
            - context_level: The context type (general/specific/regulatory/operational/temporal)
            
            CRITICAL: You must respond with ONLY valid JSON. No explanatory text, no markdown formatting.
            
            Return as JSON object with this structure:
            {{
              "input": "test input",
              "expected_output": "expected behavior",
              "reasoning": "why this is a context test",
              "context_level": "context_type"
            }}
            
            Validate your JSON before responding. Ensure all strings are properly quoted and all brackets are closed.
            """
            
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=max_llm_retries
            )
            
            if isinstance(response, dict):
                return TestSeed(
                    input=response.get("input", ""),
                    expected_output=response.get("expected_output", ""),
                    reasoning=response.get("reasoning", ""),
                    category=category,
                    metadata={"context_level": response.get("context_level", "general")}
                )
            elif isinstance(response, list) and len(response) > 0:
                item = response[0]
                return TestSeed(
                    input=item.get("input", ""),
                    expected_output=item.get("expected_output", ""),
                    reasoning=item.get("reasoning", ""),
                    category=category,
                    metadata={"context_level": item.get("context_level", "general")}
                )
            else:
                print(f"[ContextGenerator] Invalid response format: {type(response)}")
                return None
                
        except Exception as e:
            print(f"[ContextGenerator] Single seed generation failed: {e}")
            return None


class AlignmentValidator:
    """Validates alignment between generated seeds and requirements."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    async def validate_alignment(self, seeds: List[TestSeed], requirements_doc: Dict[str, Any], prompt_json: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that seeds align with requirements document."""
        
        # Extract requirements information
        objectives = requirements_doc.get("objectives", [])
        requirements = requirements_doc.get("requirements", [])
        constraints = requirements_doc.get("constraints", [])
        
        # Extract prompt information
        placeholders = []
        if "user_message" in prompt_json:
            import re
            pattern = r'\{\{([^}]+)\}\}'
            matches = re.findall(pattern, prompt_json["user_message"])
            placeholders = [f"{{{{{match}}}}}" for match in matches]
        
        # Validate each seed
        aligned_seeds = []
        misaligned_seeds = []
        
        for seed in seeds:
            is_aligned = (
                self._check_objective_alignment(seed, objectives) and
                self._check_requirement_alignment(seed, requirements) and
                self._check_constraint_alignment(seed, constraints) and
                self._check_placeholder_alignment(seed, placeholders)
            )
            
            if is_aligned:
                aligned_seeds.append(seed)
            else:
                misaligned_seeds.append(seed)
        
        # Calculate alignment metrics
        total_seeds = len(seeds)
        aligned_count = len(aligned_seeds)
        alignment_score = aligned_count / total_seeds if total_seeds > 0 else 0.0
        return {
            "alignment_score": alignment_score,
            "aligned_seeds": aligned_seeds,
            "misaligned_seeds": misaligned_seeds,
            "total_seeds": total_seeds,
            "aligned_count": aligned_count,
            "objectives_checked": objectives,
            "requirements_checked": requirements,
            "constraints_checked": constraints,
            "placeholders_checked": placeholders
        }
    
    def _check_objective_alignment(self, seed: TestSeed, objectives: List[str]) -> bool:
        """Check if seed aligns with objectives."""
        # Simple keyword matching for now
        seed_text = f"{seed.input} {seed.expected_output} {seed.reasoning}".lower()
        return any(obj.lower() in seed_text for obj in objectives)
    
    def _check_requirement_alignment(self, seed: TestSeed, requirements: List[str]) -> bool:
        """Check if seed aligns with requirements."""
        seed_text = f"{seed.input} {seed.expected_output} {seed.reasoning}".lower()
        return any(req.lower() in seed_text for req in requirements)
    
    def _check_constraint_alignment(self, seed: TestSeed, constraints: List[str]) -> bool:
        """Check if seed aligns with constraints."""
        seed_text = f"{seed.input} {seed.expected_output} {seed.reasoning}".lower()
        return any(constraint.lower() in seed_text for constraint in constraints)
    
    def _check_placeholder_alignment(self, seed: TestSeed, placeholders: List[str]) -> bool:
        """Check if seed uses required placeholders."""
        if not placeholders:
            return True  # No placeholders to check
        seed_text = f"{seed.input} {seed.expected_output}".lower()
        return any(placeholder.lower() in seed_text for placeholder in placeholders)


class ValidationAgent:
    """Validates the structure and quality of generated seeds."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    async def validate_seeds(self, seeds: List[TestSeed]) -> Dict[str, Any]:
        """Validate seeds for quality and consistency."""
        
        if not seeds:
            return {
                "valid_seeds": [],
                "invalid_seeds": [],
                "validation_score": 0.0,
                "total_seeds": 0,
                "valid_count": 0
            }
        
        valid_seeds = []
        invalid_seeds = []
        
        for seed in seeds:
            is_valid = await self._validate_single_seed(seed)
            if is_valid:
                valid_seeds.append(seed)
            else:
                invalid_seeds.append(seed)
        
        total_seeds = len(seeds)
        valid_count = len(valid_seeds)
        validation_score = valid_count / total_seeds if total_seeds > 0 else 0.0
        
        return {
            "valid_seeds": valid_seeds,
            "invalid_seeds": invalid_seeds,
            "validation_score": validation_score,
            "total_seeds": total_seeds,
            "valid_count": valid_count
        }
    
    async def _validate_single_seed(self, seed: TestSeed) -> bool:
        """Validate a single seed using LLM."""
        prompt = f"""
        Validate this test seed for quality and consistency:
        
        Input: {seed.input}
        Expected Output: {seed.expected_output}
        Reasoning: {seed.reasoning}
        Category: {seed.category}
        
        Check for:
        1. Input is clear and testable
        2. Expected output is reasonable
        3. Reasoning is logical
        4. Category is appropriate
        5. No obvious errors or contradictions
        
        Respond with ONLY a JSON object:
        {{
          "is_valid": true/false,
          "reason": "brief explanation"
        }}
        """
        
        try:
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=2
            )
            
            if isinstance(response, dict):
                return response.get("is_valid", False)
            else:
                return False
            
        except Exception as e:
            print(f"[ValidationAgent] Seed validation failed: {e}")
            return False


class QualityAssessor:
    """Assesses the overall quality of generated seeds."""
    
    def __init__(self, model: str = "o4-mini-2025-04-16", temperature: float = 1.0, max_tokens: int = 2000, delay_between_calls: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.llm_client = LLMClient(delay_between_calls=delay_between_calls)
    
    async def assess_quality(self, seeds: List[TestSeed]) -> Dict[str, Any]:
        """Assess the overall quality of generated seeds."""
        
        if not seeds:
            return {
                "overall_score": 0.0,
                "category_scores": {},
                "total_seeds": 0,
                "quality_metrics": {}
            }
        
        # Group seeds by category
        categories = {}
        for seed in seeds:
            if seed.category not in categories:
                categories[seed.category] = []
            categories[seed.category].append(seed)
        
        # Assess each category
        category_scores = {}
        for category, category_seeds in categories.items():
            score = await self._calculate_category_score(category_seeds)
            category_scores[category] = score
        
        # Calculate overall score
        if category_scores:
            overall_score = sum(category_scores.values()) / len(category_scores)
        else:
            overall_score = 0.0
        
        # Calculate additional metrics
        quality_metrics = await self._calculate_quality_metrics(seeds)
        
        return {
            "overall_score": overall_score,
            "category_scores": category_scores,
            "total_seeds": len(seeds),
            "quality_metrics": quality_metrics
        }
    
    async def _calculate_category_score(self, seeds: List[TestSeed]) -> float:
        """Calculate quality score for a category of seeds."""
        if not seeds:
            return 0.0
        
        prompt = f"""
        Assess the quality of these {len(seeds)} test seeds in the '{seeds[0].category}' category:
        
        Seeds:
        {json.dumps([{
            "input": seed.input,
            "expected_output": seed.expected_output,
            "reasoning": seed.reasoning,
            "metadata": seed.metadata
        } for seed in seeds], indent=2)}
        
        Rate the overall quality on a scale of 1-10, considering:
        1. Relevance to the category
        2. Clarity and testability
        3. Diversity and coverage
        4. Logical reasoning
        5. Practical usefulness
        
        Respond with ONLY a JSON object:
        {{
          "score": 7.5,
          "reasoning": "brief explanation"
        }}
        """
        
        try:
            response = await self.llm_client.call_llm_with_json_response(
                prompt=prompt,
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                max_retries=2
            )
            
            if isinstance(response, dict):
                return float(response.get("score", 5.0))
            else:
                return 5.0  # Default score
            
        except Exception as e:
            print(f"[QualityAssessor] Category scoring failed: {e}")
            return 5.0  # Default score
    
    async def _calculate_quality_metrics(self, seeds: List[TestSeed]) -> Dict[str, Any]:
        """Calculate additional quality metrics."""
        metrics = {
            "diversity_score": 0.0,
            "coverage_score": 0.0,
            "complexity_distribution": {},
            "category_distribution": {}
        }
        
        # Calculate category distribution
        category_counts = {}
        for seed in seeds:
            category_counts[seed.category] = category_counts.get(seed.category, 0) + 1
        
        metrics["category_distribution"] = category_counts
        
        # Calculate complexity distribution (if available)
        complexity_counts = {}
        for seed in seeds:
            complexity = seed.metadata.get("complexity", "unknown")
            complexity_counts[complexity] = complexity_counts.get(complexity, 0) + 1
        
        metrics["complexity_distribution"] = complexity_counts
        
        # Simple diversity score based on unique inputs
        unique_inputs = len(set(str(seed.input) for seed in seeds))
        metrics["diversity_score"] = unique_inputs / len(seeds) if seeds else 0.0
        
        # Simple coverage score based on categories
        metrics["coverage_score"] = len(category_counts) / 3.0  # Assuming 3 main categories
        
        return metrics 