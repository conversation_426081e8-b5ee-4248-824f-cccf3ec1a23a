"""
Configuration management for Synthetic Data Generator.

Loads and manages configuration settings from config.yaml.
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional


class Config:
    """Configuration manager for synthetic data generator."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration from file."""
        if config_path is None:
            config_path = Path(__file__).parent / "config.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Config file not found: {self.config_path}")
        
        with open(self.config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        print(f"[CONFIG] Loaded config from: {self.config_path}")
        return config
    
    def get_default_model(self) -> str:
        """Get default model name."""
        return self.config.get("default_model", "gpt-4o")
    
    def get_default_temperature(self) -> float:
        """Get default temperature."""
        return self.config.get("default_temperature", 0.7)
    
    def get_role_config(self, role_name: str) -> Dict[str, Any]:
        """Get configuration for a specific role."""
        role_models = self.config.get("role_models", {})
        role_config = role_models.get(role_name, {})
        
        # Merge with defaults
        default_config = {
            "model": self.get_default_model(),
            "temperature": self.get_default_temperature(),
            "max_tokens": 2000
        }
        
        default_config.update(role_config)
        return default_config
    
    def get_workflow_config(self) -> Dict[str, Any]:
        """Get workflow configuration."""
        return self.config.get("workflow", {})
    
    def get_categories_config(self) -> Dict[str, Any]:
        """Get categories configuration."""
        return self.config.get("categories", {})
    
    def get_quality_thresholds(self) -> Dict[str, Any]:
        """Get quality thresholds."""
        return self.config.get("quality_thresholds", {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration."""
        return self.config.get("output", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get("logging", {})
    
    def get_uniqueness_config(self) -> Dict[str, Any]:
        """Get uniqueness calculation configuration."""
        return self.config.get("uniqueness", {})
    
    def get_all_role_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all role configurations."""
        role_models = self.config.get("role_models", {})
        role_configs = {}
        
        for role_name in role_models:
            role_configs[role_name] = self.get_role_config(role_name)
        
        return role_configs
    
    def print_role_configs(self):
        """Print all role configurations for debugging."""
        role_configs = self.get_all_role_configs()
        print("[CONFIG] Role configurations:")
        for role_name, config in role_configs.items():
            print(f"  {role_name}:")
            print(f"    Model: {config.get('model', 'N/A')}")
            print(f"    Temperature: {config.get('temperature', 'N/A')}")
            print(f"    Max Tokens: {config.get('max_tokens', 'N/A')}")
            print(f"    Description: {config.get('description', 'N/A')}")
            print() 

    def get_max_llm_retries(self) -> int:
        """Get the maximum number of LLM retries from workflow config."""
        workflow = self.get_workflow_config()
        return workflow.get("max_llm_retries", 3)
    
    def get_api_call_delay(self) -> float:
        """Get the delay between API calls to prevent rate limiting."""
        workflow = self.get_workflow_config()
        return workflow.get("api_call_delay", 0.5)  # Default 0.5 seconds 