"""
Test script for Synthetic Data Generator.

Demonstrates how to use the synthetic data generator with example data.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
import json
from core import SeedGenerator


def test_synthetic_data_generator():
    """Test the synthetic data generator with example data."""
    
    # Example refined prompt JSON (from our previous test)
    prompt_json = {
        "version": "1.0.0",
        "timestamp": "",
        "workflow_type": "standard",
        "system_message": "You are an International Tax Analyst, a senior tax policy expert specializing in global tax systems. Analytical, precise, and objective tone. Quality standards: ≥95% data accuracy, comprehensive coverage of all sovereign nations, citations from authoritative sources (e.g., OECD, IMF, national tax authorities). Constraints: no speculation or hallucination; if data is unavailable for an entry, respond with \"Insufficient data.\"",
        "user_message": "Identify and rank the top {{N}} countries with the lowest effective personal and corporate tax rates worldwide as of {{year}}. For each country, provide:\n1. Country name\n2. Effective personal income tax rate (%)\n3. Effective corporate income tax rate (%)\n4. Source citation (Markdown footnote)\nOutput format: a Markdown table with columns \"Country | Personal Rate (%) | Corporate Rate (%) | Source.\"  \nSuccess criteria:  \n- The list reflects the lowest tax rates based on verified {{year}} data  \n- Table is correctly formatted in Markdown  \n- All data points include valid footnote citations  \nConstraints: limit to sovereign nations (exclude territories), no speculative estimates, adhere strictly to footnote citation format, and use \"Insufficient data\" for missing entries.",
        "metadata": {
            "role": "an International Tax Analyst",
            "tone": "professional",
            "domain": "technical",
            "output_format": "table",
            "constraints": [
                "no speculation or hallucination; if data is unavailable for an entry, respond with \"Insufficient data",
                "citations_required",
                "accuracy_required",
                "limit to sovereign nations (exclude territories), no speculative estimates, adhere strictly to footnote citation format, and use \"Insufficient data\" for missing entries"
            ],
            "placeholders": [
                "{{N}}",
                "{{year}}",
                "{{year}}"
            ],
            "estimated_tokens": 227,
            "quality_score": 8.3,
            "token_savings": 0,
            "qa_passed": False,
            "domain_optimized": False
        },
        "execution_info": {
            "total_turns": 2,
            "roles_used": [
                "EnhancedWriter",
                "Critic"
            ],
            "termination_reason": "",
            "target_score": 8.0,
            "final_score": 8.3
        }
    }
    
    # Example requirements document JSON (from our previous test)
    requirements_doc = {
        "requirements_doc": {
            "problem_statement": "",
            "core_objectives": [],
            "solution_approach": "Standard solution approach",
            "key_requirements": [],
            "functional_requirements": [],
            "non_functional_requirements": [],
            "constraints": [],
            "assumptions": [],
            "dependencies": [],
            "stakeholders": [],
            "success_criteria": [],
            "complexity_level": "moderate",
            "priority_level": "medium",
            "domain": None,
            "industry": None,
            "regulatory_requirements": [],
            "created_at": "2025-07-12T14:14:55.110104",
            "version": "1.0.0"
        },
        "workflow_expectations": {
            "input_format": "Text input",
            "output_format": "Structured output",
            "input_validation_rules": [
                "Validate input format"
            ],
            "output_validation_rules": [
                "Validate output quality"
            ],
            "processing_steps": [
                "Process input",
                "Generate output"
            ],
            "decision_points": [],
            "error_handling": {
                "general": "Graceful degradation"
            },
            "performance_expectations": {
                "response_time": "2 seconds"
            },
            "scalability_requirements": {},
            "integration_points": [
                "API integration"
            ],
            "deployment_requirements": [],
            "user_experience_goals": [],
            "accessibility_requirements": []
        },
        "quality_metrics": {
            "accuracy_threshold": 0.9,
            "precision_threshold": 0.85,
            "recall_threshold": 0.85,
            "completeness_score": 0.9,
            "relevance_score": 0.85,
            "consistency_score": 0.9,
            "response_time_threshold": 2.0,
            "throughput_requirements": {},
            "validation_criteria": [
                "Output meets requirements",
                "Response time acceptable"
            ],
            "acceptance_criteria": [
                "All requirements satisfied",
                "Quality thresholds met"
            ],
            "test_scenarios": [
                "Standard use case",
                "Edge case handling"
            ],
            "quality_dimensions": {},
            "risk_factors": [
                "Data quality issues",
                "Performance degradation"
            ],
            "monitoring_metrics": [
                "Response time",
                "Accuracy rate"
            ],
            "feedback_mechanisms": []
        },
        "metadata": {
            "original_prompt": "Create an improvement of this prompt: which are the countries that have the lowest tax rates in the world",
            "generated_at": "2025-07-12T14:15:11.842278",
            "version": "1.0.0",
            "validation_status": {
                "completeness": True,
                "consistency": True,
                "feasibility": True,
                "alignment": True
            },
            "validation_issues": []
        }
    }
    
    # Generate seeds
    print("Testing Synthetic Data Generator...")
    print("=" * 50)
    
    generator = SeedGenerator()
    result = generator.generate_seeds(
        prompt_json=prompt_json,
        requirements_doc=requirements_doc
    )
    
    # Display results
    print(f"\nGenerated {result['metadata']['total_seeds']} seeds across {len(result['metadata']['categories'])} categories")
    print(f"Categories: {', '.join(result['metadata']['categories'])}")
    
    # Show seeds from each category with new structure
    for category_name, category_data in result['seeds'].items():
        category_info = category_data['category_info']
        seeds = category_data['seeds']
        
        print(f"\n{category_name.upper()} ({category_info['count']} seeds):")
        print(f"Description: {category_info['description']}")
        print("-" * 50)
        
        for seed in seeds:
            print(f"ID: {seed['id']}")
            print(f"Input: {seed['input']}")
            print(f"Expected: {seed['expected_output']}")
            print(f"Reasoning: {seed['reasoning']}")
            print(f"Quality Metrics:")
            print(f"  - Complexity: {seed['quality_metrics']['complexity']}")
            print(f"  - Relevance: {seed['quality_metrics']['relevance']:.2f}")
            print(f"  - Uniqueness: {seed['quality_metrics']['uniqueness']:.2f}")
            print()
    
    # Show metadata
    print("METADATA:")
    print(f"- Quality Score: {result['metadata']['quality_result'].get('overall_score', 'N/A')}")
    print(f"- Alignment: {result['metadata']['alignment_result'].get('aligned', 'N/A')}")
    print(f"- Validation: {result['metadata']['validation_result'].get('valid', 'N/A')}")
    
    # Show selection info if available
    if 'selection_info' in result['metadata'] and result['metadata']['selection_info']:
        selection_info = result['metadata']['selection_info']
        print(f"- Seed Selection: {'Enabled' if selection_info.get('enabled') else 'Disabled'}")
        if selection_info.get('enabled'):
            print(f"  - Selection Criteria: {', '.join(selection_info.get('criteria', []))}")
            print(f"  - Total Generated: {selection_info.get('total_generated', 'N/A')}")
            print(f"  - Total Selected: {selection_info.get('total_selected', 'N/A')}")
    
    # Save to JSON file
    output_file = "synthetic_seeds_output.json"
    with open(output_file, 'w') as f:
        json.dump(result, f, indent=2)
    print(f"\nComplete nested JSON structure saved to: {output_file}")
    
    return result


if __name__ == "__main__":
    test_synthetic_data_generator() 