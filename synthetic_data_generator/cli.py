"""
CLI for Synthetic Data Generator.

Provides a command-line interface for generating test seeds from refined prompts.
"""

import argparse
import json
import sys
import asyncio
from pathlib import Path
from typing import Dict, Any

from .core import SeedGenerator


async def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Synthetic Data Generator for Test Seeds",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate seeds from prompt JSON and requirements
  python -m synthetic_data_generator.cli --prompt prompt.json --requirements requirements.json
  
  # Save seeds to file
  python -m synthetic_data_generator.cli --prompt prompt.json --requirements requirements.json --output seeds.json
  
  # Generate with custom iterations
  python -m synthetic_data_generator.cli --prompt prompt.json --requirements requirements.json --iterations 5
        """
    )
    
    # Required arguments
    parser.add_argument(
        "--prompt",
        type=Path,
        required=True,
        help="Path to refined prompt JSON file"
    )
    parser.add_argument(
        "--requirements",
        type=Path,
        required=True,
        help="Path to requirements document JSON file"
    )
    
    # Optional arguments
    parser.add_argument(
        "--output",
        type=Path,
        help="Output file for generated seeds (default: stdout)"
    )
    parser.add_argument(
        "--iterations",
        type=int,
        default=None,
        help="Maximum iterations for refinement (default: use config.yaml setting)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Verbose output"
    )
    
    args = parser.parse_args()
    
    try:
        # Load prompt JSON
        with open(args.prompt, 'r') as f:
            prompt_json = json.load(f)
        
        # Load requirements JSON
        with open(args.requirements, 'r') as f:
            requirements_doc = json.load(f)
        
        # Generate seeds
        generator = SeedGenerator()
        result = await generator.generate_seeds(
            prompt_json=prompt_json,
            requirements_doc=requirements_doc,
            max_iterations=args.iterations
        )
        
        # Output results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"Seeds saved to {args.output}")
        else:
            print(json.dumps(result, indent=2))
        
        if args.verbose:
            print(f"\nGenerated {result['metadata']['total_seeds']} seeds across {len(result['metadata']['categories'])} categories")
            if result['metadata']['quality_result']:
                print(f"Quality score: {result['metadata']['quality_result'].get('overall_score', 'N/A')}")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 