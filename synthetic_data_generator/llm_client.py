"""
LLM Client for Synthetic Data Generator.

Handles OpenAI API calls with proper error handling, retry logic, and rate limiting.
"""

import os
import time
import json
import asyncio
from typing import Dict, Any, Optional, List
from openai import AsyncOpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class LLMClient:
    """Client for making LLM API calls with error handling and retries."""
    
    def __init__(self, delay_between_calls: float = 0.5):
        """
        Initialize the LLM client.
        
        Args:
            delay_between_calls: Delay in seconds between API calls to avoid rate limiting
        """
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")
        
        self.client = AsyncOpenAI(api_key=api_key)
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds
        self.delay_between_calls = delay_between_calls
        self.last_call_time = 0.0
    
    async def _rate_limit_delay(self):
        """Add delay between API calls to avoid rate limiting."""
        current_time = time.time()
        time_since_last_call = current_time - self.last_call_time
        
        if time_since_last_call < self.delay_between_calls:
            delay_needed = self.delay_between_calls - time_since_last_call
            print(f"[RATE LIMIT] Waiting {delay_needed:.2f}s to avoid rate limiting...")
            await asyncio.sleep(delay_needed)
        
        self.last_call_time = time.time()
    
    async def call_llm(self, 
                 prompt: str, 
                 model: str = "o4-mini-2025-04-16",
                 temperature: float = 1.0,
                 max_tokens: int = 2000,
                 system_message: Optional[str] = None) -> str:
        """
        Make an LLM API call with retry logic.
        
        Args:
            prompt: The user prompt
            model: The model to use
            temperature: Creativity level (0.0-2.0)
            max_tokens: Maximum tokens for response
            system_message: Optional system message
            
        Returns:
            str: The LLM response
            
        Raises:
            Exception: If all retries fail
        """
        
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        for attempt in range(self.max_retries):
            try:
                # Add rate limiting delay
                await self._rate_limit_delay()
                
                print(f"[LLM CALL] Role: {self._get_calling_role()} | Model: {model} | max_tokens: {max_tokens} | temperature: {temperature}")
                
                # Use max_completion_tokens for newer models, max_tokens for older ones
                if model.startswith("o4-") or model.startswith("gpt-4o"):
                    response = await self.client.chat.completions.create(
                        model=model,
                        messages=messages,
                        temperature=temperature,
                        max_completion_tokens=max_tokens
                    )
                else:
                    response = await self.client.chat.completions.create(
                        model=model,
                        messages=messages,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )
                
                content = response.choices[0].message.content.strip()
                if not content:
                    print(f"[LLM CLIENT] Empty content in response from {model}")
                return content
                
            except Exception as e:
                print(f"[LLM ERROR] Attempt {attempt + 1}/{self.max_retries}: {str(e)}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    raise Exception(f"LLM call failed after {self.max_retries} attempts: {str(e)}")
    
    async def call_llm_with_json_response(self, 
                                   prompt: str,
                                   model: str = "o4-mini-2025-04-16",
                                   temperature: float = 1.0,
                                   max_tokens: int = 2000,
                                   system_message: Optional[str] = None,
                                   max_retries: int = 3) -> Dict[str, Any]:
        """
        Make an LLM API call and parse JSON response with retry logic.
        
        Args:
            prompt: The user prompt
            model: The model to use
            temperature: Creativity level
            max_tokens: Maximum tokens for response
            system_message: Optional system message
            max_retries: Maximum number of retries for failed calls
            
        Returns:
            Dict: Parsed JSON response
            
        Raises:
            Exception: If response is not valid JSON after all retries
        """
        
        # Add JSON-specific system message if not provided
        if not system_message:
            system_message = """You are a JSON generator. You must ALWAYS respond with valid JSON only. 
            - No explanatory text before or after the JSON
            - No markdown formatting
            - No code blocks
            - Just pure, valid JSON
            - Ensure all strings are properly quoted
            - Ensure all arrays and objects are properly closed
            - Validate your JSON before responding"""
        
        for attempt in range(max_retries + 1):
            try:
                response = await self.call_llm(prompt, model, temperature, max_tokens, system_message)
                
                # If response is already a dict or list, return it
                if isinstance(response, (dict, list)):
                    return response
                
                # Debug: Check if response is empty
                if not response or response.strip() == "":
                    print(f"[LLM CLIENT] Empty response received from LLM")
                    continue
                
                # Try to parse JSON directly
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    print(f"[LLM CLIENT] Direct JSON parsing failed, response length: {len(response)}")
                    if len(response) < 100:  # Only show short responses to avoid spam
                        print(f"[LLM CLIENT] Response preview: {repr(response[:100])}")
                    pass
                
                # Try to extract JSON from response
                json_str = self._extract_json_from_response(response)
                if json_str:
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        pass
                
                # Try to fix common JSON issues
                fixed_json = self._fix_common_json_issues(response)
                if fixed_json:
                    try:
                        return json.loads(fixed_json)
                    except json.JSONDecodeError:
                        pass
                
                # If still failing and not the last attempt, try to fix with LLM
                if attempt < max_retries:
                    print(f"[LLM CLIENT] JSON parsing failed on attempt {attempt + 1}, trying to fix with LLM...")
                    try:
                        fixed_response = await self._fix_json_with_llm(response, model, temperature)
                        # Check if fixed_response is empty or whitespace
                        if not fixed_response or fixed_response.strip() == "":
                            print(f"[LLM CLIENT] LLM JSON fix returned empty response")
                            continue
                        return json.loads(fixed_response)
                    except (json.JSONDecodeError, Exception) as e:
                        print(f"[LLM CLIENT] LLM JSON fix failed: {e}")
                        continue
                else:
                    # Final attempt failed, use fallback
                    print(f"[LLM CLIENT] All attempts failed, using fallback JSON")
                    return self._generate_fallback_json()
                        
            except Exception as e:
                if attempt < max_retries:
                    print(f"[LLM CLIENT] LLM call failed on attempt {attempt + 1}: {e}")
                    continue
                else:
                    print(f"[LLM CLIENT] All retries failed, using fallback JSON")
                    return self._generate_fallback_json()
        
        # Should never reach here, but just in case
        return self._generate_fallback_json()
    
    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """
        Extract JSON from response using multiple methods.
        
        Args:
            response: The raw LLM response
            
        Returns:
            Optional[str]: Extracted JSON string or None
        """
        if not response or response.strip() == "":
            return None
        
        # Method 1: Look for ```json blocks
        if "```json" in response:
            json_start = response.find("```json") + 7
            json_end = response.find("```", json_start)
            if json_end > json_start:
                return response[json_start:json_end].strip()
        
        # Method 2: Look for ``` blocks (without json specifier)
        elif "```" in response:
            json_start = response.find("```") + 3
            json_end = response.find("```", json_start)
            if json_end > json_start:
                return response[json_start:json_end].strip()
        
        # Method 3: Try to find JSON array or object in the response
        else:
            # Look for array start
            array_start = response.find("[")
            if array_start != -1:
                # Find matching closing bracket
                bracket_count = 0
                for i in range(array_start, len(response)):
                    if response[i] == "[":
                        bracket_count += 1
                    elif response[i] == "]":
                        bracket_count -= 1
                        if bracket_count == 0:
                            return response[array_start:i+1]
            
            # If no array found, look for object
            object_start = response.find("{")
            if object_start != -1:
                # Find matching closing brace
                brace_count = 0
                for i in range(object_start, len(response)):
                    if response[i] == "{":
                        brace_count += 1
                    elif response[i] == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            return response[object_start:i+1]
        
        # If still no JSON found, try the whole response
        return response.strip()
    
    def _fix_common_json_issues(self, response: str) -> Optional[str]:
        """Try to fix common JSON issues in the response."""
        if not response or response.strip() == "":
            return None
        
        # Remove common prefixes/suffixes
        json_str = response.strip()
        
        # Remove markdown code blocks
        if "```json" in json_str:
            start = json_str.find("```json") + 7
            end = json_str.find("```", start)
            if end > start:
                json_str = json_str[start:end].strip()
        elif "```" in json_str:
            start = json_str.find("```") + 3
            end = json_str.find("```", start)
            if end > start:
                json_str = json_str[start:end].strip()
        
        # Remove explanatory text before/after JSON
        lines = json_str.split('\n')
        json_lines = []
        in_json = False
        
        for line in lines:
            line = line.strip()
            if line.startswith('{') or line.startswith('['):
                in_json = True
            if in_json:
                json_lines.append(line)
            if line.endswith('}') or line.endswith(']'):
                in_json = False
        
        json_str = '\n'.join(json_lines)
        
        # Fix common issues
        json_str = json_str.replace('\n', ' ').replace('\r', ' ')
        json_str = json_str.replace('\\n', ' ').replace('\\r', ' ')
        
        # Fix unquoted keys
        import re
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)
        
        # Fix trailing commas
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        # Fix single quotes to double quotes
        json_str = json_str.replace("'", '"')
        
        return json_str if json_str else None
    
    async def _fix_json_with_llm(self, broken_json: str, model: str, temperature: float) -> str:
        """Use LLM to fix broken JSON without changing the content."""
        fix_prompt = f"""
        Fix this broken JSON. Keep the exact same content and meaning, just fix the syntax:
        
        Broken JSON:
        {broken_json}
        
        Rules:
        1. Keep all the original content exactly the same
        2. Only fix syntax errors (missing quotes, brackets, commas)
        3. Do not add, remove, or change any content
        4. Return ONLY the fixed JSON, no explanations
        
        Fixed JSON:
        """
        
        try:
            # NOTE: The model used here is the same as the main generation call (from config for this role).
            # Use temperature=1.0 for o4 models as they don't support other values
            temp = 1.0 if model.startswith("o4") else 0.1
            fixed_response = await self.call_llm(
                prompt=fix_prompt,
                model=model,
                temperature=temp,  # Use 1.0 for o4 models, 0.1 for others
                max_tokens=2000
            )
            return fixed_response.strip()
        except Exception as e:
            print(f"[LLM CLIENT] Failed to fix JSON with LLM: {e}")
            return broken_json  # Return original if fixing fails
    
    def _generate_fallback_json(self, role: str = None, single: bool = False) -> object:
        """Generate fallback JSON for a given role. If single=True, return a single object, else a list."""
        # Try to infer the role if not provided
        import inspect
        if role is None:
            stack = inspect.stack()
            for frame in stack:
                if 'self' in frame.frame.f_locals:
                    candidate = frame.frame.f_locals['self']
                    if hasattr(candidate, '__class__'):
                        role = candidate.__class__.__name__
                        break
        # Default fallback for unknown role
        if role is None:
            role = "Unknown"
        # Fallbacks for each generator
        if "EdgeCase" in role:
            obj = {
                "input": "Fallback edge case input",
                "expected_output": "Should handle edge case appropriately",
                "reasoning": "Fallback: boundary/edge scenario",
                "test_type": "fallback_edge_case"
            }
        elif "Complexity" in role:
            obj = {
                "input": "Fallback complexity input",
                "expected_output": "Should handle complexity appropriately",
                "reasoning": "Fallback: complexity scenario",
                "complexity": "moderate"
            }
        elif "Context" in role:
            obj = {
                "input": "Fallback context input",
                "expected_output": "Should handle context appropriately",
                "reasoning": "Fallback: context scenario",
                "context_level": "general"
            }
        else:
            obj = {
                "input": "Fallback input",
                "expected_output": "Fallback output",
                "reasoning": "Fallback reasoning"
            }
        if single:
            return obj
        else:
            return [obj]
    
    def _get_calling_role(self) -> str:
        """Get the name of the calling role for logging."""
        import inspect
        frame = inspect.currentframe()
        
        # Walk up the call stack to find the calling role
        while frame:
            frame = frame.f_back
            if frame and 'self' in frame.f_locals:
                obj = frame.f_locals['self']
                if hasattr(obj, '__class__'):
                    class_name = obj.__class__.__name__
                    if class_name.endswith('Generator') or class_name.endswith('Validator') or class_name.endswith('Agent') or class_name.endswith('Assessor'):
                        return class_name
        
        return "Unknown" 