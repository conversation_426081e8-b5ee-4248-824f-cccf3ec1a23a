"""
Core Synthetic Data Generator.

Orchestrates the multi-agent workflow to generate comprehensive test seeds
from refined prompts and requirements documents.
"""

import json
import numpy as np
import asyncio
import concurrent.futures
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
logger = logging.getLogger(__name__)

from .roles import (
    SeedAnalyzer, EdgeCaseGenerator, ComplexityGenerator, ContextGenerator,
    AlignmentValidator, ValidationAgent, QualityAssessor, TestSeed
)
from .categories import TestCategories, TestCategory
from .config import Config


class SeedGenerator:
    """Main orchestrator for synthetic data generation."""
    
    def __init__(self, config_path: str = None):
        # Load configuration
        self.config = Config(config_path)
        
        # Get API call delay configuration
        api_call_delay = self.config.get_api_call_delay()
        
        # Initialize roles with per-role configuration
        self.roles = {}
        role_configs = self.config.get_all_role_configs()
        
        for role_name, role_config in role_configs.items():
            model = role_config.get("model", "gpt-4o")
            temperature = role_config.get("temperature", 0.7)
            max_tokens = role_config.get("max_tokens", 2000)
            
            if role_name == "SeedAnalyzer":
                self.roles[role_name] = SeedAnalyzer(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
            elif role_name == "EdgeCaseGenerator":
                self.roles[role_name] = EdgeCaseGenerator(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
            elif role_name == "ComplexityGenerator":
                self.roles[role_name] = ComplexityGenerator(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
            elif role_name == "ContextGenerator":
                self.roles[role_name] = ContextGenerator(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
            elif role_name == "AlignmentValidator":
                self.roles[role_name] = AlignmentValidator(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
            elif role_name == "ValidationAgent":
                self.roles[role_name] = ValidationAgent(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
            elif role_name == "QualityAssessor":
                self.roles[role_name] = QualityAssessor(model=model, temperature=temperature, max_tokens=max_tokens, delay_between_calls=api_call_delay)
        
        # Print role configurations for debugging
        self.config.print_role_configs()
        
        # Initialize test categories
        self.test_categories = TestCategories()
        
        # Define workflow sequence
        self.workflow_sequence = [
            "SeedAnalyzer",
            "EdgeCaseGenerator",
            "ComplexityGenerator", 
            "ContextGenerator",
            "AlignmentValidator",
            "ValidationAgent",
            "QualityAssessor"
        ]
        
        # Get workflow configuration
        workflow_config = self.config.get_workflow_config()
        self.max_iterations = workflow_config.get("max_iterations", 1)
        self.seed_count_per_category = workflow_config.get("seed_count_per_category", 10)
        self.enable_seed_selection = workflow_config.get("enable_seed_selection", True)
        self.selection_criteria = workflow_config.get("selection_criteria", ["quality_score", "uniqueness", "relevance"])
        self.enable_individual_calls = workflow_config.get("enable_individual_calls", True)
        self.enable_parallel_calls = workflow_config.get("enable_parallel_calls", True)
        self.calls_per_category = workflow_config.get("calls_per_category", 15)
        self.final_seeds_per_category = workflow_config.get("final_seeds_per_category", 10)
        
        # Load uniqueness configuration
        uniqueness_config = self.config.get_uniqueness_config()
        self.use_semantic_similarity = uniqueness_config.get("use_semantic_similarity", True)
        self.similarity_threshold = uniqueness_config.get("similarity_threshold", 0.8)
        self.embedding_method = uniqueness_config.get("embedding_method", "hash_based")
        self.fallback_to_simple = uniqueness_config.get("fallback_to_simple", True)

        self.max_llm_retries = self.config.get_max_llm_retries()
    
    async def generate_seeds(self, prompt_json: Dict[str, Any], requirements_doc: Dict[str, Any], 
                      max_iterations: int = None) -> Dict[str, Any]:
        """
        Generate comprehensive test seeds from refined prompt and requirements.
        
        Args:
            prompt_json: The refined prompt JSON from the prompt generator
            requirements_doc: The requirements document JSON
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            Dict: Complete test seeds with metadata
        """
        logger.info(f"[SEED GENERATOR] Starting seed generation for prompt...")
        
        # Initialize state
        state = {
            "prompt_json": prompt_json,
            "requirements_doc": requirements_doc,
            "iteration": 0,
            "history": [],
            "all_seeds": [],
            "analysis": None
        }
        
        # Use provided max_iterations or config default
        iterations_to_run = max_iterations if max_iterations is not None else self.max_iterations
        
        # Run workflow sequence
        for iteration in range(iterations_to_run):
            state["iteration"] = iteration + 1
            logger.info(f"[SEED GENERATOR] Iteration {iteration + 1}/{iterations_to_run}")
            
            # Execute each role in sequence
            for role_name in self.workflow_sequence:
                role = self.roles[role_name]
                logger.info(f"[SEED GENERATOR] Running {role_name}...")
                
                try:
                    if role_name == "SeedAnalyzer":
                        # Analyze the prompt
                        analysis = role.analyze_prompt(prompt_json)
                        # Extract requirements asynchronously
                        analysis.key_requirements = await role._extract_requirements(prompt_json.get("system_message", ""))
                        state["analysis"] = analysis
                        logger.info(f"[SEED GENERATOR] {role_name} completed: Analyzed prompt structure")
                        
                    elif role_name in ["EdgeCaseGenerator", "ComplexityGenerator", "ContextGenerator"]:
                        # Generate seeds for each category using individual calls
                        if state["analysis"]:
                            # Process all generator categories in parallel
                            if role_name == "EdgeCaseGenerator":  # Only trigger once for all generators
                                all_generator_seeds = await self._generate_all_categories_parallel(state["analysis"])
                                state["all_seeds"].extend(all_generator_seeds)
                                logger.info(f"[SEED GENERATOR] All generators completed: Generated {len(all_generator_seeds)} total seeds")
                                # Skip the other generators since we processed them all
                                continue
                    
                    elif role_name == "AlignmentValidator":
                        # Validate alignment
                        if state["all_seeds"]:
                            validation_result = await role.validate_alignment(
                                state["all_seeds"], requirements_doc, prompt_json
                            )
                            state["alignment_result"] = validation_result
                            logger.info(f"[SEED GENERATOR] {role_name} completed: Alignment validation")
                            
                            # If not aligned, provide feedback for next iteration
                            if validation_result.get("alignment_score", 1.0) < 0.8:
                                logger.warning(f"[SEED GENERATOR] Alignment issues found: {validation_result.get('misaligned_seeds', [])}")
                    
                    elif role_name == "ValidationAgent":
                        # Validate seed structure
                        if state["all_seeds"]:
                            validation_result = await role.validate_seeds(state["all_seeds"])
                            state["validation_result"] = validation_result
                            logger.info(f"[SEED GENERATOR] {role_name} completed: Structure validation")
                    
                    elif role_name == "QualityAssessor":
                        # Assess overall quality
                        if state["all_seeds"]:
                            quality_result = await role.assess_quality(state["all_seeds"])
                            state["quality_result"] = quality_result
                            logger.info(f"[SEED GENERATOR] {role_name} completed: Quality assessment")
                    
                    # Log the execution
                    state["history"].append({
                        "iteration": iteration + 1,
                        "role": role_name,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                except Exception as e:
                    logger.error(f"[SEED GENERATOR] Error in {role_name}: {str(e)}")
                    state["history"].append({
                        "iteration": iteration + 1,
                        "role": role_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
        
        # Select best seeds if enabled
        if self.enable_seed_selection and state["all_seeds"]:
            logger.info(f"[SEED GENERATOR] Selecting best {self.final_seeds_per_category} seeds per category from {len(state['all_seeds'])} total seeds...")
            selected_seeds = self._select_best_seeds(state["all_seeds"])
            state["all_seeds"] = selected_seeds
            logger.info(f"[SEED GENERATOR] Selected {len(selected_seeds)} best seeds")
        
        # Organize seeds by category
        organized_seeds = self._organize_seeds_by_category(state["all_seeds"])
        
        # Convert any TestSeed objects in alignment_result to dicts
        if state.get("alignment_result") and "misaligned_seeds" in state["alignment_result"]:
            state["alignment_result"]["misaligned_seeds"] = [
                s.to_dict() if hasattr(s, "to_dict") else s for s in state["alignment_result"]["misaligned_seeds"]
            ]

        # Convert any TestSeed objects in validation_result to dicts
        if state.get("validation_result") and "invalid_seeds" in state["validation_result"]:
            state["validation_result"]["invalid_seeds"] = [
                s.to_dict() if hasattr(s, "to_dict") else s for s in state["validation_result"]["invalid_seeds"]
            ]
        if state.get("validation_result") and "valid_seeds" in state["validation_result"]:
            state["validation_result"]["valid_seeds"] = [
                s.to_dict() if hasattr(s, "to_dict") else s for s in state["validation_result"]["valid_seeds"]
            ]

        # Prepare final result
        result = {
            "seeds": organized_seeds,
            "metadata": {
                "total_seeds": len(state["all_seeds"]),
                "categories": list(organized_seeds.keys()),
                "iteration_count": iterations_to_run,
                "workflow_history": state["history"],
                "analysis": self._analysis_to_dict(state["analysis"]) if state["analysis"] else None,
                "alignment_result": state.get("alignment_result"),
                "validation_result": state.get("validation_result"),
                "quality_result": state.get("quality_result")
            }
        }
        
        logger.info(f"[SEED GENERATOR] Completed seed generation: {len(state['all_seeds'])} seeds across {len(organized_seeds)} categories")
        return result
    
    def _organize_seeds_by_category(self, seeds: List[TestSeed]) -> Dict[str, Dict[str, Any]]:
        """Organize seeds by category with detailed structure."""
        organized = {
            "edge_cases": {
                "category_info": {
                    "name": "edge_cases",
                    "description": "Test seeds that explore boundary conditions, error scenarios, and unusual inputs",
                    "count": 0
                },
                "seeds": []
            },
            "complexity_levels": {
                "category_info": {
                    "name": "complexity_levels", 
                    "description": "Test seeds across different complexity levels from simple to expert",
                    "count": 0
                },
                "seeds": []
            },
            "context_variations": {
                "category_info": {
                    "name": "context_variations",
                    "description": "Test seeds with varying levels of context and background information", 
                    "count": 0
                },
                "seeds": []
            }
        }
        
        for i, seed in enumerate(seeds):
            if seed.category in organized:
                seed_data = {
                    "id": f"{seed.category}_{len(organized[seed.category]['seeds']) + 1}",
                    "input": seed.input,
                    "expected_output": seed.expected_output,
                    "reasoning": seed.reasoning,
                    "metadata": seed.metadata,
                    "quality_metrics": {
                        "complexity": self._assess_seed_complexity(seed),
                        "relevance": self._assess_seed_relevance(seed),
                        "uniqueness": self._assess_seed_uniqueness(seed, seeds)
                    }
                }
                
                organized[seed.category]["seeds"].append(seed_data)
                organized[seed.category]["category_info"]["count"] += 1
        
        return organized
    
    def _assess_seed_complexity(self, seed: TestSeed) -> str:
        """Assess the complexity level of a seed."""
        input_length = len(seed.input)
        if input_length < 50:
            return "simple"
        elif input_length < 150:
            return "moderate"
        else:
            return "complex"
    
    def _assess_seed_relevance(self, seed: TestSeed) -> float:
        """Assess how relevant a seed is to the prompt."""
        # Simple relevance scoring based on input length and content
        input_length = len(seed.input)
        if input_length < 20:
            return 0.3
        elif input_length < 100:
            return 0.7
        else:
            return 0.9
    
    def _assess_seed_uniqueness(self, seed: TestSeed, all_seeds: List[TestSeed]) -> float:
        """Assess how unique a seed is compared to others using semantic similarity."""
        if not all_seeds:
            return 1.0
        
        # Use semantic similarity if enabled
        if self.use_semantic_similarity:
            try:
                return self._assess_semantic_uniqueness(seed, all_seeds)
            except Exception as e:
                logger.warning(f"[UNIQUENESS] Semantic similarity failed: {e}")
                if self.fallback_to_simple:
                    return self._assess_simple_uniqueness(seed, all_seeds)
                else:
                    return 0.5  # Default score if semantic fails and no fallback
        else:
            return self._assess_simple_uniqueness(seed, all_seeds)
    
    def _assess_semantic_uniqueness(self, seed: TestSeed, all_seeds: List[TestSeed]) -> float:
        """Assess uniqueness using semantic embeddings."""
        # Get embeddings for all seeds
        seed_embeddings = {}
        for s in all_seeds:
            embedding = self._get_text_embedding(s.input)
            seed_embeddings[s] = embedding
        
        # Calculate similarity scores
        similarities = []
        seed_embedding = seed_embeddings[seed]
        
        for other_seed, other_embedding in seed_embeddings.items():
            if other_seed != seed:
                similarity = self._cosine_similarity(seed_embedding, other_embedding)
                similarities.append(similarity)
        
        if not similarities:
            return 1.0
        
        # Calculate uniqueness based on similarity threshold
        high_similarity_count = sum(1 for sim in similarities if sim > self.similarity_threshold)
        total_comparisons = len(similarities)
        
        if total_comparisons == 0:
            return 1.0
        
        # Uniqueness = 1 - (high_similarity_count / total_comparisons)
        uniqueness = max(0.0, 1.0 - (high_similarity_count / total_comparisons))
        
        return uniqueness
    
    def _assess_simple_uniqueness(self, seed: TestSeed, all_seeds: List[TestSeed]) -> float:
        """Fallback uniqueness assessment using simple text comparison."""
        similar_count = 0
        for other_seed in all_seeds:
            if other_seed != seed:
                # Simple similarity based on input length and first few words
                if (abs(len(str(seed.input)) - len(str(other_seed.input))) < 10 and
                    str(seed.input)[:20] == str(other_seed.input)[:20]):
                    similar_count += 1
        
        return max(1.0 - (similar_count / len(all_seeds)), 0.0)
    
    def _get_text_embedding(self, text: str) -> np.ndarray:
        """Get embedding for text using a simple hash-based approach."""
        # Simple hash-based embedding for now
        # In production, you'd use a proper embedding model
        import hashlib
        
        # Create a simple embedding vector from text hash
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Convert hash to 128-dimensional vector
        embedding = np.zeros(128)
        for i, char in enumerate(text_hash):
            if i < 128:
                embedding[i] = ord(char) / 255.0
        
        return embedding
    
    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors."""
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    def _analysis_to_dict(self, analysis) -> Dict[str, Any]:
        """Convert analysis to dictionary."""
        if not analysis:
            return {}
        
        return {
            "placeholders": analysis.placeholders,
            "constraints": analysis.constraints,
            "domain": analysis.domain,
            "output_format": analysis.output_format,
            "role": analysis.role,
            "tone": analysis.tone,
            "complexity_level": analysis.complexity_level,
            "key_requirements": analysis.key_requirements
        }
    
    async def generate_json(self, prompt_json: Dict[str, Any], requirements_doc: Dict[str, Any], 
                     max_iterations: int = None) -> Dict[str, Any]:
        """
        Generate seeds and return as JSON.
        
        Args:
            prompt_json: The refined prompt JSON
            requirements_doc: The requirements document JSON
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            Dict: JSON representation of the generated seeds
        """
        return await self.generate_seeds(prompt_json, requirements_doc, max_iterations)
    
    async def save_seeds_to_file(self, prompt_json: Dict[str, Any], requirements_doc: Dict[str, Any], 
                          output_file: str, max_iterations: int = None) -> str:
        """
        Generate seeds and save to JSON file.
        
        Args:
            prompt_json: The refined prompt JSON
            requirements_doc: The requirements document JSON
            output_file: Path to output file
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            str: Path to saved file
        """
        result = await self.generate_seeds(prompt_json, requirements_doc, max_iterations)
        
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info(f"[SEED GENERATOR] Seeds saved to {output_file}")
        return output_file
    
    def _select_best_seeds(self, all_seeds: List[TestSeed]) -> List[TestSeed]:
        """Select the best seeds from all generated seeds based on quality criteria."""
        if not all_seeds:
            return []
        
        # Calculate quality scores for each seed
        scored_seeds = []
        for seed in all_seeds:
            quality_score = self._calculate_seed_quality_score(seed, all_seeds)
            scored_seeds.append((seed, quality_score))
        
        # Sort by quality score (highest first)
        scored_seeds.sort(key=lambda x: x[1], reverse=True)
        
        # Select best seeds per category
        selected_seeds = []
        seeds_per_category = {}
        
        for seed, score in scored_seeds:
            category = seed.category
            if category not in seeds_per_category:
                seeds_per_category[category] = 0
            
            if seeds_per_category[category] < self.final_seeds_per_category:
                selected_seeds.append(seed)
                seeds_per_category[category] += 1
        
        logger.info(f"[SEED SELECTOR] Selected seeds per category:")
        for category, count in seeds_per_category.items():
            logger.info(f"  {category}: {count} seeds")
        
        return selected_seeds
    
    def _calculate_seed_quality_score(self, seed: TestSeed, all_seeds: List[TestSeed]) -> float:
        """Calculate a comprehensive quality score for a seed."""
        # Get individual metrics
        complexity_score = self._assess_seed_complexity_score(seed)
        relevance_score = self._assess_seed_relevance(seed)
        uniqueness_score = self._assess_seed_uniqueness(seed, all_seeds)
        
        # Weighted combination based on selection criteria
        weights = {
            "quality_score": 0.4,
            "uniqueness": 0.3,
            "relevance": 0.3
        }
        
        # Map criteria to actual scores
        criteria_scores = {
            "quality_score": complexity_score,
            "uniqueness": uniqueness_score,
            "relevance": relevance_score
        }
        
        # Calculate weighted score
        total_score = 0.0
        total_weight = 0.0
        
        for criterion in self.selection_criteria:
            if criterion in criteria_scores:
                weight = weights.get(criterion, 0.3)
                total_score += criteria_scores[criterion] * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _assess_seed_complexity_score(self, seed: TestSeed) -> float:
        """Convert complexity assessment to a numerical score."""
        complexity = self._assess_seed_complexity(seed)
        complexity_scores = {
            "simple": 0.3,
            "moderate": 0.6,
            "complex": 0.9
        }
        return complexity_scores.get(complexity, 0.5)
    
    async def _generate_individual_seeds_async(self, role, analysis, category: str) -> List[TestSeed]:
        """Generate individual seeds asynchronously."""
        seeds = []
        
        async def generate_single_seed(seed_number: int) -> Optional[TestSeed]:
            """Generate a single seed asynchronously."""
            try:
                return await role.generate_single_seed(
                    analysis, seed_number, category, self.max_llm_retries
                )
            except Exception as e:
                logger.error(f"[SEED GENERATOR] Error generating seed {seed_number}: {e}")
                return None
        
        # Create tasks for all seeds
        tasks = []
        for i in range(self.calls_per_category):
            task = generate_single_seed(i)
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect valid results
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"[SEED GENERATOR] Seed generation failed: {result}")
                continue
            if result is not None:
                seeds.append(result)
        
        logger.info(f"[SEED GENERATOR] Generated {len(seeds)} valid seeds for {category}")
        return seeds
    
    async def _generate_sequential_seeds_async(self, role, analysis, category: str) -> List[TestSeed]:
        """Generate seeds sequentially asynchronously."""
        seeds = []
        
        for i in range(self.calls_per_category):
            try:
                seed = await role.generate_single_seed(analysis, i, category, self.max_llm_retries)
                if seed:
                    seeds.append(seed)
            except Exception as e:
                logger.error(f"[SEED GENERATOR] Error generating seed {i}: {e}")
        
        logger.info(f"[SEED GENERATOR] Generated {len(seeds)} seeds for {category}")
        return seeds 

    async def _generate_all_categories_parallel(self, analysis) -> List[TestSeed]:
        """Generate seeds for all categories in parallel using ThreadPoolExecutor."""
        logger.info(f"[SEED GENERATOR] Starting parallel generation for all categories...")
        
        # Get all generators
        edge_generator = self.roles["EdgeCaseGenerator"]
        complexity_generator = self.roles["ComplexityGenerator"]
        context_generator = self.roles["ContextGenerator"]
        
        # Define categories and their generators
        category_generators = [
            ("edge_cases", edge_generator),
            ("complexity", complexity_generator),
            ("context", context_generator)
        ]
        
        async def generate_category_seeds(generator_name: str, category: str) -> List[TestSeed]:
            """Generate seeds for a specific category."""
            logger.info(f"[SEED GENERATOR] Generating {self.calls_per_category} seeds for {category} using {generator_name}")
            
            if self.enable_individual_calls:
                return await self._generate_individual_seeds_async(self.roles[generator_name], analysis, category)
            else:
                return await self._generate_sequential_seeds_async(self.roles[generator_name], analysis, category)
        
        # Create tasks for all categories
        tasks = []
        for category, generator in category_generators:
            generator_name = generator.__class__.__name__
            task = generate_category_seeds(generator_name, category)
            tasks.append(task)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine all results
        all_seeds = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"[SEED GENERATOR] Error in category {category_generators[i][0]}: {result}")
                continue
            all_seeds.extend(result)
        
        logger.info(f"[SEED GENERATOR] Parallel generation completed: {len(all_seeds)} total seeds")
        return all_seeds 