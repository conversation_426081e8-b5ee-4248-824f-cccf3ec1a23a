"""
Integration module showing how requirements documents enhance prompt generation.

This module demonstrates how to use the requirements document as structured input
to improve the prompt generation process.
"""

from typing import Dict, Any, List
from dataclasses import dataclass
import json

from .models import RequirementsDocument, RequirementsDoc, WorkflowExpectations, QualityMetrics


@dataclass
class EnhancedPromptInput:
    """Enhanced input for prompt generation using requirements document."""
    
    # Original prompt
    original_prompt: str
    
    # Requirements context
    problem_statement: str
    core_objectives: List[str]
    key_requirements: List[str]
    
    # Domain and complexity context
    domain: str
    complexity_level: str
    priority_level: str
    
    # Workflow context
    input_format: str
    output_format: str
    processing_steps: List[str]
    
    # Quality context
    accuracy_threshold: float
    completeness_score: float
    validation_criteria: List[str]
    
    # Additional context
    stakeholders: List[str]
    success_criteria: List[str]
    constraints: List[str]


class RequirementsToPromptConverter:
    """Converts requirements documents into enhanced prompt generation inputs."""
    
    def convert_to_prompt_input(self, requirements_doc: RequirementsDocument) -> EnhancedPromptInput:
        """
        Convert requirements document to enhanced prompt generation input.
        
        Args:
            requirements_doc: The requirements document
            
        Returns:
            EnhancedPromptInput: Structured input for prompt generation
        """
        req_doc = requirements_doc.requirements_doc
        workflow = requirements_doc.workflow_expectations
        quality = requirements_doc.quality_metrics
        
        return EnhancedPromptInput(
            original_prompt=requirements_doc.original_prompt,
            problem_statement=req_doc.problem_statement,
            core_objectives=req_doc.core_objectives,
            key_requirements=req_doc.key_requirements,
            domain=req_doc.domain or "general",
            complexity_level=req_doc.complexity_level.value,
            priority_level=req_doc.priority_level.value,
            input_format=workflow.input_format,
            output_format=workflow.output_format,
            processing_steps=workflow.processing_steps,
            accuracy_threshold=quality.accuracy_threshold,
            completeness_score=quality.completeness_score,
            validation_criteria=quality.validation_criteria,
            stakeholders=req_doc.stakeholders,
            success_criteria=req_doc.success_criteria,
            constraints=req_doc.constraints
        )
    
    def create_enhanced_prompt_context(self, enhanced_input: EnhancedPromptInput) -> str:
        """
        Create enhanced prompt context for the prompt generator.
        
        Args:
            enhanced_input: The enhanced input from requirements
            
        Returns:
            str: Enhanced context for prompt generation
        """
        
        context = f"""
        ENHANCED PROMPT GENERATION CONTEXT
        =================================
        
        ORIGINAL REQUEST: {enhanced_input.original_prompt}
        
        PROBLEM ANALYSIS:
        - Problem Statement: {enhanced_input.problem_statement}
        - Core Objectives: {', '.join(enhanced_input.core_objectives)}
        - Key Requirements: {', '.join(enhanced_input.key_requirements)}
        
        DOMAIN CONTEXT:
        - Domain: {enhanced_input.domain}
        - Complexity Level: {enhanced_input.complexity_level}
        - Priority Level: {enhanced_input.priority_level}
        
        WORKFLOW SPECIFICATIONS:
        - Input Format: {enhanced_input.input_format}
        - Output Format: {enhanced_input.output_format}
        - Processing Steps: {', '.join(enhanced_input.processing_steps)}
        
        QUALITY STANDARDS:
        - Accuracy Threshold: {enhanced_input.accuracy_threshold}
        - Completeness Score: {enhanced_input.completeness_score}
        - Validation Criteria: {', '.join(enhanced_input.validation_criteria)}
        
        STAKEHOLDERS & SUCCESS:
        - Stakeholders: {', '.join(enhanced_input.stakeholders)}
        - Success Criteria: {', '.join(enhanced_input.success_criteria)}
        - Constraints: {', '.join(enhanced_input.constraints)}
        
        PROMPT GENERATION REQUIREMENTS:
        ===============================
        
        The generated prompt must:
        1. Address the core problem: {enhanced_input.problem_statement}
        2. Meet all objectives: {', '.join(enhanced_input.core_objectives)}
        3. Cover all requirements: {', '.join(enhanced_input.key_requirements)}
        4. Match the domain context: {enhanced_input.domain}
        5. Handle the specified complexity: {enhanced_input.complexity_level}
        6. Accept input in format: {enhanced_input.input_format}
        7. Produce output in format: {enhanced_input.output_format}
        8. Meet quality standards: {enhanced_input.accuracy_threshold} accuracy
        9. Consider stakeholders: {', '.join(enhanced_input.stakeholders)}
        10. Achieve success criteria: {', '.join(enhanced_input.success_criteria)}
        
        CONSTRAINTS TO RESPECT:
        {chr(10).join(f"- {constraint}" for constraint in enhanced_input.constraints)}
        """
        
        return context
    
    def create_json_context(self, enhanced_input: EnhancedPromptInput) -> Dict[str, Any]:
        """
        Create JSON context for programmatic prompt generation.
        
        Args:
            enhanced_input: The enhanced input from requirements
            
        Returns:
            Dict: JSON context for prompt generation
        """
        
        return {
            "original_prompt": enhanced_input.original_prompt,
            "requirements_context": {
                "problem_statement": enhanced_input.problem_statement,
                "core_objectives": enhanced_input.core_objectives,
                "key_requirements": enhanced_input.key_requirements,
                "stakeholders": enhanced_input.stakeholders,
                "success_criteria": enhanced_input.success_criteria,
                "constraints": enhanced_input.constraints
            },
            "domain_context": {
                "domain": enhanced_input.domain,
                "complexity_level": enhanced_input.complexity_level,
                "priority_level": enhanced_input.priority_level
            },
            "workflow_context": {
                "input_format": enhanced_input.input_format,
                "output_format": enhanced_input.output_format,
                "processing_steps": enhanced_input.processing_steps
            },
            "quality_context": {
                "accuracy_threshold": enhanced_input.accuracy_threshold,
                "completeness_score": enhanced_input.completeness_score,
                "validation_criteria": enhanced_input.validation_criteria
            },
            "prompt_generation_requirements": {
                "must_address_problem": enhanced_input.problem_statement,
                "must_meet_objectives": enhanced_input.core_objectives,
                "must_cover_requirements": enhanced_input.key_requirements,
                "must_match_domain": enhanced_input.domain,
                "must_handle_complexity": enhanced_input.complexity_level,
                "must_accept_input_format": enhanced_input.input_format,
                "must_produce_output_format": enhanced_input.output_format,
                "must_meet_quality_standards": {
                    "accuracy_threshold": enhanced_input.accuracy_threshold,
                    "completeness_score": enhanced_input.completeness_score
                },
                "must_consider_stakeholders": enhanced_input.stakeholders,
                "must_achieve_success_criteria": enhanced_input.success_criteria,
                "must_respect_constraints": enhanced_input.constraints
            }
        }


class PromptGeneratorEnhancer:
    """Enhances prompt generation using requirements document context."""
    
    def __init__(self, requirements_converter: RequirementsToPromptConverter):
        self.converter = requirements_converter
    
    def enhance_prompt_generation(self, requirements_doc: RequirementsDocument) -> Dict[str, Any]:
        """
        Enhance prompt generation with requirements document context.
        
        Args:
            requirements_doc: The requirements document
            
        Returns:
            Dict: Enhanced context for prompt generation
        """
        
        # Convert requirements to enhanced input
        enhanced_input = self.converter.convert_to_prompt_input(requirements_doc)
        
        # Create different formats for different use cases
        text_context = self.converter.create_enhanced_prompt_context(enhanced_input)
        json_context = self.converter.create_json_context(enhanced_input)
        
        return {
            "text_context": text_context,
            "json_context": json_context,
            "enhanced_input": enhanced_input,
            "requirements_doc": requirements_doc
        }
    
    def create_prompt_generator_input(self, requirements_doc: RequirementsDocument) -> str:
        """
        Create input string for the prompt generator.
        
        Args:
            requirements_doc: The requirements document
            
        Returns:
            str: Enhanced input for prompt generator
        """
        
        enhanced_input = self.converter.convert_to_prompt_input(requirements_doc)
        return self.converter.create_enhanced_prompt_context(enhanced_input)


# Example usage functions
def example_enhanced_prompt_generation():
    """Example of how to use requirements document to enhance prompt generation."""
    
    # This would be your actual requirements document
    from .models import RequirementsDocument, RequirementsDoc, WorkflowExpectations, QualityMetrics, ComplexityLevel, PriorityLevel
    
    # Create a sample requirements document
    requirements_doc = RequirementsDocument(
        requirements_doc=RequirementsDoc(
            problem_statement="Need to create professional email templates for job applications that are adaptable and maintain high quality standards",
            core_objectives=["Create professional templates", "Ensure adaptability across industries", "Maintain consistent quality standards"],
            key_requirements=["Professional tone and formatting", "Clear structure with sections", "Contact information integration", "Customizable content areas", "Industry-specific adaptations"],
            domain="professional",
            complexity_level=ComplexityLevel.MODERATE,
            priority_level=PriorityLevel.HIGH,
            stakeholders=["Job seekers", "HR professionals", "Career counselors"],
            success_criteria=["Templates are professional and effective", "Templates are easily customizable", "Templates work across different industries"],
            constraints=["Must be under 500 words", "Must include placeholders for customization", "Must follow professional email standards"]
        ),
        workflow_expectations=WorkflowExpectations(
            input_format="Job description, candidate details, and company information",
            output_format="Professional email template with placeholders",
            processing_steps=["Analyze job requirements", "Create template structure", "Add customization placeholders", "Include professional formatting"]
        ),
        quality_metrics=QualityMetrics(
            accuracy_threshold=0.95,
            completeness_score=0.9,
            validation_criteria=["Professional tone maintained", "All sections included", "Placeholders properly formatted"]
        ),
        original_prompt="Create a professional email template for job applications"
    )
    
    # Create converter and enhancer
    converter = RequirementsToPromptConverter()
    enhancer = PromptGeneratorEnhancer(converter)
    
    # Get enhanced context
    enhanced_context = enhancer.enhance_prompt_generation(requirements_doc)
    
    print("Enhanced Prompt Generation Context:")
    print("="*50)
    print(enhanced_context["text_context"])
    
    print("\nJSON Context for Programmatic Use:")
    print("="*50)
    print(json.dumps(enhanced_context["json_context"], indent=2))
    
    return enhanced_context


if __name__ == "__main__":
    example_enhanced_prompt_generation() 