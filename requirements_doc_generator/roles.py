"""
Specialized agent roles for requirements document generation.

Each role has a specific focus in analyzing and generating requirements documents.
"""

import os
import json
import re
import time
from typing import Dict, Any, List
from dataclasses import dataclass
from openai import OpenAI
from .models import RequirementsDoc, WorkflowExpectations, QualityMetrics, ComplexityLevel, PriorityLevel

# Global variable to track last API call time
_last_api_call_time = 0.0
_api_call_delay = 1.0  # Delay in seconds between API calls

def _rate_limit_delay():
    """Add delay between API calls to avoid rate limiting."""
    global _last_api_call_time
    current_time = time.time()
    time_since_last_call = current_time - _last_api_call_time
    
    if time_since_last_call < _api_call_delay:
        delay_needed = _api_call_delay - time_since_last_call
        print(f"[RATE LIMIT] Waiting {delay_needed:.2f}s to avoid rate limiting...")
        time.sleep(delay_needed)
    
    _last_api_call_time = time.time()

@dataclass
class RoleOutput:
    """Output from a role execution."""
    requirements_doc: RequirementsDoc = None
    workflow_expectations: WorkflowExpectations = None
    quality_metrics: QualityMetrics = None
    analysis: Dict[str, Any] = None
    log: str = ""


class BaseRequirementsRole:
    """Base class for all requirements generation roles."""
    
    def __init__(self, role_name: str = None, model: str = None, temperature: float = None, max_tokens: int = None):
        # Load configuration
        from .config import cfg
        self.role_name = role_name or self.__class__.__name__
        self.model = model if model is not None else cfg.get("model", "gpt-4o")
        self.temperature = temperature if temperature is not None else cfg.get("temperature", 1.0)
        self.max_tokens = max_tokens if max_tokens is not None else cfg.get("max_tokens", 4000)
        api_key = os.environ.get(cfg.get("api_key_env", "OPENAI_API_KEY"), os.environ.get("OPENAI_API_KEY"))
        self.client = OpenAI(api_key=api_key) if api_key else None

    def _call_llm(self, messages: List[Dict[str, str]]) -> str:
        """Make LLM call with error handling and rate limiting."""
        if not self.client:
            raise RuntimeError("OpenAI client not initialized. Set OPENAI_API_KEY environment variable.")
        
        # Add rate limiting delay
        _rate_limit_delay()
        
        try:
            # Debug: Check what self.model actually is
            print(f"[DEBUG] self.model type: {type(self.model)}, value: {self.model}")
            
            # Use max_completion_tokens for newer models, max_tokens for older ones
            if isinstance(self.model, str) and (self.model.startswith("o4-") or self.model.startswith("gpt-4o")):
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_completion_tokens=self.max_tokens
                )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
            return response.choices[0].message.content.strip()
        except Exception as e:
            raise RuntimeError(f"LLM call failed: {str(e)}")
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        """Evaluate the current state and return role output."""
        raise NotImplementedError("Subclasses must implement eval method")

    def _extract_json_from_code_block(self, response: str) -> str:
        """Extract JSON from markdown code block if present."""
        print(f"[DEBUG] Response length: {len(response)}")
        print(f"[DEBUG] Response starts with: {repr(response[:50])}")
        
        # Simple approach: find the first { and last }
        if "```json" in response and "{" in response and "}" in response:
            start = response.find("{")
            # Find the matching closing brace
            brace_count = 0
            end = start
            for i in range(start, len(response)):
                if response[i] == "{":
                    brace_count += 1
                elif response[i] == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end = i + 1
                        break
            
            if end > start:
                extracted = response[start:end].strip()
                print(f"[DEBUG] Extracted JSON: {extracted[:100]}...")
                return extracted
        
        # If not found, return original
        print(f"[DEBUG] No JSON found, returning original")
        return response.strip()


class ProblemAnalyzer(BaseRequirementsRole):
    """Analyzes the initial prompt to understand the core problem and objectives."""
    
    system_prompt = """
    You are a Requirements Analyst specializing in problem decomposition and objective identification.
    
    Your task is to analyze an initial prompt and extract:
    1. The core problem being solved
    2. Primary and secondary objectives
    3. Key stakeholders and their needs
    4. Success criteria
    5. Domain and industry context
    6. Solution approach
    
    Output your analysis as a JSON object with the following structure:
    {
        "problem_statement": "Clear description of the problem",
        "core_objectives": ["List of main objectives"],
        "solution_approach": "High-level solution approach",
        "key_requirements": ["Essential requirements"],
        "stakeholders": ["List of stakeholders"],
        "success_criteria": ["How success is measured"],
        "domain": "Domain context (e.g., financial, technical, medical)",
        "industry": "Industry context if applicable",
        "complexity_level": "simple|moderate|complex|enterprise",
        "priority_level": "low|medium|high|critical"
    }
    
    Be thorough but concise. Focus on understanding the real problem behind the prompt.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="ProblemAnalyzer", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        original_prompt = state.get("original_prompt", "")
        
        user_prompt = f"""
        Analyze this initial prompt and extract the core problem and requirements:
        
        PROMPT: {original_prompt}
        
        Provide a comprehensive analysis of the problem, objectives, and requirements.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        
        # Debug: Print the actual response
        print(f"[DEBUG] {self.__class__.__name__} LLM Response: {response[:500]}...")
        
        # Extract JSON from code block if present
        response_json = self._extract_json_from_code_block(response)
        
        # Parse JSON response
        try:
            analysis = json.loads(response_json)
            print(f"[DEBUG] {self.__class__.__name__} JSON parsing successful")
        except json.JSONDecodeError as e:
            print(f"[DEBUG] {self.__class__.__name__} JSON parsing failed: {e}")
            # Fallback parsing
            analysis = self._parse_fallback(response_json)
        
        # Create RequirementsDoc
        requirements_doc = RequirementsDoc(
            problem_statement=analysis.get("problem_statement", ""),
            core_objectives=analysis.get("core_objectives", []),
            solution_approach=analysis.get("solution_approach", "Standard approach"),
            key_requirements=analysis.get("key_requirements", []),
            stakeholders=analysis.get("stakeholders", []),
            success_criteria=analysis.get("success_criteria", []),
            domain=analysis.get("domain"),
            industry=analysis.get("industry"),
            complexity_level=ComplexityLevel(analysis.get("complexity_level", "moderate")),
            priority_level=PriorityLevel(analysis.get("priority_level", "medium"))
        )
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            analysis=analysis,
            log="ProblemAnalyzer completed analysis of initial prompt."
        )
    
    def _parse_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing if JSON parsing fails."""
        # Extract key information using regex patterns
        patterns = {
            "problem_statement": r"problem[:\s]+(.+?)(?=\n|$)",
            "core_objectives": r"objectives?[:\s]+(.+?)(?=\n|$)",
            "key_requirements": r"requirements?[:\s]+(.+?)(?=\n|$)",
        }
        
        result = {}
        for key, pattern in patterns.items():
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                result[key] = match.group(1).strip()
        
        return result


class SolutionArchitect(BaseRequirementsRole):
    """Designs the solution approach and workflow expectations."""
    
    system_prompt = """
    You are a Solution Architect specializing in designing comprehensive solution approaches.
    
    Your task is to design:
    1. Solution approach and methodology
    2. Input/output specifications
    3. Processing workflow and steps
    4. Error handling strategies
    5. Performance expectations
    6. Integration requirements
    
    Output your design as a JSON object with the following structure:
    {
        "solution_approach": "Detailed solution methodology",
        "input_format": "Expected input format and structure",
        "output_format": "Expected output format and structure",
        "processing_steps": ["Step-by-step workflow"],
        "error_handling": {"error_type": "handling_strategy"},
        "performance_expectations": {"metric": "value"},
        "integration_points": ["Integration requirements"],
        "input_validation_rules": ["Validation rules"],
        "output_validation_rules": ["Output validation rules"]
    }
    
    Focus on practical, implementable solutions.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="SolutionArchitect", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="SolutionArchitect skipped - no requirements doc available.")
        
        user_prompt = f"""
        Design a solution approach for this requirements document:
        
        PROBLEM: {requirements_doc.problem_statement}
        OBJECTIVES: {requirements_doc.core_objectives}
        REQUIREMENTS: {requirements_doc.key_requirements}
        DOMAIN: {requirements_doc.domain}
        COMPLEXITY: {requirements_doc.complexity_level.value}
        
        Design a comprehensive solution approach and workflow.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        
        # Debug: Print the actual response
        print(f"[DEBUG] {self.__class__.__name__} LLM Response: {response[:500]}...")
        
        # Extract JSON from code block if present
        response_json = self._extract_json_from_code_block(response)
        
        try:
            design = json.loads(response_json)
            print(f"[DEBUG] {self.__class__.__name__} JSON parsing successful")
        except json.JSONDecodeError as e:
            print(f"[DEBUG] {self.__class__.__name__} JSON parsing failed: {e}")
            design = self._parse_design_fallback(response_json)
        
        # Update requirements doc with solution approach
        requirements_doc.solution_approach = design.get("solution_approach", "")
        
        # Create WorkflowExpectations
        workflow_expectations = WorkflowExpectations(
            input_format=design.get("input_format", ""),
            output_format=design.get("output_format", ""),
            processing_steps=design.get("processing_steps", []),
            error_handling=design.get("error_handling", {}),
            performance_expectations=design.get("performance_expectations", {}),
            integration_points=design.get("integration_points", []),
            input_validation_rules=design.get("input_validation_rules", []),
            output_validation_rules=design.get("output_validation_rules", [])
        )
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            workflow_expectations=workflow_expectations,
            analysis=design,
            log="SolutionArchitect completed solution design."
        )
    
    def _parse_design_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for solution design."""
        return {
            "solution_approach": "Standard solution approach",
            "input_format": "Text input",
            "output_format": "Structured output",
            "processing_steps": ["Process input", "Generate output"],
            "error_handling": {"general": "Graceful degradation"},
            "performance_expectations": {"response_time": "2 seconds"},
            "integration_points": ["API integration"],
            "input_validation_rules": ["Validate input format"],
            "output_validation_rules": ["Validate output quality"]
        }


class QualityEngineer(BaseRequirementsRole):
    """Defines quality metrics and validation criteria."""
    
    system_prompt = """
    You are a Quality Engineer specializing in defining comprehensive quality metrics and validation criteria.
    
    Your task is to define:
    1. Accuracy and precision thresholds
    2. Completeness and relevance scores
    3. Performance metrics
    4. Validation criteria
    5. Acceptance criteria
    6. Risk factors and monitoring
    
    Output your quality specification as a JSON object with the following structure:
    {
        "accuracy_threshold": 0.9,
        "precision_threshold": 0.85,
        "recall_threshold": 0.85,
        "completeness_score": 0.9,
        "relevance_score": 0.85,
        "consistency_score": 0.9,
        "response_time_threshold": 2.0,
        "validation_criteria": ["List of validation criteria"],
        "acceptance_criteria": ["List of acceptance criteria"],
        "test_scenarios": ["Test scenarios"],
        "risk_factors": ["Potential risks"],
        "monitoring_metrics": ["Metrics to monitor"]
    }
    
    Set realistic but high-quality standards.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="QualityEngineer", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        workflow_expectations = state.get("workflow_expectations")
        
        if not requirements_doc:
            return RoleOutput(log="QualityEngineer skipped - no requirements doc available.")
        
        user_prompt = f"""
        Define quality metrics for this requirements document:
        
        PROBLEM: {requirements_doc.problem_statement}
        COMPLEXITY: {requirements_doc.complexity_level.value}
        PRIORITY: {requirements_doc.priority_level.value}
        DOMAIN: {requirements_doc.domain}
        
        INPUT FORMAT: {workflow_expectations.input_format if workflow_expectations else "Not specified"}
        OUTPUT FORMAT: {workflow_expectations.output_format if workflow_expectations else "Not specified"}
        
        Define comprehensive quality metrics and validation criteria.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        
        # Debug: Print the actual response
        print(f"[DEBUG] {self.__class__.__name__} LLM Response: {response[:500]}...")
        
        # Extract JSON from code block if present
        response_json = self._extract_json_from_code_block(response)
        
        try:
            quality_spec = json.loads(response_json)
            print(f"[DEBUG] {self.__class__.__name__} JSON parsing successful")
        except json.JSONDecodeError as e:
            print(f"[DEBUG] {self.__class__.__name__} JSON parsing failed: {e}")
            quality_spec = self._parse_quality_fallback(response_json)
        
        # Create QualityMetrics
        quality_metrics = QualityMetrics(
            accuracy_threshold=quality_spec.get("accuracy_threshold", 0.9),
            precision_threshold=quality_spec.get("precision_threshold", 0.85),
            recall_threshold=quality_spec.get("recall_threshold", 0.85),
            completeness_score=quality_spec.get("completeness_score", 0.9),
            relevance_score=quality_spec.get("relevance_score", 0.85),
            consistency_score=quality_spec.get("consistency_score", 0.9),
            response_time_threshold=quality_spec.get("response_time_threshold", 2.0),
            validation_criteria=quality_spec.get("validation_criteria", []),
            acceptance_criteria=quality_spec.get("acceptance_criteria", []),
            test_scenarios=quality_spec.get("test_scenarios", []),
            risk_factors=quality_spec.get("risk_factors", []),
            monitoring_metrics=quality_spec.get("monitoring_metrics", [])
        )
        
        return RoleOutput(
            quality_metrics=quality_metrics,
            analysis=quality_spec,
            log="QualityEngineer completed quality metrics definition."
        )
    
    def _parse_quality_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for quality metrics."""
        return {
            "accuracy_threshold": 0.9,
            "precision_threshold": 0.85,
            "recall_threshold": 0.85,
            "completeness_score": 0.9,
            "relevance_score": 0.85,
            "consistency_score": 0.9,
            "response_time_threshold": 2.0,
            "validation_criteria": ["Output meets requirements", "Response time acceptable"],
            "acceptance_criteria": ["All requirements satisfied", "Quality thresholds met"],
            "test_scenarios": ["Standard use case", "Edge case handling"],
            "risk_factors": ["Data quality issues", "Performance degradation"],
            "monitoring_metrics": ["Response time", "Accuracy rate"]
        }


class RequirementsValidator(BaseRequirementsRole):
    """Validates the completeness and coherence of requirements."""
    
    system_prompt = """
    You are a Requirements Validation Specialist.
    
    Your task is to validate the completeness, coherence, and quality of requirements documents.
    Analyze the requirements for:
    1. Completeness - Are all necessary requirements captured?
    2. Consistency - Do requirements conflict with each other?
    3. Clarity - Are requirements clear and unambiguous?
    4. Feasibility - Are requirements realistic and achievable?
    5. Traceability - Can requirements be traced to objectives?
    
    Output your validation as a JSON object with the following structure:
    {
        "validation_status": {
            "completeness": true/false,
            "consistency": true/false,
            "clarity": true/false,
            "feasibility": true/false,
            "traceability": true/false
        },
        "validation_issues": ["List of specific issues found"],
        "recommendations": ["List of improvement recommendations"],
        "overall_score": 0.0-1.0,
        "critical_issues": ["List of critical issues that must be addressed"]
    }
    
    Be thorough and constructive in your analysis.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="RequirementsValidator", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="No requirements document to validate")
        
        user_prompt = f"""
        Validate this requirements document:
        
        PROBLEM STATEMENT: {requirements_doc.problem_statement}
        CORE OBJECTIVES: {requirements_doc.core_objectives}
        KEY REQUIREMENTS: {requirements_doc.key_requirements}
        FUNCTIONAL REQUIREMENTS: {requirements_doc.functional_requirements}
        NON-FUNCTIONAL REQUIREMENTS: {requirements_doc.non_functional_requirements}
        CONSTRAINTS: {requirements_doc.constraints}
        STAKEHOLDERS: {requirements_doc.stakeholders}
        SUCCESS CRITERIA: {requirements_doc.success_criteria}
        
        Provide a comprehensive validation analysis.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        response_json = self._extract_json_from_code_block(response)
        
        try:
            validation_analysis = json.loads(response_json)
        except json.JSONDecodeError:
            validation_analysis = self._parse_validation_fallback(response_json)
        
        return RoleOutput(
            analysis=validation_analysis,
            log="Requirements validation completed"
        )
    
    def _parse_validation_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for validation response."""
        return {
            "validation_status": {
                "completeness": True,
                "consistency": True,
                "clarity": True,
                "feasibility": True,
                "traceability": True
            },
            "validation_issues": [],
            "recommendations": ["Review requirements for completeness"],
            "overall_score": 0.8,
            "critical_issues": []
        }


class SecurityArchitect(BaseRequirementsRole):
    """Designs comprehensive security and compliance requirements."""
    
    system_prompt = """
    You are a Security Architect specializing in comprehensive security and compliance requirements.
    
    Your task is to analyze the requirements and design comprehensive security specifications including:
    1. Authentication and Authorization methods
    2. Data encryption and protection requirements
    3. Compliance standards (GDPR, HIPAA, SOX, PCI-DSS, etc.)
    4. Audit and logging requirements
    5. Privacy protection measures
    6. Security testing and validation
    7. Risk assessment and mitigation
    
    Output your security requirements as a JSON object with the following structure:
    {
        "authentication_methods": ["List of required authentication methods"],
        "authorization_levels": ["List of authorization levels and roles"],
        "data_encryption": ["Encryption requirements for data at rest and in transit"],
        "compliance_standards": ["List of applicable compliance standards"],
        "audit_requirements": ["Audit and logging requirements"],
        "privacy_requirements": ["Privacy protection requirements"],
        "security_testing": ["Security testing and validation requirements"],
        "risk_assessment": {
            "data_security": "low/medium/high/critical",
            "access_control": "low/medium/high/critical",
            "compliance": "low/medium/high/critical",
            "business_continuity": "low/medium/high/critical"
        }
    }
    
    Consider industry-specific requirements and regulatory frameworks.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="SecurityArchitect", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="No requirements document to analyze for security")
        
        user_prompt = f"""
        Design comprehensive security requirements for this system:
        
        PROBLEM STATEMENT: {requirements_doc.problem_statement}
        DOMAIN: {requirements_doc.domain}
        INDUSTRY: {requirements_doc.industry}
        COMPLEXITY LEVEL: {requirements_doc.complexity_level.value}
        STAKEHOLDERS: {requirements_doc.stakeholders}
        
        Consider the domain, industry, and complexity level when designing security requirements.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        response_json = self._extract_json_from_code_block(response)
        
        try:
            security_analysis = json.loads(response_json)
        except json.JSONDecodeError:
            security_analysis = self._parse_security_fallback(response_json)
        
        # Update the requirements document with security requirements
        from .models import SecurityRequirements, RiskLevel
        
        security_req = SecurityRequirements(
            authentication_methods=security_analysis.get("authentication_methods", []),
            authorization_levels=security_analysis.get("authorization_levels", []),
            data_encryption=security_analysis.get("data_encryption", []),
            compliance_standards=security_analysis.get("compliance_standards", []),
            audit_requirements=security_analysis.get("audit_requirements", []),
            privacy_requirements=security_analysis.get("privacy_requirements", []),
            security_testing=security_analysis.get("security_testing", [])
        )
        
        requirements_doc.security_requirements = security_req
        
        # Add risk assessment
        risk_assessment = {}
        for risk_type, level in security_analysis.get("risk_assessment", {}).items():
            try:
                risk_assessment[risk_type] = RiskLevel(level.lower())
            except ValueError:
                risk_assessment[risk_type] = RiskLevel.MEDIUM
        
        requirements_doc.risk_assessment.update(risk_assessment)
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            analysis=security_analysis,
            log="Security requirements designed and integrated"
        )
    
    def _parse_security_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for security analysis response."""
        return {
            "authentication_methods": ["Multi-factor authentication", "Single sign-on"],
            "authorization_levels": ["Admin", "User", "Guest"],
            "data_encryption": ["AES-256 for data at rest", "TLS 1.3 for data in transit"],
            "compliance_standards": ["GDPR", "ISO 27001"],
            "audit_requirements": ["Comprehensive logging", "Regular security audits"],
            "privacy_requirements": ["Data minimization", "User consent management"],
            "security_testing": ["Penetration testing", "Vulnerability assessments"],
            "risk_assessment": {
                "data_security": "medium",
                "access_control": "medium",
                "compliance": "medium",
                "business_continuity": "medium"
            }
        }


class TechnicalArchitect(BaseRequirementsRole):
    """Designs comprehensive technical specifications and architecture."""
    
    system_prompt = """
    You are a Technical Architect specializing in comprehensive technical specifications and system architecture.
    
    Your task is to design detailed technical specifications including:
    1. Architecture patterns and design principles
    2. Technology stack recommendations
    3. Data models and database design
    4. API specifications and integration patterns
    5. Deployment and scalability strategies
    6. Performance targets and optimization
    7. Infrastructure requirements
    
    Output your technical specifications as a JSON object with the following structure:
    {
        "architecture_patterns": ["List of architectural patterns to use"],
        "technology_stack": ["Recommended technology stack"],
        "data_models": ["Data modeling requirements and specifications"],
        "api_specifications": ["API design and specification requirements"],
        "integration_patterns": ["Integration patterns and approaches"],
        "deployment_strategy": "Detailed deployment strategy",
        "scalability_approach": "Scalability and performance approach",
        "performance_targets": {
            "response_time": "Target response time",
            "throughput": "Target throughput",
            "availability": "Target availability percentage",
            "concurrent_users": "Target concurrent users"
        }
    }
    
    Consider scalability, maintainability, and performance requirements.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="TechnicalArchitect", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="No requirements document to analyze for technical specifications")
        
        user_prompt = f"""
        Design comprehensive technical specifications for this system:
        
        PROBLEM STATEMENT: {requirements_doc.problem_statement}
        SOLUTION APPROACH: {requirements_doc.solution_approach}
        COMPLEXITY LEVEL: {requirements_doc.complexity_level.value}
        FUNCTIONAL REQUIREMENTS: {requirements_doc.functional_requirements}
        NON-FUNCTIONAL REQUIREMENTS: {requirements_doc.non_functional_requirements}
        CONSTRAINTS: {requirements_doc.constraints}
        
        Consider the complexity level and requirements when designing technical specifications.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        response_json = self._extract_json_from_code_block(response)
        
        try:
            technical_analysis = json.loads(response_json)
        except json.JSONDecodeError:
            technical_analysis = self._parse_technical_fallback(response_json)
        
        # Update the requirements document with technical specifications
        from .models import TechnicalSpecifications
        
        tech_spec = TechnicalSpecifications(
            architecture_patterns=technical_analysis.get("architecture_patterns", []),
            technology_stack=technical_analysis.get("technology_stack", []),
            data_models=technical_analysis.get("data_models", []),
            api_specifications=technical_analysis.get("api_specifications", []),
            integration_patterns=technical_analysis.get("integration_patterns", []),
            deployment_strategy=technical_analysis.get("deployment_strategy", ""),
            scalability_approach=technical_analysis.get("scalability_approach", ""),
            performance_targets=technical_analysis.get("performance_targets", {})
        )
        
        requirements_doc.technical_specifications = tech_spec
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            analysis=technical_analysis,
            log="Technical specifications designed and integrated"
        )
    
    def _parse_technical_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for technical analysis response."""
        return {
            "architecture_patterns": ["Microservices", "Event-driven architecture", "Layered architecture"],
            "technology_stack": ["Python", "FastAPI", "PostgreSQL", "Redis", "Docker"],
            "data_models": ["Relational database design", "NoSQL for specific use cases"],
            "api_specifications": ["RESTful API design", "GraphQL for complex queries"],
            "integration_patterns": ["API Gateway", "Message queues", "Event streaming"],
            "deployment_strategy": "Containerized deployment with CI/CD pipeline",
            "scalability_approach": "Horizontal scaling with load balancing",
            "performance_targets": {
                "response_time": "Under 2 seconds",
                "throughput": "1000 requests per second",
                "availability": "99.9%",
                "concurrent_users": "10,000"
            }
        }


class BusinessAnalyst(BaseRequirementsRole):
    """Designs comprehensive business and operational requirements."""
    
    system_prompt = """
    You are a Business Analyst specializing in comprehensive business and operational requirements.
    
    Your task is to design detailed business requirements including:
    1. Business processes and workflows
    2. Operational procedures and policies
    3. Reporting and analytics requirements
    4. Compliance and regulatory requirements
    5. Risk mitigation strategies
    6. Business continuity planning
    7. Change management processes
    
    Output your business requirements as a JSON object with the following structure:
    {
        "business_processes": ["List of business processes to be supported"],
        "operational_procedures": ["Operational procedures and policies"],
        "reporting_requirements": ["Reporting and analytics requirements"],
        "compliance_requirements": ["Compliance and regulatory requirements"],
        "risk_mitigation": ["Risk mitigation strategies and approaches"],
        "business_continuity": ["Business continuity and disaster recovery"],
        "change_management": ["Change management and transition processes"]
    }
    
    Consider business value, operational efficiency, and stakeholder needs.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="BusinessAnalyst", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="No requirements document to analyze for business requirements")
        
        user_prompt = f"""
        Design comprehensive business requirements for this system:
        
        PROBLEM STATEMENT: {requirements_doc.problem_statement}
        CORE OBJECTIVES: {requirements_doc.core_objectives}
        STAKEHOLDERS: {requirements_doc.stakeholders}
        SUCCESS CRITERIA: {requirements_doc.success_criteria}
        DOMAIN: {requirements_doc.domain}
        INDUSTRY: {requirements_doc.industry}
        
        Consider the business context, stakeholders, and success criteria when designing business requirements.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        response_json = self._extract_json_from_code_block(response)
        
        try:
            business_analysis = json.loads(response_json)
        except json.JSONDecodeError:
            business_analysis = self._parse_business_fallback(response_json)
        
        # Update the requirements document with business requirements
        from .models import BusinessRequirements
        
        business_req = BusinessRequirements(
            business_processes=business_analysis.get("business_processes", []),
            operational_procedures=business_analysis.get("operational_procedures", []),
            reporting_requirements=business_analysis.get("reporting_requirements", []),
            compliance_requirements=business_analysis.get("compliance_requirements", []),
            risk_mitigation=business_analysis.get("risk_mitigation", []),
            business_continuity=business_analysis.get("business_continuity", []),
            change_management=business_analysis.get("change_management", [])
        )
        
        requirements_doc.business_requirements = business_req
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            analysis=business_analysis,
            log="Business requirements designed and integrated"
        )
    
    def _parse_business_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for business analysis response."""
        return {
            "business_processes": ["User registration", "Data processing", "Reporting generation"],
            "operational_procedures": ["Standard operating procedures", "Quality assurance processes"],
            "reporting_requirements": ["Regular performance reports", "Analytics dashboards"],
            "compliance_requirements": ["Industry-specific compliance", "Data protection regulations"],
            "risk_mitigation": ["Risk assessment procedures", "Contingency planning"],
            "business_continuity": ["Backup and recovery procedures", "Disaster recovery plans"],
            "change_management": ["Change control procedures", "User training programs"]
        }


class UXDesigner(BaseRequirementsRole):
    """Designs comprehensive user experience and interface requirements."""
    
    system_prompt = """
    You are a UX Designer specializing in comprehensive user experience and interface requirements.
    
    Your task is to design detailed UX requirements including:
    1. User interface design requirements
    2. Accessibility standards and compliance
    3. Usability goals and metrics
    4. User journey mapping
    5. Interaction patterns and behaviors
    6. Feedback mechanisms and user engagement
    7. Responsive design requirements
    
    Output your UX requirements as a JSON object with the following structure:
    {
        "user_interface_requirements": ["List of UI design requirements"],
        "accessibility_standards": ["Accessibility standards and compliance requirements"],
        "usability_goals": ["Usability goals and success metrics"],
        "user_journeys": ["Key user journeys and workflows"],
        "interaction_patterns": ["Interaction patterns and user behaviors"],
        "feedback_mechanisms": ["User feedback and engagement mechanisms"]
    }
    
    Consider user needs, accessibility, and modern UX best practices.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="UXDesigner", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="No requirements document to analyze for UX requirements")
        
        user_prompt = f"""
        Design comprehensive user experience requirements for this system:
        
        PROBLEM STATEMENT: {requirements_doc.problem_statement}
        CORE OBJECTIVES: {requirements_doc.core_objectives}
        STAKEHOLDERS: {requirements_doc.stakeholders}
        SUCCESS CRITERIA: {requirements_doc.success_criteria}
        FUNCTIONAL REQUIREMENTS: {requirements_doc.functional_requirements}
        
        Consider the user needs, accessibility requirements, and modern UX best practices.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        response_json = self._extract_json_from_code_block(response)
        
        try:
            ux_analysis = json.loads(response_json)
        except json.JSONDecodeError:
            ux_analysis = self._parse_ux_fallback(response_json)
        
        # Update the requirements document with UX requirements
        from .models import UserExperienceRequirements
        
        ux_req = UserExperienceRequirements(
            user_interface_requirements=ux_analysis.get("user_interface_requirements", []),
            accessibility_standards=ux_analysis.get("accessibility_standards", []),
            usability_goals=ux_analysis.get("usability_goals", []),
            user_journeys=ux_analysis.get("user_journeys", []),
            interaction_patterns=ux_analysis.get("interaction_patterns", []),
            feedback_mechanisms=ux_analysis.get("feedback_mechanisms", [])
        )
        
        requirements_doc.user_experience_requirements = ux_req
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            analysis=ux_analysis,
            log="UX requirements designed and integrated"
        )
    
    def _parse_ux_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for UX analysis response."""
        return {
            "user_interface_requirements": ["Intuitive navigation", "Responsive design", "Modern UI components"],
            "accessibility_standards": ["WCAG 2.1 AA compliance", "Screen reader support", "Keyboard navigation"],
            "usability_goals": ["Task completion rate > 90%", "Error rate < 5%", "User satisfaction > 4.0/5.0"],
            "user_journeys": ["Onboarding flow", "Main task completion", "Help and support"],
            "interaction_patterns": ["Progressive disclosure", "Consistent navigation", "Clear feedback"],
            "feedback_mechanisms": ["In-app feedback", "User surveys", "Analytics tracking"]
        }


class ImplementationPlanner(BaseRequirementsRole):
    """Designs comprehensive implementation plans and testing requirements."""
    
    system_prompt = """
    You are an Implementation Planner specializing in comprehensive implementation strategies and testing requirements.
    
    Your task is to design detailed implementation plans including:
    1. Implementation phases and milestones
    2. Acceptance criteria and validation
    3. Testing strategies and requirements
    4. Deployment and rollout plans
    5. Training and documentation requirements
    6. Risk mitigation during implementation
    7. Success metrics and monitoring
    
    Output your implementation plan as a JSON object with the following structure:
    {
        "implementation_phases": ["List of implementation phases"],
        "acceptance_criteria": ["Detailed acceptance criteria"],
        "testing_requirements": ["Comprehensive testing requirements"],
        "deployment_plan": "Detailed deployment strategy",
        "training_requirements": ["Training and documentation needs"],
        "risk_mitigation": ["Implementation risk mitigation strategies"],
        "success_metrics": ["Implementation success metrics"]
    }
    
    Consider project complexity, stakeholder needs, and risk management.
    """
    
    def __init__(self, **kwargs):
        super().__init__(role_name="ImplementationPlanner", **kwargs)
    
    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        requirements_doc = state.get("requirements_doc")
        if not requirements_doc:
            return RoleOutput(log="No requirements document to analyze for implementation planning")
        
        user_prompt = f"""
        Design comprehensive implementation plan for this system:
        
        PROBLEM STATEMENT: {requirements_doc.problem_statement}
        COMPLEXITY LEVEL: {requirements_doc.complexity_level.value}
        FUNCTIONAL REQUIREMENTS: {requirements_doc.functional_requirements}
        NON-FUNCTIONAL REQUIREMENTS: {requirements_doc.non_functional_requirements}
        CONSTRAINTS: {requirements_doc.constraints}
        STAKEHOLDERS: {requirements_doc.stakeholders}
        
        Consider the complexity level, requirements, and stakeholder needs when planning implementation.
        """
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self._call_llm(messages)
        response_json = self._extract_json_from_code_block(response)
        
        try:
            implementation_analysis = json.loads(response_json)
        except json.JSONDecodeError:
            implementation_analysis = self._parse_implementation_fallback(response_json)
        
        # Update the requirements document with implementation details
        requirements_doc.implementation_phases = implementation_analysis.get("implementation_phases", [])
        requirements_doc.acceptance_criteria = implementation_analysis.get("acceptance_criteria", [])
        requirements_doc.testing_requirements = implementation_analysis.get("testing_requirements", [])
        
        return RoleOutput(
            requirements_doc=requirements_doc,
            analysis=implementation_analysis,
            log="Implementation plan designed and integrated"
        )
    
    def _parse_implementation_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for implementation analysis response."""
        return {
            "implementation_phases": [
                "Phase 1: Core functionality development",
                "Phase 2: Integration and testing",
                "Phase 3: User acceptance testing",
                "Phase 4: Production deployment"
            ],
            "acceptance_criteria": [
                "All functional requirements implemented",
                "Performance targets met",
                "Security requirements satisfied",
                "User acceptance testing passed"
            ],
            "testing_requirements": [
                "Unit testing for all components",
                "Integration testing for system modules",
                "Performance testing under load",
                "Security testing and penetration testing",
                "User acceptance testing with stakeholders"
            ],
            "deployment_plan": "Staged deployment with rollback capability",
            "training_requirements": [
                "User training and documentation",
                "Administrator training",
                "Technical documentation"
            ],
            "risk_mitigation": [
                "Regular progress reviews",
                "Contingency planning",
                "Stakeholder communication"
            ],
            "success_metrics": [
                "On-time delivery",
                "Budget adherence",
                "Quality standards met",
                "User satisfaction achieved"
            ]
        } 