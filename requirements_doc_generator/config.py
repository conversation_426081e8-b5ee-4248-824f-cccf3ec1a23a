import os
import yaml
from pathlib import Path

CONFIG_PATH = Path(__file__).parent / "config.yaml"

_default_config = {
    "llm": {
        "model": "o4-mini-2025-04-16",
        "temperature": 1.0,
        "max_tokens": 4000,
        "api_key_env": "OPENAI_API_KEY",
        "roles": {}
    },
    "alignment_validator": {
        "model": "o4-mini-2025-04-16",
        "temperature": 1.0,
        "max_tokens": 4000
    }
}

def load_config():
    if CONFIG_PATH.exists():
        with open(CONFIG_PATH, "r") as f:
            config = yaml.safe_load(f)
        # Merge with defaults
        merged = _default_config.copy()
        for section in config or {}:
            if section in merged and isinstance(merged[section], dict):
                merged[section].update(config[section] or {})
            else:
                merged[section] = config[section]
        return merged
    else:
        return _default_config.copy()

def get_llm_config():
    return load_config()["llm"]

def get_alignment_validator_config():
    return load_config()["alignment_validator"]

def get_role_llm_config(role_name: str):
    cfg = get_llm_config()
    role_cfg = (cfg.get("roles") or {}).get(role_name, {})
    merged = cfg.copy()
    merged.update(role_cfg)
    return merged

# Export the loaded config as 'cfg' for backward compatibility
cfg = get_llm_config() 