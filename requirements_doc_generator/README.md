# Requirements Document Generator

A sophisticated AI-powered system that transforms simple initial prompts into comprehensive requirements documents with detailed analysis, workflow expectations, and quality metrics.

## Overview

The Requirements Document Generator works alongside the main prompt generator to provide complete documentation and specification for prompt engineering projects. It uses a multi-agent system to analyze initial prompts and generate structured requirements documents.

## Features

### 🎯 Core Functionality
- **Multi-Agent Analysis**: Uses specialized AI agents (ProblemAnalyzer, SolutionArchitect, QualityEngineer, RequirementsValidator)
- **Comprehensive Output**: Generates three main components:
  - **RequirementsDoc**: Problem analysis, objectives, and key requirements
  - **WorkflowExpectations**: Input/output specifications and processing workflow
  - **QualityMetrics**: Accuracy thresholds and validation criteria
- **Multiple Output Formats**: JSON for process flow and human-readable markdown
- **Validation**: Ensures requirements completeness, consistency, and alignment

### 🔧 Advanced Features
- **Domain Detection**: Automatically identifies domain context (financial, technical, medical, etc.)
- **Complexity Assessment**: Determines complexity level and priority
- **Quality Standards**: Defines realistic but high-quality thresholds
- **Iterative Refinement**: Multiple iterations for comprehensive analysis

## Architecture

### Multi-Agent System
The system uses 4 specialized AI agents:

1. **ProblemAnalyzer**: Extracts core problem, objectives, and stakeholders
2. **SolutionArchitect**: Designs solution approach and workflow expectations
3. **QualityEngineer**: Defines quality metrics and validation criteria
4. **RequirementsValidator**: Validates completeness and coherence

### Output Structure

#### RequirementsDoc Object
```json
{
  "problem_statement": "Clear description of the problem",
  "core_objectives": ["List of main objectives"],
  "solution_approach": "How we plan to solve this",
  "key_requirements": ["Essential requirements"],
  "stakeholders": ["Who this affects"],
  "success_criteria": ["How we know it works"],
  "domain": "Domain context",
  "complexity_level": "simple|moderate|complex|enterprise",
  "priority_level": "low|medium|high|critical"
}
```

#### WorkflowExpectations Object
```json
{
  "input_format": "Expected input format",
  "output_format": "Expected output format",
  "processing_steps": ["Step-by-step workflow"],
  "error_handling": {"error_type": "handling_strategy"},
  "performance_expectations": {"metric": "value"},
  "integration_points": ["Integration requirements"]
}
```

#### QualityMetrics Object
```json
{
  "accuracy_threshold": 0.9,
  "precision_threshold": 0.85,
  "completeness_score": 0.9,
  "relevance_score": 0.85,
  "response_time_threshold": 2.0,
  "validation_criteria": ["List of validation criteria"],
  "acceptance_criteria": ["List of acceptance criteria"]
}
```

## Installation

### Prerequisites
- Python 3.8+
- OpenAI API key

### Setup
1. **Set your OpenAI API key**
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   ```

2. **Install dependencies**
   ```bash
   pip install openai
   ```

## Usage

### Python API
```python
from requirements_doc_generator import RequirementsGenerator

# Initialize generator
generator = RequirementsGenerator()

# Generate requirements document
document = generator.generate("Create a professional email template")

# Get JSON output
json_output = generator.generate_json("Create a professional email template")

# Get Markdown output
markdown_output = generator.generate_markdown("Create a professional email template")
```

### Command Line Interface
```bash
# Generate both JSON and Markdown output
python -m requirements_doc_generator.cli "Create a professional email template"

# Generate only JSON output
python -m requirements_doc_generator.cli "Create a professional email template" --output-format json

# Save to file
python -m requirements_doc_generator.cli "Create a professional email template" --output-file requirements

# Custom settings
python -m requirements_doc_generator.cli "Create a professional email template" \
  --iterations 5 \
  --model gpt-4o \
  --temperature 0.8
```

### Testing
```bash
# Run the test script
python requirements_doc_generator/test_generator.py
```

## Examples

### Simple Prompt
**Input**: "Create a professional email template"

**Output**:
- **RequirementsDoc**: Problem analysis, objectives, stakeholders
- **WorkflowExpectations**: Input/output formats, processing steps
- **QualityMetrics**: Accuracy thresholds, validation criteria

### Complex Prompt
**Input**: "Build a financial analysis system for investment portfolio management"

**Output**:
- **Domain**: Financial
- **Complexity**: Enterprise
- **Priority**: High
- **Quality Metrics**: High accuracy thresholds
- **Validation**: Regulatory compliance considerations

## Integration with Prompt Generator

The Requirements Document Generator is designed to work alongside the main prompt generator:

1. **Initial Analysis**: Use requirements generator to analyze the initial prompt
2. **Requirements Document**: Get comprehensive requirements specification
3. **Prompt Generation**: Use the main prompt generator with the requirements as context
4. **Validation**: Ensure the generated prompt meets the requirements

## Configuration

### Model Settings
```python
generator = RequirementsGenerator(
    model="gpt-4o",           # LLM model to use
    temperature=0.7           # Creativity level
)
```

### Iteration Settings
```python
document = generator.generate(
    initial_prompt="Your prompt",
    max_iterations=3          # Number of refinement iterations
)
```

## Validation

The system includes comprehensive validation:

- **Completeness**: Ensures all necessary requirements are captured
- **Consistency**: Checks for internal contradictions
- **Feasibility**: Validates implementation possibility
- **Alignment**: Verifies requirements match original prompt

## Error Handling

The system includes robust error handling:

- **LLM Failures**: Graceful degradation with fallback parsing
- **JSON Parsing**: Automatic fallback to regex-based extraction
- **Missing Data**: Default values for incomplete information
- **Validation Issues**: Clear reporting of problems and recommendations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
- Check the test script for examples
- Review the validation output for issues
- Ensure your OpenAI API key is set correctly 