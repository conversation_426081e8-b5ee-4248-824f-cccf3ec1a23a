"""
CLI interface for the Requirements Document Generator.
"""

import argparse
import json
import sys
from pathlib import Path

from .core import RequirementsGenerator


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Generate comprehensive requirements documents from initial prompts"
    )
    
    parser.add_argument(
        "prompt",
        help="The initial prompt to analyze and generate requirements for"
    )
    
    parser.add_argument(
        "--output-format",
        choices=["json", "markdown", "both"],
        default="both",
        help="Output format (default: both)"
    )
    
    parser.add_argument(
        "--output-file",
        type=Path,
        help="Output file path (if not specified, prints to stdout)"
    )
    
    parser.add_argument(
        "--iterations",
        type=int,
        default=3,
        help="Maximum number of refinement iterations (default: 3)"
    )
    
    parser.add_argument(
        "--model",
        default="gpt-4o",
        help="LLM model to use (default: gpt-4o)"
    )
    
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.7,
        help="Temperature for LLM calls (default: 0.7)"
    )
    
    args = parser.parse_args()
    
    # Initialize generator
    generator = RequirementsGenerator(
        model=args.model,
        temperature=args.temperature
    )
    
    try:
        print(f"[CLI] Generating requirements document for: {args.prompt[:100]}...")
        
        if args.output_format in ["json", "both"]:
            # Generate JSON output
            json_result = generator.generate_json(args.prompt, args.iterations)
            
            if args.output_file:
                json_file = args.output_file.with_suffix(".json")
                with open(json_file, "w") as f:
                    json.dump(json_result, f, indent=2)
                print(f"[CLI] JSON output saved to: {json_file}")
            else:
                print("\n" + "="*50)
                print("JSON OUTPUT:")
                print("="*50)
                print(json.dumps(json_result, indent=2))
        
        if args.output_format in ["markdown", "both"]:
            # Generate Markdown output
            markdown_result = generator.generate_markdown(args.prompt, args.iterations)
            
            if args.output_file:
                md_file = args.output_file.with_suffix(".md")
                with open(md_file, "w") as f:
                    f.write(markdown_result)
                print(f"[CLI] Markdown output saved to: {md_file}")
            else:
                print("\n" + "="*50)
                print("MARKDOWN OUTPUT:")
                print("="*50)
                print(markdown_result)
        
        print(f"[CLI] Requirements document generation completed successfully!")
        
    except Exception as e:
        print(f"[CLI] Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main() 