"""
Integration Example: Requirements Document Generator + Prompt Generator + Alignment Validation.

This example shows how to use the requirements document generator alongside the prompt generator
to ensure alignment between requirements and generated prompts.
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from requirements_doc_generator.core import RequirementsGenerator
from requirements_doc_generator.alignment_validator import AlignmentValidator, PromptRequirementsIntegrator


def example_integration():
    """Example of integrating requirements generation with prompt generation."""
    
    print("🔗 Requirements + Prompt Generator Integration Example")
    print("="*60)
    
    # Check API key
    if not os.environ.get('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY environment variable not set!")
        return False
    
    # Test prompt
    test_prompt = "Create a professional email template for job applications"
    
    try:
        # Initialize components
        print("📋 Initializing components...")
        requirements_generator = RequirementsGenerator()
        alignment_validator = AlignmentValidator()
        
        # Step 1: Generate requirements document
        print("\n📋 Step 1: Generating requirements document...")
        requirements_doc = requirements_generator.generate(test_prompt)
        
        print(f"✅ Requirements document generated!")
        print(f"   - Problem: {requirements_doc.requirements_doc.problem_statement[:80]}...")
        print(f"   - Objectives: {len(requirements_doc.requirements_doc.core_objectives)} objectives")
        print(f"   - Domain: {requirements_doc.requirements_doc.domain}")
        
        # Step 2: Generate a prompt (simulated - in real use, this would be your prompt generator)
        print("\n🤖 Step 2: Generating prompt...")
        generated_prompt = f"""
        You are a professional email writer specializing in job applications.
        
        Create a comprehensive email template for job applications that includes:
        - Professional greeting and introduction
        - Clear statement of interest and qualifications
        - Specific examples of relevant experience
        - Professional closing and contact information
        - Appropriate tone and formatting
        
        The template should be adaptable for different positions and companies.
        """
        
        print(f"✅ Prompt generated!")
        print(f"   - Length: {len(generated_prompt)} characters")
        
        # Step 3: Validate alignment
        print("\n🔍 Step 3: Validating alignment...")
        alignment_result = alignment_validator.validate_alignment(requirements_doc, generated_prompt)
        
        print(f"✅ Alignment validation completed!")
        print(f"   - Overall Score: {alignment_result.alignment_score:.1%}")
        print(f"   - Problem Alignment: {alignment_result.problem_alignment:.1%}")
        print(f"   - Requirements Coverage: {alignment_result.requirements_coverage:.1%}")
        
        # Step 4: Generate alignment report
        print("\n📊 Step 4: Generating alignment report...")
        alignment_report = alignment_validator.generate_alignment_report(alignment_result)
        
        print("✅ Alignment report generated!")
        print("\n" + "="*50)
        print("ALIGNMENT REPORT")
        print("="*50)
        print(alignment_report)
        
        # Step 5: Show integration workflow
        print("\n🔄 Step 5: Integration Workflow Example...")
        
        # Simulate the integrator (in real use, you'd pass your actual prompt generator)
        class MockPromptGenerator:
            def generate(self, prompt):
                return f"Generated prompt for: {prompt}"
        
        mock_prompt_generator = MockPromptGenerator()
        
        integrator = PromptRequirementsIntegrator(
            requirements_generator=requirements_generator,
            prompt_generator=mock_prompt_generator,
            alignment_validator=alignment_validator
        )
        
        print("✅ Integration workflow ready!")
        print("   - Requirements → Prompt → Validation → Improvement")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration example failed: {str(e)}")
        return False


def example_alignment_validation():
    """Example of just the alignment validation functionality."""
    
    print("\n🔍 Alignment Validation Example")
    print("="*40)
    
    # Create a sample requirements document
    from requirements_doc_generator.models import RequirementsDocument, RequirementsDoc, WorkflowExpectations, QualityMetrics, ComplexityLevel, PriorityLevel
    
    requirements_doc = RequirementsDocument(
        requirements_doc=RequirementsDoc(
            problem_statement="Need to create professional email templates for job applications",
            core_objectives=["Create professional templates", "Ensure adaptability", "Maintain quality standards"],
            key_requirements=["Professional tone", "Clear structure", "Contact information", "Customizable content"],
            domain="professional",
            complexity_level=ComplexityLevel.MODERATE,
            priority_level=PriorityLevel.HIGH
        ),
        workflow_expectations=WorkflowExpectations(
            input_format="Job description and candidate details",
            output_format="Professional email template",
            processing_steps=["Analyze job requirements", "Create template structure", "Add customization options"]
        ),
        quality_metrics=QualityMetrics(
            accuracy_threshold=0.9,
            completeness_score=0.85,
            response_time_threshold=2.0
        ),
        original_prompt="Create a professional email template"
    )
    
    # Test prompts with different alignment levels
    test_prompts = [
        {
            "name": "Well-Aligned Prompt",
            "prompt": """
            You are a professional email writer. Create a comprehensive job application email template that includes:
            - Professional greeting and introduction
            - Clear statement of interest and qualifications  
            - Specific examples of relevant experience
            - Professional closing with contact information
            - Adaptable format for different positions
            """,
            "expected_score": "High"
        },
        {
            "name": "Partially Aligned Prompt", 
            "prompt": """
            Write an email template for job applications.
            """,
            "expected_score": "Medium"
        },
        {
            "name": "Poorly Aligned Prompt",
            "prompt": """
            Create a recipe for chocolate cake.
            """,
            "expected_score": "Low"
        }
    ]
    
    alignment_validator = AlignmentValidator()
    
    for test_case in test_prompts:
        print(f"\n📝 Testing: {test_case['name']}")
        print(f"Expected Score: {test_case['expected_score']}")
        
        try:
            alignment_result = alignment_validator.validate_alignment(
                requirements_doc, test_case['prompt']
            )
            
            print(f"✅ Actual Score: {alignment_result.alignment_score:.1%}")
            print(f"   - Problem Alignment: {alignment_result.problem_alignment:.1%}")
            print(f"   - Requirements Coverage: {alignment_result.requirements_coverage:.1%}")
            
            if alignment_result.alignment_issues:
                print(f"   - Issues: {len(alignment_result.alignment_issues)} found")
            
        except Exception as e:
            print(f"❌ Validation failed: {str(e)}")


if __name__ == "__main__":
    print("🚀 Requirements Document Generator Integration Examples")
    print("="*60)
    
    # Run integration example
    success1 = example_integration()
    
    # Run alignment validation example
    example_alignment_validation()
    
    if success1:
        print("\n🎉 All examples completed successfully!")
    else:
        print("\n❌ Some examples failed. Check the output above.") 