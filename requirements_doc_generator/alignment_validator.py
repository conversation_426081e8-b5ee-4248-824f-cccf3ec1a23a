"""
Alignment Validator for Requirements Document and Generated Prompt.

Ensures that generated prompts align with the requirements document and provides
feedback for improvement.
"""

from typing import Dict, Any, List, <PERSON>ple
from dataclasses import dataclass
import json
from openai import OpenAI
import os

from .models import RequirementsDocument, RequirementsDoc, WorkflowExpectations, QualityMetrics
from .config import get_alignment_validator_config


@dataclass
class AlignmentResult:
    """Result of alignment validation between requirements and prompt."""
    
    # Overall alignment score (0-1)
    alignment_score: float
    
    # Detailed alignment metrics
    problem_alignment: float
    objectives_alignment: float
    requirements_coverage: float
    workflow_alignment: float
    quality_alignment: float
    
    # Specific issues found
    alignment_issues: List[str]
    
    # Recommendations for improvement
    recommendations: List[str]
    
    # Validation status
    is_aligned: bool
    
    # Detailed analysis
    detailed_analysis: Dict[str, Any]


class AlignmentValidator:
    """Validates alignment between requirements document and generated prompt."""
    def __init__(self, model: str = None, temperature: float = None, max_tokens: int = None):
        cfg = get_alignment_validator_config()
        self.model = model or cfg.get("model", "o4-mini-2025-04-16")
        self.temperature = temperature if temperature is not None else cfg.get("temperature", 1.0)
        self.max_tokens = max_tokens if max_tokens is not None else cfg.get("max_tokens", 4000)
        api_key = os.environ.get(cfg.get("api_key_env", "OPENAI_API_KEY"), os.environ.get("OPENAI_API_KEY"))
        self.client = OpenAI(api_key=api_key) if api_key else None
    
    def validate_alignment(self, requirements_doc: RequirementsDocument, generated_prompt: str) -> AlignmentResult:
        """
        Validate that the generated prompt aligns with the requirements document.
        
        Args:
            requirements_doc: The requirements document
            generated_prompt: The generated prompt to validate
            
        Returns:
            AlignmentResult: Detailed alignment analysis
        """
        if not self.client:
            raise RuntimeError("OpenAI client not initialized. Set OPENAI_API_KEY environment variable.")
        
        # Create comprehensive validation prompt
        validation_prompt = self._create_validation_prompt(requirements_doc, generated_prompt)
        
        try:
            # Use max_completion_tokens for newer models, max_tokens for older ones
            if self.model.startswith("o4-") or self.model.startswith("gpt-4o"):
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self._get_system_prompt()},
                        {"role": "user", "content": validation_prompt}
                    ],
                    temperature=self.temperature,
                    max_completion_tokens=self.max_tokens
                )
            else:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": self._get_system_prompt()},
                        {"role": "user", "content": validation_prompt}
                    ],
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
            
            validation_result = response.choices[0].message.content.strip()
            return self._parse_validation_result(validation_result)
            
        except Exception as e:
            return self._create_fallback_result(str(e))
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for alignment validation."""
        return """
        You are an Alignment Validator specializing in ensuring that generated prompts
        align with their requirements documents.
        
        Your task is to analyze the alignment between a requirements document and a
        generated prompt, providing detailed feedback on:
        
        1. Problem Alignment: Does the prompt address the core problem?
        2. Objectives Coverage: Does the prompt meet the stated objectives?
        3. Requirements Coverage: Does the prompt cover all key requirements?
        4. Workflow Alignment: Does the prompt match the expected workflow?
        5. Quality Standards: Does the prompt meet quality expectations?
        
        Output your analysis as a JSON object with the following structure:
        {
            "alignment_score": 0.85,
            "problem_alignment": 0.9,
            "objectives_alignment": 0.8,
            "requirements_coverage": 0.85,
            "workflow_alignment": 0.75,
            "quality_alignment": 0.8,
            "alignment_issues": ["List of specific issues"],
            "recommendations": ["List of improvement suggestions"],
            "is_aligned": true,
            "detailed_analysis": {
                "problem_analysis": "Detailed analysis of problem alignment",
                "objectives_analysis": "Detailed analysis of objectives coverage",
                "requirements_analysis": "Detailed analysis of requirements coverage",
                "workflow_analysis": "Detailed analysis of workflow alignment",
                "quality_analysis": "Detailed analysis of quality standards"
            }
        }
        
        Be thorough and constructive in your analysis. Focus on actionable feedback.
        """
    
    def _create_validation_prompt(self, requirements_doc: RequirementsDocument, generated_prompt: str) -> str:
        """Create the validation prompt for alignment analysis."""
        
        req_doc = requirements_doc.requirements_doc
        workflow = requirements_doc.workflow_expectations
        quality = requirements_doc.quality_metrics
        
        return f"""
        Please validate the alignment between this requirements document and the generated prompt.
        
        REQUIREMENTS DOCUMENT:
        
        Problem Statement: {req_doc.problem_statement}
        
        Core Objectives:
        {chr(10).join(f"- {obj}" for obj in req_doc.core_objectives)}
        
        Key Requirements:
        {chr(10).join(f"- {req}" for req in req_doc.key_requirements)}
        
        Domain: {req_doc.domain}
        Complexity Level: {req_doc.complexity_level.value}
        Priority Level: {req_doc.priority_level.value}
        
        Workflow Expectations:
        - Input Format: {workflow.input_format}
        - Output Format: {workflow.output_format}
        - Processing Steps: {chr(10).join(f"  {i+1}. {step}" for i, step in enumerate(workflow.processing_steps))}
        
        Quality Metrics:
        - Accuracy Threshold: {quality.accuracy_threshold}
        - Completeness Score: {quality.completeness_score}
        - Response Time: {quality.response_time_threshold}s
        
        GENERATED PROMPT:
        {generated_prompt}
        
        Please analyze the alignment between the requirements and the generated prompt.
        Provide specific feedback on what's aligned, what's missing, and how to improve.
        """
    
    def _parse_validation_result(self, result: str) -> AlignmentResult:
        """Parse the validation result from LLM response."""
        try:
            # Try to extract JSON from the response
            json_start = result.find('{')
            json_end = result.rfind('}') + 1
            
            if json_start != -1 and json_end != 0:
                json_str = result[json_start:json_end]
                data = json.loads(json_str)
            else:
                # Fallback parsing
                data = self._parse_fallback_result(result)
            
            return AlignmentResult(
                alignment_score=data.get("alignment_score", 0.0),
                problem_alignment=data.get("problem_alignment", 0.0),
                objectives_alignment=data.get("objectives_alignment", 0.0),
                requirements_coverage=data.get("requirements_coverage", 0.0),
                workflow_alignment=data.get("workflow_alignment", 0.0),
                quality_alignment=data.get("quality_alignment", 0.0),
                alignment_issues=data.get("alignment_issues", []),
                recommendations=data.get("recommendations", []),
                is_aligned=data.get("is_aligned", False),
                detailed_analysis=data.get("detailed_analysis", {})
            )
            
        except (json.JSONDecodeError, KeyError) as e:
            return self._create_fallback_result(f"Failed to parse validation result: {str(e)}")
    
    def _parse_fallback_result(self, result: str) -> AlignmentResult:
        """Create a fallback result when parsing fails."""
        return AlignmentResult(
            alignment_score=0.5,
            problem_alignment=0.5,
            objectives_alignment=0.5,
            requirements_coverage=0.5,
            workflow_alignment=0.5,
            quality_alignment=0.5,
            alignment_issues=["Failed to parse validation result"],
            recommendations=["Review the alignment manually"],
            is_aligned=False,
            detailed_analysis={"error": result}
        )
    
    def _create_fallback_result(self, error: str) -> AlignmentResult:
        """Create a fallback result when validation fails."""
        return AlignmentResult(
            alignment_score=0.0,
            problem_alignment=0.0,
            objectives_alignment=0.0,
            requirements_coverage=0.0,
            workflow_alignment=0.0,
            quality_alignment=0.0,
            alignment_issues=[f"Validation failed: {error}"],
            recommendations=["Check API key and try again"],
            is_aligned=False,
            detailed_analysis={"error": error}
        )
    
    def generate_alignment_report(self, alignment_result: AlignmentResult) -> str:
        """Generate a human-readable alignment report."""
        report = []
        
        report.append("# Alignment Validation Report")
        report.append("")
        
        # Overall score
        score_icon = "✅" if alignment_result.is_aligned else "❌"
        report.append(f"## Overall Alignment: {score_icon} {alignment_result.alignment_score:.1%}")
        report.append("")
        
        # Detailed metrics
        report.append("## Detailed Alignment Metrics")
        report.append("")
        report.append(f"- **Problem Alignment**: {alignment_result.problem_alignment:.1%}")
        report.append(f"- **Objectives Alignment**: {alignment_result.objectives_alignment:.1%}")
        report.append(f"- **Requirements Coverage**: {alignment_result.requirements_coverage:.1%}")
        report.append(f"- **Workflow Alignment**: {alignment_result.workflow_alignment:.1%}")
        report.append(f"- **Quality Alignment**: {alignment_result.quality_alignment:.1%}")
        report.append("")
        
        # Issues
        if alignment_result.alignment_issues:
            report.append("## Alignment Issues")
            report.append("")
            for issue in alignment_result.alignment_issues:
                report.append(f"- ❌ {issue}")
            report.append("")
        
        # Recommendations
        if alignment_result.recommendations:
            report.append("## Recommendations")
            report.append("")
            for rec in alignment_result.recommendations:
                report.append(f"- 💡 {rec}")
            report.append("")
        
        # Detailed analysis
        if alignment_result.detailed_analysis:
            report.append("## Detailed Analysis")
            report.append("")
            for key, value in alignment_result.detailed_analysis.items():
                if key != "error":
                    report.append(f"### {key.replace('_', ' ').title()}")
                    report.append(str(value))
                    report.append("")
        
        return "\n".join(report)


class PromptRequirementsIntegrator:
    """Integrates requirements validation with prompt generation for iterative improvement."""
    
    def __init__(self, requirements_generator, prompt_generator, alignment_validator):
        self.requirements_generator = requirements_generator
        self.prompt_generator = prompt_generator
        self.alignment_validator = alignment_validator
    
    def generate_aligned_prompt(self, initial_prompt: str, max_iterations: int = 3) -> Tuple[str, RequirementsDocument, AlignmentResult]:
        """
        Generate a prompt that aligns with comprehensive requirements.
        
        Args:
            initial_prompt: The initial prompt
            max_iterations: Maximum iterations for improvement
            
        Returns:
            Tuple of (final_prompt, requirements_doc, alignment_result)
        """
        print(f"[INTEGRATOR] Starting aligned prompt generation for: {initial_prompt[:100]}...")
        
        # Step 1: Generate requirements document
        print("[INTEGRATOR] Step 1: Generating requirements document...")
        requirements_doc = self.requirements_generator.generate(initial_prompt)
        
        # Step 2: Generate initial prompt
        print("[INTEGRATOR] Step 2: Generating initial prompt...")
        generated_prompt = self._generate_prompt_with_requirements(initial_prompt, requirements_doc)
        
        # Step 3: Validate alignment
        print("[INTEGRATOR] Step 3: Validating alignment...")
        alignment_result = self.alignment_validator.validate_alignment(requirements_doc, generated_prompt)
        
        # Step 4: Iterative improvement
        best_prompt = generated_prompt
        best_alignment = alignment_result
        
        for iteration in range(max_iterations - 1):
            print(f"[INTEGRATOR] Iteration {iteration + 2}: Improving alignment...")
            
            # Generate improved prompt based on alignment feedback
            improved_prompt = self._improve_prompt_with_feedback(
                initial_prompt, requirements_doc, alignment_result
            )
            
            # Validate improved prompt
            improved_alignment = self.alignment_validator.validate_alignment(
                requirements_doc, improved_prompt
            )
            
            # Keep the better result
            if improved_alignment.alignment_score > best_alignment.alignment_score:
                best_prompt = improved_prompt
                best_alignment = improved_alignment
                print(f"[INTEGRATOR] Alignment improved to {best_alignment.alignment_score:.1%}")
            else:
                print(f"[INTEGRATOR] No improvement, keeping previous result")
        
        print(f"[INTEGRATOR] Final alignment score: {best_alignment.alignment_score:.1%}")
        return best_prompt, requirements_doc, best_alignment
    
    def _generate_prompt_with_requirements(self, initial_prompt: str, requirements_doc: RequirementsDocument) -> str:
        """Generate a prompt using the requirements document as context."""
        # This would integrate with your existing prompt generator
        # For now, we'll create a simple enhanced prompt
        req_doc = requirements_doc.requirements_doc
        
        enhanced_prompt = f"""
        Based on the following requirements:
        
        Problem: {req_doc.problem_statement}
        Objectives: {', '.join(req_doc.core_objectives)}
        Requirements: {', '.join(req_doc.key_requirements)}
        Domain: {req_doc.domain}
        Complexity: {req_doc.complexity_level.value}
        
        Generate a comprehensive prompt for: {initial_prompt}
        
        The prompt should:
        - Address the core problem directly
        - Meet all stated objectives
        - Cover all key requirements
        - Match the specified domain and complexity level
        - Include appropriate quality standards
        """
        
        # In a real implementation, this would call your prompt generator
        # For now, return the enhanced prompt
        return enhanced_prompt
    
    def _improve_prompt_with_feedback(self, initial_prompt: str, requirements_doc: RequirementsDocument, alignment_result: AlignmentResult) -> str:
        """Improve the prompt based on alignment feedback."""
        
        improvement_prompt = f"""
        The following prompt was generated but has alignment issues:
        
        ORIGINAL PROMPT: {initial_prompt}
        
        REQUIREMENTS:
        - Problem: {requirements_doc.requirements_doc.problem_statement}
        - Objectives: {', '.join(requirements_doc.requirements_doc.core_objectives)}
        - Requirements: {', '.join(requirements_doc.requirements_doc.key_requirements)}
        
        ALIGNMENT ISSUES:
        {chr(10).join(f"- {issue}" for issue in alignment_result.alignment_issues)}
        
        RECOMMENDATIONS:
        {chr(10).join(f"- {rec}" for rec in alignment_result.recommendations)}
        
        Please generate an improved prompt that addresses these alignment issues.
        """
        
        # In a real implementation, this would call your prompt generator
        # For now, return an improved version
        return improvement_prompt 