"""
Test script for the Requirements Document Generator.
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import the requirements_doc_generator
sys.path.insert(0, str(Path(__file__).parent.parent))

from requirements_doc_generator.core import RequirementsGenerator


def test_simple_prompt():
    """Test with a simple prompt."""
    print("Testing Requirements Document Generator...")
    print("="*50)
    
    # Check if OpenAI API key is set
    if not os.environ.get('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY environment variable not set!")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        return False
    
    # Test prompt
    test_prompt = "Create a professional email template for job applications"
    
    try:
        # Initialize generator
        generator = RequirementsGenerator()
        
        print(f"📝 Testing with prompt: {test_prompt}")
        print("🔄 Generating requirements document...")
        
        # Generate requirements document
        document = generator.generate(test_prompt, max_iterations=1)
        
        print("✅ Requirements document generated successfully!")
        print("\n📊 Summary:")
        print(f"   - Problem Statement: {document.requirements_doc.problem_statement[:100]}...")
        print(f"   - Core Objectives: {len(document.requirements_doc.core_objectives)} objectives")
        print(f"   - Key Requirements: {len(document.requirements_doc.key_requirements)} requirements")
        print(f"   - Domain: {document.requirements_doc.domain}")
        print(f"   - Complexity: {document.requirements_doc.complexity_level.value}")
        print(f"   - Priority: {document.requirements_doc.priority_level.value}")
        
        print(f"\n🔧 Workflow Expectations:")
        print(f"   - Input Format: {document.workflow_expectations.input_format}")
        print(f"   - Output Format: {document.workflow_expectations.output_format}")
        print(f"   - Processing Steps: {len(document.workflow_expectations.processing_steps)} steps")
        
        print(f"\n📈 Quality Metrics:")
        print(f"   - Accuracy Threshold: {document.quality_metrics.accuracy_threshold}")
        print(f"   - Completeness Score: {document.quality_metrics.completeness_score}")
        print(f"   - Response Time: {document.quality_metrics.response_time_threshold}s")
        
        print(f"\n✅ Validation Status:")
        for aspect, status in document.validation_status.items():
            status_icon = "✅" if status else "❌"
            print(f"   - {aspect.title()}: {status_icon}")
        
        # Test JSON output
        print("\n🔄 Testing JSON output...")
        json_output = generator.generate_json(test_prompt, max_iterations=1)
        print(f"✅ JSON output generated successfully! ({len(str(json_output))} characters)")
        
        # Test Markdown output
        print("\n🔄 Testing Markdown output...")
        markdown_output = generator.generate_markdown(test_prompt, max_iterations=1)
        print(f"✅ Markdown output generated successfully! ({len(markdown_output)} characters)")
        
        print("\n🎉 All tests passed! The Requirements Document Generator is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False


if __name__ == "__main__":
    success = test_simple_prompt()
    sys.exit(0 if success else 1) 