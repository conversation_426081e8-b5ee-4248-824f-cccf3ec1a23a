"""
Requirements Document Generator

A sophisticated system that transforms initial prompts into comprehensive
requirements documents with detailed analysis, workflow expectations, and quality metrics.

This module works alongside the prompt generator to provide complete
documentation and specification for prompt engineering projects.
"""

__version__ = "1.0.0"
__author__ = "Prompt Generator Team"

from .core import RequirementsGenerator
from .models import RequirementsDoc, WorkflowExpectations, QualityMetrics
from .validators import RequirementsValidator

__all__ = [
    "RequirementsGenerator",
    "RequirementsDoc", 
    "WorkflowExpectations",
    "QualityMetrics",
    "RequirementsValidator"
] 