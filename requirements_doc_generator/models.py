"""
Data models for requirements document generator.

Defines the structure for comprehensive RequirementsDoc, WorkflowExpectations, and QualityMetrics objects.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class ComplexityLevel(Enum):
    """Complexity levels for requirements."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    ENTERPRISE = "enterprise"


class PriorityLevel(Enum):
    """Priority levels for requirements."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskLevel(Enum):
    """Risk levels for requirements."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityRequirements:
    """Security and compliance requirements."""
    authentication_methods: List[str] = field(default_factory=list)
    authorization_levels: List[str] = field(default_factory=list)
    data_encryption: List[str] = field(default_factory=list)
    compliance_standards: List[str] = field(default_factory=list)
    audit_requirements: List[str] = field(default_factory=list)
    privacy_requirements: List[str] = field(default_factory=list)
    security_testing: List[str] = field(default_factory=list)


@dataclass
class TechnicalSpecifications:
    """Technical implementation specifications."""
    architecture_patterns: List[str] = field(default_factory=list)
    technology_stack: List[str] = field(default_factory=list)
    data_models: List[str] = field(default_factory=list)
    api_specifications: List[str] = field(default_factory=list)
    integration_patterns: List[str] = field(default_factory=list)
    deployment_strategy: str = ""
    scalability_approach: str = ""
    performance_targets: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BusinessRequirements:
    """Business and operational requirements."""
    business_processes: List[str] = field(default_factory=list)
    operational_procedures: List[str] = field(default_factory=list)
    reporting_requirements: List[str] = field(default_factory=list)
    compliance_requirements: List[str] = field(default_factory=list)
    risk_mitigation: List[str] = field(default_factory=list)
    business_continuity: List[str] = field(default_factory=list)
    change_management: List[str] = field(default_factory=list)


@dataclass
class UserExperienceRequirements:
    """User experience and interface requirements."""
    user_interface_requirements: List[str] = field(default_factory=list)
    accessibility_standards: List[str] = field(default_factory=list)
    usability_goals: List[str] = field(default_factory=list)
    user_journeys: List[str] = field(default_factory=list)
    interaction_patterns: List[str] = field(default_factory=list)
    feedback_mechanisms: List[str] = field(default_factory=list)


@dataclass
class RequirementsDoc:
    """Comprehensive requirements document structure."""
    
    # Core problem analysis
    problem_statement: str
    core_objectives: List[str]
    solution_approach: str
    
    # Detailed requirements
    key_requirements: List[str]
    functional_requirements: List[str] = field(default_factory=list)
    non_functional_requirements: List[str] = field(default_factory=list)
    
    # Constraints and context
    constraints: List[str] = field(default_factory=list)
    assumptions: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    # Stakeholders and success
    stakeholders: List[str] = field(default_factory=list)
    success_criteria: List[str] = field(default_factory=list)
    
    # Enhanced sections
    security_requirements: SecurityRequirements = field(default_factory=SecurityRequirements)
    technical_specifications: TechnicalSpecifications = field(default_factory=TechnicalSpecifications)
    business_requirements: BusinessRequirements = field(default_factory=BusinessRequirements)
    user_experience_requirements: UserExperienceRequirements = field(default_factory=UserExperienceRequirements)
    
    # Risk and compliance
    risk_assessment: Dict[str, RiskLevel] = field(default_factory=dict)
    compliance_requirements: List[str] = field(default_factory=list)
    
    # Implementation details
    implementation_phases: List[str] = field(default_factory=list)
    acceptance_criteria: List[str] = field(default_factory=list)
    testing_requirements: List[str] = field(default_factory=list)
    
    # Metadata
    complexity_level: ComplexityLevel = ComplexityLevel.MODERATE
    priority_level: PriorityLevel = PriorityLevel.MEDIUM
    created_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0.0"
    
    # Additional context
    domain: Optional[str] = None
    industry: Optional[str] = None
    regulatory_requirements: List[str] = field(default_factory=list)


@dataclass
class WorkflowExpectations:
    """Expected workflow and behavior specifications."""
    
    # Input/Output specifications
    input_format: str
    output_format: str
    input_validation_rules: List[str] = field(default_factory=list)
    output_validation_rules: List[str] = field(default_factory=list)
    
    # Processing workflow
    processing_steps: List[str] = field(default_factory=list)
    decision_points: List[str] = field(default_factory=list)
    error_handling: Dict[str, str] = field(default_factory=dict)
    
    # Performance expectations
    performance_expectations: Dict[str, Any] = field(default_factory=dict)
    scalability_requirements: Dict[str, Any] = field(default_factory=dict)
    
    # Integration and deployment
    integration_points: List[str] = field(default_factory=list)
    deployment_requirements: List[str] = field(default_factory=list)
    
    # User experience
    user_experience_goals: List[str] = field(default_factory=list)
    accessibility_requirements: List[str] = field(default_factory=list)
    
    # Advanced workflow features
    workflow_automation: List[str] = field(default_factory=list)
    monitoring_and_alerting: List[str] = field(default_factory=list)
    backup_and_recovery: List[str] = field(default_factory=list)
    disaster_recovery: List[str] = field(default_factory=list)


@dataclass
class QualityMetrics:
    """Quality measurement and validation criteria."""
    
    # Accuracy and precision
    accuracy_threshold: float = 0.9
    precision_threshold: float = 0.85
    recall_threshold: float = 0.85
    
    # Completeness and relevance
    completeness_score: float = 0.9
    relevance_score: float = 0.85
    consistency_score: float = 0.9
    
    # Performance metrics
    response_time_threshold: float = 2.0  # seconds
    throughput_requirements: Dict[str, Any] = field(default_factory=dict)
    
    # Validation criteria
    validation_criteria: List[str] = field(default_factory=list)
    acceptance_criteria: List[str] = field(default_factory=list)
    test_scenarios: List[str] = field(default_factory=list)
    
    # Quality dimensions
    quality_dimensions: Dict[str, float] = field(default_factory=dict)
    risk_factors: List[str] = field(default_factory=list)
    
    # Monitoring and feedback
    monitoring_metrics: List[str] = field(default_factory=list)
    feedback_mechanisms: List[str] = field(default_factory=list)
    
    # Advanced quality metrics
    reliability_metrics: Dict[str, float] = field(default_factory=dict)
    maintainability_metrics: Dict[str, float] = field(default_factory=dict)
    security_metrics: Dict[str, float] = field(default_factory=dict)
    compliance_metrics: Dict[str, float] = field(default_factory=dict)


@dataclass
class RequirementsDocument:
    """Complete requirements document with all components."""
    
    requirements_doc: RequirementsDoc
    workflow_expectations: WorkflowExpectations
    quality_metrics: QualityMetrics
    
    # Metadata
    original_prompt: str
    generated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0.0"
    
    # Validation status
    validation_status: Dict[str, bool] = field(default_factory=dict)
    validation_issues: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "requirements_doc": {
                "problem_statement": self.requirements_doc.problem_statement,
                "core_objectives": self.requirements_doc.core_objectives,
                "solution_approach": self.requirements_doc.solution_approach,
                "key_requirements": self.requirements_doc.key_requirements,
                "functional_requirements": self.requirements_doc.functional_requirements,
                "non_functional_requirements": self.requirements_doc.non_functional_requirements,
                "constraints": self.requirements_doc.constraints,
                "assumptions": self.requirements_doc.assumptions,
                "dependencies": self.requirements_doc.dependencies,
                "stakeholders": self.requirements_doc.stakeholders,
                "success_criteria": self.requirements_doc.success_criteria,
                "complexity_level": self.requirements_doc.complexity_level.value,
                "priority_level": self.requirements_doc.priority_level.value,
                "domain": self.requirements_doc.domain,
                "industry": self.requirements_doc.industry,
                "regulatory_requirements": self.requirements_doc.regulatory_requirements,
                "created_at": self.requirements_doc.created_at.isoformat(),
                "version": self.requirements_doc.version,
                "security_requirements": {
                    "authentication_methods": self.requirements_doc.security_requirements.authentication_methods,
                    "authorization_levels": self.requirements_doc.security_requirements.authorization_levels,
                    "data_encryption": self.requirements_doc.security_requirements.data_encryption,
                    "compliance_standards": self.requirements_doc.security_requirements.compliance_standards,
                    "audit_requirements": self.requirements_doc.security_requirements.audit_requirements,
                    "privacy_requirements": self.requirements_doc.security_requirements.privacy_requirements,
                    "security_testing": self.requirements_doc.security_requirements.security_testing
                },
                "technical_specifications": {
                    "architecture_patterns": self.requirements_doc.technical_specifications.architecture_patterns,
                    "technology_stack": self.requirements_doc.technical_specifications.technology_stack,
                    "data_models": self.requirements_doc.technical_specifications.data_models,
                    "api_specifications": self.requirements_doc.technical_specifications.api_specifications,
                    "integration_patterns": self.requirements_doc.technical_specifications.integration_patterns,
                    "deployment_strategy": self.requirements_doc.technical_specifications.deployment_strategy,
                    "scalability_approach": self.requirements_doc.technical_specifications.scalability_approach,
                    "performance_targets": self.requirements_doc.technical_specifications.performance_targets
                },
                "business_requirements": {
                    "business_processes": self.requirements_doc.business_requirements.business_processes,
                    "operational_procedures": self.requirements_doc.business_requirements.operational_procedures,
                    "reporting_requirements": self.requirements_doc.business_requirements.reporting_requirements,
                    "compliance_requirements": self.requirements_doc.business_requirements.compliance_requirements,
                    "risk_mitigation": self.requirements_doc.business_requirements.risk_mitigation,
                    "business_continuity": self.requirements_doc.business_requirements.business_continuity,
                    "change_management": self.requirements_doc.business_requirements.change_management
                },
                "user_experience_requirements": {
                    "user_interface_requirements": self.requirements_doc.user_experience_requirements.user_interface_requirements,
                    "accessibility_standards": self.requirements_doc.user_experience_requirements.accessibility_standards,
                    "usability_goals": self.requirements_doc.user_experience_requirements.usability_goals,
                    "user_journeys": self.requirements_doc.user_experience_requirements.user_journeys,
                    "interaction_patterns": self.requirements_doc.user_experience_requirements.interaction_patterns,
                    "feedback_mechanisms": self.requirements_doc.user_experience_requirements.feedback_mechanisms
                },
                "risk_assessment": {k: v.value for k, v in self.requirements_doc.risk_assessment.items()},
                "compliance_requirements": self.requirements_doc.compliance_requirements,
                "implementation_phases": self.requirements_doc.implementation_phases,
                "acceptance_criteria": self.requirements_doc.acceptance_criteria,
                "testing_requirements": self.requirements_doc.testing_requirements
            },
            "workflow_expectations": {
                "input_format": self.workflow_expectations.input_format,
                "output_format": self.workflow_expectations.output_format,
                "input_validation_rules": self.workflow_expectations.input_validation_rules,
                "output_validation_rules": self.workflow_expectations.output_validation_rules,
                "processing_steps": self.workflow_expectations.processing_steps,
                "decision_points": self.workflow_expectations.decision_points,
                "error_handling": self.workflow_expectations.error_handling,
                "performance_expectations": self.workflow_expectations.performance_expectations,
                "scalability_requirements": self.workflow_expectations.scalability_requirements,
                "integration_points": self.workflow_expectations.integration_points,
                "deployment_requirements": self.workflow_expectations.deployment_requirements,
                "user_experience_goals": self.workflow_expectations.user_experience_goals,
                "accessibility_requirements": self.workflow_expectations.accessibility_requirements,
                "workflow_automation": self.workflow_expectations.workflow_automation,
                "monitoring_and_alerting": self.workflow_expectations.monitoring_and_alerting,
                "backup_and_recovery": self.workflow_expectations.backup_and_recovery,
                "disaster_recovery": self.workflow_expectations.disaster_recovery
            },
            "quality_metrics": {
                "accuracy_threshold": self.quality_metrics.accuracy_threshold,
                "precision_threshold": self.quality_metrics.precision_threshold,
                "recall_threshold": self.quality_metrics.recall_threshold,
                "completeness_score": self.quality_metrics.completeness_score,
                "relevance_score": self.quality_metrics.relevance_score,
                "consistency_score": self.quality_metrics.consistency_score,
                "response_time_threshold": self.quality_metrics.response_time_threshold,
                "throughput_requirements": self.quality_metrics.throughput_requirements,
                "validation_criteria": self.quality_metrics.validation_criteria,
                "acceptance_criteria": self.quality_metrics.acceptance_criteria,
                "test_scenarios": self.quality_metrics.test_scenarios,
                "quality_dimensions": self.quality_metrics.quality_dimensions,
                "risk_factors": self.quality_metrics.risk_factors,
                "monitoring_metrics": self.quality_metrics.monitoring_metrics,
                "feedback_mechanisms": self.quality_metrics.feedback_mechanisms,
                "reliability_metrics": self.quality_metrics.reliability_metrics,
                "maintainability_metrics": self.quality_metrics.maintainability_metrics,
                "security_metrics": self.quality_metrics.security_metrics,
                "compliance_metrics": self.quality_metrics.compliance_metrics
            },
            "metadata": {
                "original_prompt": self.original_prompt,
                "generated_at": self.generated_at.isoformat(),
                "version": self.version,
                "validation_status": self.validation_status,
                "validation_issues": self.validation_issues
            }
        } 