"""
Core Requirements Document Generator.

Orchestrates the multi-agent workflow to generate comprehensive requirements documents
from initial prompts.
"""

from typing import Dict, Any, List, Tuple
import json
from datetime import datetime
import logging
logger = logging.getLogger(__name__)

from .models import RequirementsDocument, RequirementsDoc, WorkflowExpectations, QualityMetrics
from .roles import (
    ProblemAnalyzer, SolutionArchitect, QualityEngineer, RequirementsValidator,
    SecurityArchitect, TechnicalArchitect, BusinessAnalyst, UXDesigner, ImplementationPlanner
)


class RequirementsGenerator:
    """Main orchestrator for requirements document generation."""
    
    def __init__(self, model: str = "gpt-4o", temperature: float = 0.7):
        self.model = model
        self.temperature = temperature
        
        # Initialize roles
        self.roles = {
            "ProblemAnalyzer": ProblemAnalyzer(model=model, temperature=temperature),
            "SolutionArchitect": SolutionArchitect(model=model, temperature=temperature),
            "SecurityArchitect": SecurityArchitect(model=model, temperature=temperature),
            "TechnicalArchitect": TechnicalArchitect(model=model, temperature=temperature),
            "BusinessAnalyst": <PERSON>Analyst(model=model, temperature=temperature),
            "UXDesigner": UX<PERSON><PERSON>er(model=model, temperature=temperature),
            "QualityEngineer": QualityEngineer(model=model, temperature=temperature),
            "ImplementationPlanner": ImplementationPlanner(model=model, temperature=temperature),
            "RequirementsValidator": RequirementsValidator(model=model, temperature=temperature)
        }
        
        # Define workflow sequence
        self.workflow_sequence = [
            "ProblemAnalyzer",
            "SolutionArchitect",
            "SecurityArchitect",
            "TechnicalArchitect", 
            "BusinessAnalyst",
            "UXDesigner",
            "QualityEngineer",
            "ImplementationPlanner",
            "RequirementsValidator"
        ]
    
    def generate(self, initial_prompt: str, max_iterations: int = 3) -> RequirementsDocument:
        """
        Generate a comprehensive requirements document from an initial prompt.
        
        Args:
            initial_prompt: The original prompt to analyze
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            RequirementsDocument: Complete requirements document
        """
        logger.info(f"[REQUIREMENTS GENERATOR] Starting analysis of: {initial_prompt[:100]}...")
        
        # Initialize state
        state = {
            "original_prompt": initial_prompt,
            "iteration": 0,
            "history": []
        }
        
        # Run workflow sequence
        for iteration in range(max_iterations):
            state["iteration"] = iteration + 1
            logger.info(f"[REQUIREMENTS GENERATOR] Iteration {iteration + 1}/{max_iterations}")
            
            # Execute each role in sequence
            for role_name in self.workflow_sequence:
                role = self.roles[role_name]
                logger.info(f"[REQUIREMENTS GENERATOR] Running {role_name}...")
                
                try:
                    output = role.eval(state)
                    
                    # Update state with role output
                    if output.requirements_doc:
                        state["requirements_doc"] = output.requirements_doc
                    if output.workflow_expectations:
                        state["workflow_expectations"] = output.workflow_expectations
                    if output.quality_metrics:
                        state["quality_metrics"] = output.quality_metrics
                    if output.analysis:
                        state[f"{role_name.lower()}_analysis"] = output.analysis
                    
                    # Log the execution
                    state["history"].append({
                        "iteration": iteration + 1,
                        "role": role_name,
                        "log": output.log,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    logger.info(f"[REQUIREMENTS GENERATOR] {role_name} completed: {output.log}")
                    
                except Exception as e:
                    logger.error(f"[REQUIREMENTS GENERATOR] Error in {role_name}: {str(e)}")
                    state["history"].append({
                        "iteration": iteration + 1,
                        "role": role_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
        
        # Create final requirements document
        requirements_doc = state.get("requirements_doc")
        workflow_expectations = state.get("workflow_expectations")
        quality_metrics = state.get("quality_metrics")
        
        if not requirements_doc:
            raise RuntimeError("Failed to generate requirements document - ProblemAnalyzer did not complete successfully.")
        
        # Create validation status from validator analysis
        validation_analysis = state.get("requirementsvalidator_analysis", {})
        validation_status = validation_analysis.get("validation_status", {})
        validation_issues = validation_analysis.get("validation_issues", [])
        
        # Create final document
        final_document = RequirementsDocument(
            requirements_doc=requirements_doc,
            workflow_expectations=workflow_expectations or self._create_default_workflow(),
            quality_metrics=quality_metrics or self._create_default_quality_metrics(),
            original_prompt=initial_prompt,
            validation_status=validation_status,
            validation_issues=validation_issues
        )
        
        logger.info(f"[REQUIREMENTS GENERATOR] Generation completed successfully!")
        return final_document
    
    def _create_default_workflow(self) -> WorkflowExpectations:
        """Create default workflow expectations if none were generated."""
        return WorkflowExpectations(
            input_format="Text input",
            output_format="Structured output",
            processing_steps=["Process input", "Generate output"],
            error_handling={"general": "Graceful degradation"},
            performance_expectations={"response_time": "2 seconds"},
            integration_points=["API integration"]
        )
    
    def _create_default_quality_metrics(self) -> QualityMetrics:
        """Create default quality metrics if none were generated."""
        return QualityMetrics(
            accuracy_threshold=0.9,
            precision_threshold=0.85,
            recall_threshold=0.85,
            completeness_score=0.9,
            relevance_score=0.85,
            consistency_score=0.9,
            response_time_threshold=2.0,
            validation_criteria=["Output meets requirements", "Response time acceptable"],
            acceptance_criteria=["All requirements satisfied", "Quality thresholds met"]
        )
    
    def generate_json(self, initial_prompt: str, max_iterations: int = 3) -> Dict[str, Any]:
        """
        Generate requirements document and return as JSON.
        
        Args:
            initial_prompt: The original prompt to analyze
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            Dict: JSON representation of the requirements document
        """
        document = self.generate(initial_prompt, max_iterations)
        return document.to_dict()
    
    def generate_markdown(self, initial_prompt: str, max_iterations: int = 3) -> str:
        """
        Generate requirements document and return as human-readable markdown.
        
        Args:
            initial_prompt: The original prompt to analyze
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            str: Markdown representation of the requirements document
        """
        document = self.generate(initial_prompt, max_iterations)
        return self._document_to_markdown(document)
    
    def _document_to_markdown(self, document: RequirementsDocument) -> str:
        """Convert requirements document to markdown format."""
        md = []
        
        # Header
        md.append("# Requirements Document")
        md.append(f"*Generated from: {document.original_prompt}*")
        md.append(f"*Generated at: {document.generated_at.strftime('%Y-%m-%d %H:%M:%S')}*")
        md.append("")
        
        # Requirements Document Section
        req_doc = document.requirements_doc
        md.append("## Requirements Document")
        md.append("")
        
        md.append("### Problem Statement")
        md.append(req_doc.problem_statement)
        md.append("")
        
        md.append("### Core Objectives")
        for i, objective in enumerate(req_doc.core_objectives, 1):
            md.append(f"{i}. {objective}")
        md.append("")
        
        md.append("### Solution Approach")
        md.append(req_doc.solution_approach)
        md.append("")
        
        md.append("### Key Requirements")
        for i, requirement in enumerate(req_doc.key_requirements, 1):
            md.append(f"{i}. {requirement}")
        md.append("")
        
        if req_doc.stakeholders:
            md.append("### Stakeholders")
            for stakeholder in req_doc.stakeholders:
                md.append(f"- {stakeholder}")
            md.append("")
        
        if req_doc.success_criteria:
            md.append("### Success Criteria")
            for i, criterion in enumerate(req_doc.success_criteria, 1):
                md.append(f"{i}. {criterion}")
            md.append("")
        
        md.append("### Metadata")
        md.append(f"- **Domain**: {req_doc.domain or 'Not specified'}")
        md.append(f"- **Industry**: {req_doc.industry or 'Not specified'}")
        md.append(f"- **Complexity Level**: {req_doc.complexity_level.value}")
        md.append(f"- **Priority Level**: {req_doc.priority_level.value}")
        md.append("")
        
        # Workflow Expectations Section
        workflow = document.workflow_expectations
        md.append("## Workflow Expectations")
        md.append("")
        
        md.append("### Input/Output Specifications")
        md.append(f"**Input Format**: {workflow.input_format}")
        md.append(f"**Output Format**: {workflow.output_format}")
        md.append("")
        
        if workflow.processing_steps:
            md.append("### Processing Steps")
            for i, step in enumerate(workflow.processing_steps, 1):
                md.append(f"{i}. {step}")
            md.append("")
        
        if workflow.error_handling:
            md.append("### Error Handling")
            for error_type, handling in workflow.error_handling.items():
                md.append(f"- **{error_type}**: {handling}")
            md.append("")
        
        if workflow.performance_expectations:
            md.append("### Performance Expectations")
            for metric, value in workflow.performance_expectations.items():
                md.append(f"- **{metric}**: {value}")
            md.append("")
        
        # Quality Metrics Section
        quality = document.quality_metrics
        md.append("## Quality Metrics")
        md.append("")
        
        md.append("### Accuracy and Precision")
        md.append(f"- **Accuracy Threshold**: {quality.accuracy_threshold}")
        md.append(f"- **Precision Threshold**: {quality.precision_threshold}")
        md.append(f"- **Recall Threshold**: {quality.recall_threshold}")
        md.append("")
        
        md.append("### Completeness and Relevance")
        md.append(f"- **Completeness Score**: {quality.completeness_score}")
        md.append(f"- **Relevance Score**: {quality.relevance_score}")
        md.append(f"- **Consistency Score**: {quality.consistency_score}")
        md.append("")
        
        md.append("### Performance Metrics")
        md.append(f"- **Response Time Threshold**: {quality.response_time_threshold} seconds")
        md.append("")
        
        if quality.validation_criteria:
            md.append("### Validation Criteria")
            for i, criterion in enumerate(quality.validation_criteria, 1):
                md.append(f"{i}. {criterion}")
            md.append("")
        
        if quality.acceptance_criteria:
            md.append("### Acceptance Criteria")
            for i, criterion in enumerate(quality.acceptance_criteria, 1):
                md.append(f"{i}. {criterion}")
            md.append("")
        
        if quality.risk_factors:
            md.append("### Risk Factors")
            for risk in quality.risk_factors:
                md.append(f"- {risk}")
            md.append("")
        
        # Enhanced Requirements Sections
        md.append("## Enhanced Requirements")
        md.append("")
        
        # Security Requirements
        if req_doc.security_requirements:
            security = req_doc.security_requirements
            md.append("### Security Requirements")
            md.append("")
            
            if security.authentication_methods:
                md.append("#### Authentication Methods")
                for method in security.authentication_methods:
                    md.append(f"- {method}")
                md.append("")
            
            if security.authorization_levels:
                md.append("#### Authorization Levels")
                for level in security.authorization_levels:
                    md.append(f"- {level}")
                md.append("")
            
            if security.data_encryption:
                md.append("#### Data Encryption")
                for encryption in security.data_encryption:
                    md.append(f"- {encryption}")
                md.append("")
            
            if security.compliance_standards:
                md.append("#### Compliance Standards")
                for standard in security.compliance_standards:
                    md.append(f"- {standard}")
                md.append("")
            
            if security.audit_requirements:
                md.append("#### Audit Requirements")
                for audit in security.audit_requirements:
                    md.append(f"- {audit}")
                md.append("")
            
            if security.privacy_requirements:
                md.append("#### Privacy Requirements")
                for privacy in security.privacy_requirements:
                    md.append(f"- {privacy}")
                md.append("")
            
            if security.security_testing:
                md.append("#### Security Testing")
                for test in security.security_testing:
                    md.append(f"- {test}")
                md.append("")
        
        # Technical Specifications
        if req_doc.technical_specifications:
            tech = req_doc.technical_specifications
            md.append("### Technical Specifications")
            md.append("")
            
            if tech.architecture_patterns:
                md.append("#### Architecture Patterns")
                for pattern in tech.architecture_patterns:
                    md.append(f"- {pattern}")
                md.append("")
            
            if tech.technology_stack:
                md.append("#### Technology Stack")
                for tech_item in tech.technology_stack:
                    md.append(f"- {tech_item}")
                md.append("")
            
            if tech.data_models:
                md.append("#### Data Models")
                for model in tech.data_models:
                    md.append(f"- {model}")
                md.append("")
            
            if tech.api_specifications:
                md.append("#### API Specifications")
                for api in tech.api_specifications:
                    md.append(f"- {api}")
                md.append("")
            
            if tech.integration_patterns:
                md.append("#### Integration Patterns")
                for pattern in tech.integration_patterns:
                    md.append(f"- {pattern}")
                md.append("")
            
            if tech.deployment_strategy:
                md.append("#### Deployment Strategy")
                md.append(tech.deployment_strategy)
                md.append("")
            
            if tech.scalability_approach:
                md.append("#### Scalability Approach")
                md.append(tech.scalability_approach)
                md.append("")
            
            if tech.performance_targets:
                md.append("#### Performance Targets")
                for target, value in tech.performance_targets.items():
                    md.append(f"- **{target}**: {value}")
                md.append("")
        
        # Business Requirements
        if req_doc.business_requirements:
            business = req_doc.business_requirements
            md.append("### Business Requirements")
            md.append("")
            
            if business.business_processes:
                md.append("#### Business Processes")
                for process in business.business_processes:
                    md.append(f"- {process}")
                md.append("")
            
            if business.operational_procedures:
                md.append("#### Operational Procedures")
                for procedure in business.operational_procedures:
                    md.append(f"- {procedure}")
                md.append("")
            
            if business.reporting_requirements:
                md.append("#### Reporting Requirements")
                for report in business.reporting_requirements:
                    md.append(f"- {report}")
                md.append("")
            
            if business.compliance_requirements:
                md.append("#### Compliance Requirements")
                for compliance in business.compliance_requirements:
                    md.append(f"- {compliance}")
                md.append("")
            
            if business.risk_mitigation:
                md.append("#### Risk Mitigation")
                for risk in business.risk_mitigation:
                    md.append(f"- {risk}")
                md.append("")
            
            if business.business_continuity:
                md.append("#### Business Continuity")
                for continuity in business.business_continuity:
                    md.append(f"- {continuity}")
                md.append("")
            
            if business.change_management:
                md.append("#### Change Management")
                for change in business.change_management:
                    md.append(f"- {change}")
                md.append("")
        
        # User Experience Requirements
        if req_doc.user_experience_requirements:
            ux = req_doc.user_experience_requirements
            md.append("### User Experience Requirements")
            md.append("")
            
            if ux.user_interface_requirements:
                md.append("#### User Interface Requirements")
                for ui_req in ux.user_interface_requirements:
                    md.append(f"- {ui_req}")
                md.append("")
            
            if ux.accessibility_standards:
                md.append("#### Accessibility Standards")
                for standard in ux.accessibility_standards:
                    md.append(f"- {standard}")
                md.append("")
            
            if ux.usability_goals:
                md.append("#### Usability Goals")
                for goal in ux.usability_goals:
                    md.append(f"- {goal}")
                md.append("")
            
            if ux.user_journeys:
                md.append("#### User Journeys")
                for journey in ux.user_journeys:
                    md.append(f"- {journey}")
                md.append("")
            
            if ux.interaction_patterns:
                md.append("#### Interaction Patterns")
                for pattern in ux.interaction_patterns:
                    md.append(f"- {pattern}")
                md.append("")
            
            if ux.feedback_mechanisms:
                md.append("#### Feedback Mechanisms")
                for mechanism in ux.feedback_mechanisms:
                    md.append(f"- {mechanism}")
                md.append("")
        
        # Implementation Details
        if req_doc.implementation_phases:
            md.append("### Implementation Phases")
            for i, phase in enumerate(req_doc.implementation_phases, 1):
                md.append(f"{i}. {phase}")
            md.append("")
        
        if req_doc.acceptance_criteria:
            md.append("### Acceptance Criteria")
            for i, criterion in enumerate(req_doc.acceptance_criteria, 1):
                md.append(f"{i}. {criterion}")
            md.append("")
        
        if req_doc.testing_requirements:
            md.append("### Testing Requirements")
            for i, test in enumerate(req_doc.testing_requirements, 1):
                md.append(f"{i}. {test}")
            md.append("")
        
        # Risk Assessment
        if req_doc.risk_assessment:
            md.append("### Risk Assessment")
            for risk_type, level in req_doc.risk_assessment.items():
                md.append(f"- **{risk_type.replace('_', ' ').title()}**: {level.value}")
            md.append("")
        
        # Compliance Requirements
        if req_doc.compliance_requirements:
            md.append("### Compliance Requirements")
            for compliance in req_doc.compliance_requirements:
                md.append(f"- {compliance}")
            md.append("")
        
        # Enhanced Workflow Features
        if workflow.workflow_automation:
            md.append("### Workflow Automation")
            for automation in workflow.workflow_automation:
                md.append(f"- {automation}")
            md.append("")
        
        if workflow.monitoring_and_alerting:
            md.append("### Monitoring and Alerting")
            for monitoring in workflow.monitoring_and_alerting:
                md.append(f"- {monitoring}")
            md.append("")
        
        if workflow.backup_and_recovery:
            md.append("### Backup and Recovery")
            for backup in workflow.backup_and_recovery:
                md.append(f"- {backup}")
            md.append("")
        
        if workflow.disaster_recovery:
            md.append("### Disaster Recovery")
            for recovery in workflow.disaster_recovery:
                md.append(f"- {recovery}")
            md.append("")
        
        # Enhanced Quality Metrics
        if quality.reliability_metrics:
            md.append("### Reliability Metrics")
            for metric, value in quality.reliability_metrics.items():
                md.append(f"- **{metric}**: {value}")
            md.append("")
        
        if quality.maintainability_metrics:
            md.append("### Maintainability Metrics")
            for metric, value in quality.maintainability_metrics.items():
                md.append(f"- **{metric}**: {value}")
            md.append("")
        
        if quality.security_metrics:
            md.append("### Security Metrics")
            for metric, value in quality.security_metrics.items():
                md.append(f"- **{metric}**: {value}")
            md.append("")
        
        if quality.compliance_metrics:
            md.append("### Compliance Metrics")
            for metric, value in quality.compliance_metrics.items():
                md.append(f"- **{metric}**: {value}")
            md.append("")
        
        # Validation Status
        if document.validation_status:
            md.append("## Validation Status")
            md.append("")
            for aspect, status in document.validation_status.items():
                status_icon = "✅" if status else "❌"
                md.append(f"- **{aspect.title()}**: {status_icon}")
            md.append("")
        
        if document.validation_issues:
            md.append("### Validation Issues")
            for issue in document.validation_issues:
                md.append(f"- {issue}")
            md.append("")
        
        return "\n".join(md) 