#!/usr/bin/env python3
"""
Detailed field-by-field comparison between SPA template and mapped data
"""

import json
from typing import Dict, Any, List


def load_spa_template() -> Dict[str, Any]:
    """Load the SPA template structure from the JS file"""
    return {
        "id": "task-001",
        "name": "User Authentication Flow Validation",
        "context": "prod - AWS us-east-1",
        "systemPrompt": "Validate all authentication endpoints including login, logout, password reset, and session management. Ensure compliance with security standards and performance requirements.",
        "testScenarios": [
            {
                "id": "test-001",
                "description": "Calculate compound interest correctly",
                "status": "pass",
                "output": "$1,628.89",
                "input": {"principal": 1000, "rate": 0.05, "time": 10, "compound": "monthly"},
                "graderScores": [
                    {"criterion": "Accuracy", "score": 100, "status": "pass"},
                    {"criterion": "Performance", "score": 95, "status": "pass"},
                    {"criterion": "Edge Cases", "score": 90, "status": "pass"}
                ]
            }
        ],
        "aggregateScores": [
            {"category": "Authentication", "passRate": 95},
            {"category": "Data Validation", "passRate": 88}
        ]
    }


def compare_structures(expected: Dict[str, Any], actual: Dict[str, Any], path: str = "") -> List[Dict[str, Any]]:
    """Deep comparison of data structures"""
    differences = []
    
    # Field mapping analysis
    field_mappings = {
        # Expected SPA field -> Actual mapped field
        "id": {
            "spa_format": "task-XXX",
            "actual_field": "id",
            "actual_value": actual.get("id", ""),
            "matches_format": actual.get("id", "").startswith("task-")
        },
        "name": {
            "spa_example": "User Authentication Flow Validation",
            "actual_field": "name",
            "actual_value": actual.get("name", "")[:50] + "...",
            "value_type": "truncated prompt"
        },
        "context": {
            "spa_example": "prod - AWS us-east-1",
            "actual_field": "context",
            "actual_value": actual.get("context", ""),
            "value_type": "domain - tone"
        },
        "systemPrompt": {
            "spa_example": "Validate all authentication endpoints...",
            "actual_field": "systemPrompt",
            "actual_value": actual.get("systemPrompt", "")[:50] + "...",
            "source": "likely from prompt generation step"
        }
    }
    
    # Test scenario field mapping
    if "testScenarios" in actual and actual["testScenarios"]:
        scenario = actual["testScenarios"][0]
        scenario_mapping = {
            "id": {
                "spa_format": "test-XXX",
                "actual_format": scenario.get("id", ""),
                "uses_seed_id": scenario.get("id", "").startswith("seed_")
            },
            "description": {
                "spa_type": "brief test action",
                "actual_type": "detailed test reasoning",
                "actual_value": scenario.get("description", "")[:50] + "..."
            },
            "status": {
                "spa_values": ["pass", "fail"],
                "actual_value": scenario.get("status", ""),
                "valid": scenario.get("status", "") in ["pass", "fail"]
            },
            "output": {
                "spa_type": "specific result value",
                "actual_type": "general output description",
                "actual_value": scenario.get("output", "")[:50] + "..."
            },
            "input": {
                "spa_type": "specific parameter dict",
                "actual_type": "placeholder dict",
                "actual_keys": list(scenario.get("input", {}).keys())
            }
        }
        
        # Grader scores mapping
        if "graderScores" in scenario and scenario["graderScores"]:
            grader_mapping = {
                "criteria_mapping": {
                    "spa_examples": ["Accuracy", "Performance", "Edge Cases"],
                    "actual_criteria": [g.get("criterion") for g in scenario["graderScores"]],
                    "pattern": "Different criteria set used"
                },
                "score_range": {
                    "expected": "0-100",
                    "actual_scores": [g.get("score") for g in scenario["graderScores"]],
                    "all_valid": all(0 <= g.get("score", 0) <= 100 for g in scenario["graderScores"])
                },
                "status_values": {
                    "expected": ["pass", "fail", "warn"],
                    "actual_statuses": list(set(g.get("status") for g in scenario["graderScores"])),
                    "all_valid": all(g.get("status") in ["pass", "fail", "warn"] for g in scenario["graderScores"])
                }
            }
            differences.append({"type": "grader_scores_mapping", "details": grader_mapping})
        
        differences.append({"type": "scenario_field_mapping", "details": scenario_mapping})
    
    differences.append({"type": "top_level_field_mapping", "details": field_mappings})
    
    # Aggregate scores mapping
    if "aggregateScores" in actual:
        categories_spa = ["Authentication", "Data Validation", "Performance", "Security", "API Endpoints", 
                          "Error Handling", "Integration", "UI Components", "Database", "Caching", 
                          "Logging", "Monitoring"]
        categories_actual = [s.get("category") for s in actual["aggregateScores"]]
        
        aggregate_mapping = {
            "spa_categories": categories_spa[:5] + ["..."],
            "actual_categories": categories_actual[:5] + ["..."] if len(categories_actual) > 5 else categories_actual,
            "category_pattern": "Different domain-specific categories",
            "duplicates_found": len(categories_actual) != len(set(categories_actual))
        }
        differences.append({"type": "aggregate_scores_mapping", "details": aggregate_mapping})
    
    return differences


def analyze_data_transformations(source_data: Dict[str, Any], mapped_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Analyze how data was transformed from source to mapped format"""
    transformations = []
    
    # ID generation
    if "id" in mapped_data:
        transformations.append({
            "field": "id",
            "transformation": "Generated timestamp-based ID",
            "pattern": "task-{timestamp}",
            "example": mapped_data["id"]
        })
    
    # Name transformation
    if "metadata" in source_data and "initial_prompt" in source_data["metadata"]:
        source_prompt = source_data["metadata"]["initial_prompt"]
        mapped_name = mapped_data.get("name", "")
        
        transformations.append({
            "field": "name",
            "source": "metadata.initial_prompt",
            "transformation": "Truncated to first ~50 chars",
            "source_length": len(source_prompt),
            "mapped_length": len(mapped_name),
            "ends_with_ellipsis": mapped_name.endswith("...")
        })
    
    # Context transformation
    transformations.append({
        "field": "context",
        "likely_source": "prompt metadata domain and tone",
        "transformation": "Concatenated domain - tone",
        "value": mapped_data.get("context", "")
    })
    
    # System prompt
    transformations.append({
        "field": "systemPrompt",
        "likely_source": "step_2_prompt.output.system_message",
        "transformation": "Extracted from prompt generation",
        "contains_role": "Prompt-Architect-PE-v1" in mapped_data.get("systemPrompt", "")
    })
    
    # Test scenarios transformation
    if "step_3_synthetic_data" in source_data and "seeds" in source_data["step_3_synthetic_data"]:
        source_seeds = source_data["step_3_synthetic_data"]["seeds"]
        mapped_scenarios = mapped_data.get("testScenarios", [])
        
        transformations.append({
            "field": "testScenarios",
            "source": "step_3_synthetic_data.seeds",
            "transformation": "Mapped seeds to test scenarios",
            "id_pattern": "seed_{index}",
            "description_source": "seed.reasoning",
            "output_source": "seed.expected_output (truncated)",
            "input_transformation": "Placeholder variables from prompt",
            "status_assignment": "Alternating pass/fail pattern or random"
        })
    
    # Grader scores
    transformations.append({
        "field": "graderScores",
        "source": "Not in source data",
        "transformation": "Generated synthetic scores",
        "criteria": ["Complexity Handling", "Relevance", "Uniqueness"],
        "score_generation": "All scores 80-100 range",
        "status_logic": "All 'pass' when score >= 80"
    })
    
    # Aggregate scores
    transformations.append({
        "field": "aggregateScores",
        "source": "Not directly in source",
        "transformation": "Generated categories with scores",
        "categories_source": "Likely from requirements analysis",
        "score_generation": "Mix of 0%, 20%, 67%, 90-100% values",
        "duplicates": "Yes - Completeness and Consistency appear twice"
    })
    
    return transformations


def generate_detailed_report(mapped_file: str, source_file: str) -> str:
    """Generate comprehensive field-by-field comparison report"""
    with open(mapped_file, 'r') as f:
        mapped_data = json.load(f)
    
    with open(source_file, 'r') as f:
        source_data = json.load(f)
    
    spa_template = load_spa_template()
    
    report = []
    report.append("# Detailed Field-by-Field Comparison Report")
    report.append("")
    
    # Structure comparison
    report.append("## Structure Comparison (SPA Template vs Mapped Data)")
    differences = compare_structures(spa_template, mapped_data)
    
    for diff in differences:
        report.append(f"\n### {diff['type'].replace('_', ' ').title()}")
        details = diff['details']
        if isinstance(details, dict):
            for key, value in details.items():
                report.append(f"- **{key}**: {json.dumps(value, indent=2)}")
    
    # Data transformations
    report.append("\n## Data Transformation Analysis")
    transformations = analyze_data_transformations(source_data, mapped_data)
    
    for transform in transformations:
        report.append(f"\n### {transform['field']}")
        for key, value in transform.items():
            if key != 'field':
                report.append(f"- **{key}**: {value}")
    
    # Specific mismatches
    report.append("\n## Key Mismatches and Issues")
    
    # 1. ID format
    report.append("\n### 1. ID Format Mismatch")
    report.append(f"- SPA expects: 'task-001', 'test-001' format")
    report.append(f"- Mapped uses: '{mapped_data.get('id')}' (timestamp-based)")
    report.append(f"- Test IDs use: 'seed_1', 'seed_2' format instead of 'test-XXX'")
    
    # 2. Test scenario structure
    report.append("\n### 2. Test Scenario Structure Differences")
    if mapped_data.get("testScenarios"):
        scenario = mapped_data["testScenarios"][0]
        report.append("- **Description field**:")
        report.append("  - SPA: Brief action description (e.g., 'Calculate compound interest correctly')")
        report.append(f"  - Mapped: Long reasoning text: '{scenario.get('description', '')[:60]}...'")
        report.append("- **Output field**:")
        report.append("  - SPA: Specific result (e.g., '$1,628.89')")
        report.append(f"  - Mapped: General description: '{scenario.get('output', '')[:60]}...'")
        report.append("- **Input field**:")
        report.append("  - SPA: Concrete values (e.g., {'principal': 1000, 'rate': 0.05})")
        report.append(f"  - Mapped: Template placeholders: {list(scenario.get('input', {}).keys())}")
    
    # 3. Aggregate scores
    report.append("\n### 3. Aggregate Scores Issues")
    report.append("- Duplicate categories found: 'Completeness' (90%, 0%), 'Consistency' (93%, 100%)")
    report.append("- Mix of testing-related and business categories")
    report.append("- Some categories have 0% pass rate which seems unrealistic")
    
    # 4. Missing SPA features
    report.append("\n### 4. Missing SPA Features")
    report.append("- No hoverable tooltips data")
    report.append("- No additional metadata like duration, memory usage")
    report.append("- No timestamp or execution history")
    
    # Recommendations
    report.append("\n## Recommendations for Fixing the Mapping")
    report.append("\n1. **Fix ID formats**:")
    report.append("   - Change task ID to format 'task-001' or keep timestamp")
    report.append("   - Change test scenario IDs from 'seed_X' to 'test-00X'")
    
    report.append("\n2. **Transform test scenarios**:")
    report.append("   - Shorten descriptions to action-based phrases")
    report.append("   - Convert output to specific values instead of descriptions")
    report.append("   - Replace placeholder inputs with example concrete values")
    
    report.append("\n3. **Fix aggregate scores**:")
    report.append("   - Remove duplicate categories")
    report.append("   - Ensure all categories have realistic pass rates")
    report.append("   - Consider using domain-specific categories")
    
    report.append("\n4. **Add missing fields**:")
    report.append("   - Add execution metadata to scenarios")
    report.append("   - Consider adding hover tooltip data")
    
    return "\n".join(report)


def main():
    report = generate_detailed_report(
        "/Users/<USER>/Code3b/Github/pfc/af_2/mapped_task_card.json",
        "/Users/<USER>/Code3b/Github/pfc/af_2/pipeline_output.json"
    )
    
    report_path = "/Users/<USER>/Code3b/Github/pfc/af_2/.tmp/detailed_field_comparison.md"
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"Detailed field comparison report generated: {report_path}")


if __name__ == "__main__":
    main()