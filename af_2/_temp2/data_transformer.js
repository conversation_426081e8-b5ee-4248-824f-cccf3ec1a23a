// Data Transformer: step_combined.json → SPA Format
// Converts pipeline execution data to task card visualization format

class StepDataTransformer {
  
  constructor(stepCombinedData) {
    this.data = stepCombinedData;
    this.sections = stepCombinedData.step_by_step.sections;
  }

  // Main transformation method
  transform() {
    return {
      id: this.generateTaskId(),
      name: this.extractTaskName(),
      context: this.extractContext(),
      systemPrompt: this.extractSystemPrompt(),
      testScenarios: this.extractTestScenarios(),
      aggregateScores: this.calculateAggregateScores()
    };
  }

  // Generate unique task ID from timestamp
  generateTaskId() {
    const timestamp = this.sections[0]?.timestamp || Date.now();
    return `task-${Math.floor(timestamp)}`;
  }

  // Extract task name from initial input
  extractTaskName() {
    const initialInput = this.sections.find(s => s.name === 'INITIAL INPUT');
    if (initialInput) {
      const input = initialInput.data;
      // Extract key phrase from input
      if (input.includes('Industry 101')) {
        return 'Industry 101 Document Generator';
      }
      return input.substring(0, 50) + '...';
    }
    return 'Unknown Task';
  }

  // Extract context from requirements or domain info
  extractContext() {
    const reqSection = this.sections.find(s => s.name.includes('REQUIREMENTS'));
    if (reqSection?.data?.requirements_doc) {
      const domain = reqSection.data.requirements_doc.domain;
      const industry = reqSection.data.requirements_doc.industry;
      return `${domain} - ${industry}`;
    }
    return 'General - Multi-domain';
  }

  // Extract system prompt from any step
  extractSystemPrompt() {
    for (const section of this.sections) {
      if (section.data?.generated_prompt?.system_message) {
        return section.data.generated_prompt.system_message;
      }
      if (section.data?.feedback?.system_message) {
        return section.data.feedback.system_message;
      }
    }
    return 'System prompt not found';
  }

  // Collect and transform all test results
  extractTestScenarios() {
    const allTests = [];
    
    // Collect test results from all sections
    for (const section of this.sections) {
      const testResults = this.findTestResults(section);
      allTests.push(...testResults);
    }

    // Transform to SPA format
    return allTests.map((test, index) => ({
      id: test.seed_id || `test-${index + 1}`,
      description: this.extractTestDescription(test.user_message),
      status: test.comparison_result?.match ? 'pass' : 'fail',
      output: this.truncateOutput(test.actual_output),
      input: this.extractInputParams(test.user_message),
      graderScores: this.mapComparisonToGraderScores(test.comparison_result)
    }));
  }

  // Find test results in a section
  findTestResults(section) {
    const results = [];
    
    if (section.data?.feedback?.test_results) {
      results.push(...section.data.feedback.test_results);
    }
    if (section.data?.test_results) {
      results.push(...section.data.test_results);
    }
    
    return results;
  }

  // Extract test description from user message
  extractTestDescription(userMessage) {
    if (!userMessage) return 'Unknown test';
    
    // Extract industry and region if present
    const industryMatch = userMessage.match(/analysis of the (\w+)/i);
    const regionMatch = userMessage.match(/in the ([^over]+) over/i);
    const companiesMatch = userMessage.match(/top (\d+) companies/i);
    
    if (industryMatch && regionMatch && companiesMatch) {
      return `Analyze ${industryMatch[1]} industry in ${regionMatch[1].trim()} - ${companiesMatch[1]} companies`;
    }
    
    return userMessage.substring(0, 60) + '...';
  }

  // Extract input parameters from user message
  extractInputParams(userMessage) {
    if (!userMessage) return {};
    
    const params = {};
    
    // Extract common parameters
    const industryMatch = userMessage.match(/analysis of the (\w+)/i);
    const regionMatch = userMessage.match(/in the ([^over]+) over/i);
    const timeMatch = userMessage.match(/over the ([^.]+)\./i);
    const companiesMatch = userMessage.match(/top (\d+) companies/i);
    
    if (industryMatch) params.industry = industryMatch[1];
    if (regionMatch) params.region = regionMatch[1].trim();
    if (timeMatch) params.timePeriod = timeMatch[1].trim();
    if (companiesMatch) params.numCompanies = parseInt(companiesMatch[1]);
    
    return params;
  }

  // Truncate output for display
  truncateOutput(output) {
    if (!output) return 'No output';
    if (output.length <= 100) return output;
    return output.substring(0, 97) + '...';
  }

  // Map comparison result to grader scores
  mapComparisonToGraderScores(comparisonResult) {
    if (!comparisonResult) {
      return [{ criterion: 'Unknown', score: 0, status: 'fail' }];
    }

    const scores = [];

    // Content Match Score
    if (comparisonResult.similarity_score !== undefined) {
      const score = Math.round(comparisonResult.similarity_score * 100);
      scores.push({
        criterion: 'Content Match',
        score: score,
        status: score > 80 ? 'pass' : score > 50 ? 'warn' : 'fail'
      });
    }

    // Confidence Score
    if (comparisonResult.confidence_level) {
      const confidenceMap = { high: 90, medium: 70, low: 40 };
      const score = confidenceMap[comparisonResult.confidence_level] || 50;
      scores.push({
        criterion: 'Confidence',
        score: score,
        status: score > 80 ? 'pass' : score > 60 ? 'warn' : 'fail'
      });
    }

    // Completeness Score
    if (comparisonResult.missing_elements) {
      const missingCount = comparisonResult.missing_elements.length;
      const score = Math.max(0, 100 - (missingCount * 20));
      scores.push({
        criterion: 'Completeness',
        score: score,
        status: missingCount === 0 ? 'pass' : missingCount <= 2 ? 'warn' : 'fail'
      });
    }

    // Accuracy Score (based on key similarities)
    if (comparisonResult.key_similarities) {
      const similarityCount = comparisonResult.key_similarities.length;
      const score = Math.min(100, similarityCount * 20);
      scores.push({
        criterion: 'Accuracy',
        score: score,
        status: similarityCount >= 4 ? 'pass' : similarityCount >= 2 ? 'warn' : 'fail'
      });
    }

    return scores.length > 0 ? scores : [{ criterion: 'Unknown', score: 0, status: 'fail' }];
  }

  // Calculate aggregate scores by category
  calculateAggregateScores() {
    const testScenarios = this.extractTestScenarios();
    
    if (testScenarios.length === 0) {
      return [{ category: 'No Tests', passRate: 0 }];
    }

    // Calculate overall pass rate
    const passCount = testScenarios.filter(t => t.status === 'pass').length;
    const overallPassRate = Math.round((passCount / testScenarios.length) * 100);

    // Group by different criteria
    const categories = [
      { category: 'Overall', passRate: overallPassRate },
      { category: 'Content Quality', passRate: this.calculateCriterionPassRate(testScenarios, 'Content Match') },
      { category: 'Completeness', passRate: this.calculateCriterionPassRate(testScenarios, 'Completeness') },
      { category: 'Accuracy', passRate: this.calculateCriterionPassRate(testScenarios, 'Accuracy') },
      { category: 'Confidence', passRate: this.calculateCriterionPassRate(testScenarios, 'Confidence') }
    ];

    return categories;
  }

  // Calculate pass rate for specific criterion
  calculateCriterionPassRate(testScenarios, criterion) {
    let totalScores = 0;
    let count = 0;

    for (const test of testScenarios) {
      const graderScore = test.graderScores.find(g => g.criterion === criterion);
      if (graderScore) {
        totalScores += graderScore.score;
        count++;
      }
    }

    return count > 0 ? Math.round(totalScores / count) : 0;
  }
}

// Usage example:
// const transformer = new StepDataTransformer(stepCombinedData);
// const spaData = transformer.transform();

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StepDataTransformer;
}
