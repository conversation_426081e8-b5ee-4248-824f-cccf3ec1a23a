# Integration Guide: Connecting step_combined.json to SPA

## Overview
This guide explains how to integrate the data transformation pipeline with the existing SPA to display real task execution data instead of dummy data.

## Files Created
1. `data_mapping_analysis.md` - Detailed analysis of data structure mapping
2. `data_transformer.js` - Core transformation logic
3. `test_transformation.js` - Test script to verify transformation
4. `integration_guide.md` - This integration guide

## Integration Steps

### Step 1: Test the Transformation
```bash
cd af_2/_temp2
node test_transformation.js
```
This will:
- Load step_combined.json
- Transform it to SPA format
- Validate the output
- Save transformed data to `transformed_spa_data.json`

### Step 2: Modify the SPA to Use Real Data

Replace the dummy data in `af_ui/af_chat_spa.js`:

```javascript
// BEFORE (lines 4-87):
const dummyTaskData = { ... };

// AFTER:
// Import the transformer
import StepDataTransformer from './data_transformer.js';

// Load and transform real data
async function loadRealTaskData() {
  try {
    const response = await fetch('./step_combined.json');
    const stepData = await response.json();
    const transformer = new StepDataTransformer(stepData);
    return transformer.transform();
  } catch (error) {
    console.error('Failed to load real data, using dummy data:', error);
    return dummyTaskData; // Fallback to dummy data
  }
}

// Use real data in component
const [taskData, setTaskData] = useState(null);

useEffect(() => {
  loadRealTaskData().then(setTaskData);
}, []);

// Update component to handle loading state
if (!taskData) {
  return <div>Loading task data...</div>;
}
```

### Step 3: Update File Structure
```
af_ui/
├── index.html
├── af_chat_spa.js (modified to use real data)
├── data_transformer.js (copied from _temp2)
├── step_combined.json (copied from root)
└── serve.py
```

### Step 4: Handle Multiple Task Cards (Future)

For multiple task cards, you'll need:

```javascript
// Load multiple step_combined.json files
const taskFiles = ['step_combined_1.json', 'step_combined_2.json', ...];

const allTaskData = await Promise.all(
  taskFiles.map(async (file) => {
    const response = await fetch(`./${file}`);
    const stepData = await response.json();
    const transformer = new StepDataTransformer(stepData);
    return transformer.transform();
  })
);
```

## Key Transformation Mappings

### Task Level
- **ID**: Generated from execution timestamp
- **Name**: Extracted from initial input ("Industry 101 Document Generator")
- **Context**: Derived from domain/industry ("Business research and analysis - Varies based on industry")
- **System Prompt**: Taken from any step's system_message

### Test Scenarios
- **ID**: Uses seed_id from test results
- **Description**: Parsed from user_message (e.g., "Analyze Technology industry in North America - 5 companies")
- **Status**: Based on comparison_result.match (true = pass, false = fail)
- **Output**: Truncated actual_output for display
- **Input**: Extracted parameters (industry, region, time period, etc.)

### Grader Scores
- **Content Match**: similarity_score * 100
- **Confidence**: confidence_level mapped to score (high=90, medium=70, low=40)
- **Completeness**: 100 - (missing_elements.length * 20)
- **Accuracy**: key_similarities.length * 20 (capped at 100)

### Aggregate Scores
- **Overall**: Total pass rate across all tests
- **Content Quality**: Average Content Match scores
- **Completeness**: Average Completeness scores
- **Accuracy**: Average Accuracy scores
- **Confidence**: Average Confidence scores

## Testing the Integration

1. **Run transformation test**: Verify data transforms correctly
2. **Check SPA display**: Ensure UI renders with real data
3. **Validate interactions**: Test clicking on different elements
4. **Verify grader details**: Check that grader scores display properly

## Error Handling

The integration includes fallbacks:
- If step_combined.json fails to load → use dummy data
- If transformation fails → log error and use dummy data
- If specific fields are missing → provide default values

## Performance Considerations

- **Data size**: step_combined.json can be large (2700+ lines)
- **Transformation time**: Runs once on page load
- **Memory usage**: Keeps transformed data in memory
- **Caching**: Consider caching transformed data

## Future Enhancements

1. **Real-time updates**: WebSocket connection for live data
2. **Multiple tasks**: Support for multiple task cards
3. **Filtering**: Filter tests by status, category, etc.
4. **Export**: Export test results and grader scores
5. **Historical data**: Show trends over time

## Validation Checklist

- [ ] Transformation produces valid SPA format
- [ ] All test scenarios display correctly
- [ ] Grader scores show proper criteria and values
- [ ] Aggregate scores calculate correctly
- [ ] Error handling works for missing data
- [ ] Performance is acceptable for large datasets
- [ ] UI interactions work with real data
