# Data Mapping Analysis: step_combined.json → SPA

## Overview
The `step_combined.json` file represents **ONE task card** with execution data from a complete pipeline run. The SPA needs to display this as a single task card with multiple test scenarios and grading information.

## Key Insight
- `step_combined.json` = 1 Task Card
- Multiple `test_results` arrays across steps = Multiple Test Scenarios for that task
- Each `comparison_result` = Grader scores for that test scenario

## Data Structure Mapping

### 1. Task Card Level Data

**Source**: Root level and Step 1 data
```javascript
// SPA Expected Format:
{
  id: 'task-001',
  name: 'Task name',
  context: 'environment info', 
  systemPrompt: 'prompt text'
}

// step_combined.json Mapping:
{
  id: generateId(timestamp), // Generate from execution timestamp
  name: extractTaskName(sections[0].data), // From "INITIAL INPUT" 
  context: extractContext(requirements_doc), // From domain/industry info
  systemPrompt: system_message // From any step's system_message
}
```

### 2. Test Scenarios Data

**Source**: All `test_results` arrays across all steps
```javascript
// SPA Expected Format:
testScenarios: [
  {
    id: 'test-001',
    description: 'test description',
    status: 'pass'|'fail',
    output: 'result text',
    input: { object },
    graderScores: [...]
  }
]

// step_combined.json Mapping:
testScenarios: collectAllTestResults().map(testResult => ({
  id: testResult.seed_id,
  description: extractTestDescription(testResult.user_message),
  status: testResult.comparison_result.match ? 'pass' : 'fail',
  output: truncate(testResult.actual_output, 100),
  input: extractInputParams(testResult.user_message),
  graderScores: mapComparisonToGraderScores(testResult.comparison_result)
}))
```

### 3. Grader Scores Mapping

**Source**: `comparison_result` object in each test result
```javascript
// SPA Expected Format:
graderScores: [
  { criterion: 'Accuracy', score: 85, status: 'pass' }
]

// step_combined.json Mapping:
graderScores: [
  {
    criterion: 'Content Match',
    score: Math.round(comparison_result.similarity_score * 100),
    status: comparison_result.similarity_score > 0.8 ? 'pass' : 
            comparison_result.similarity_score > 0.5 ? 'warn' : 'fail'
  },
  {
    criterion: 'Confidence',
    score: mapConfidenceToScore(comparison_result.confidence_level),
    status: comparison_result.confidence_level === 'high' ? 'pass' : 'warn'
  },
  {
    criterion: 'Completeness', 
    score: calculateCompletenessScore(comparison_result.missing_elements),
    status: comparison_result.missing_elements.length === 0 ? 'pass' : 'fail'
  },
  {
    criterion: 'Accuracy',
    score: calculateAccuracyScore(comparison_result.key_similarities),
    status: comparison_result.key_similarities.length > 3 ? 'pass' : 'warn'
  }
]
```

### 4. Aggregate Scores

**Source**: Calculated from all test results
```javascript
// SPA Expected Format:
aggregateScores: [
  { category: 'Performance', passRate: 85 }
]

// step_combined.json Mapping:
aggregateScores: calculateAggregateScores(allTestResults)
// Categories could be derived from:
// - Step names (Requirements, Prompt Generation, etc.)
// - Test types (Content Match, Completeness, etc.)
// - Industry domains (Technology, Automotive, etc.)
```

## Implementation Steps

1. **Extract Task Info**: Parse initial input and requirements for task metadata
2. **Collect All Tests**: Gather test_results from all steps into single array
3. **Transform Tests**: Convert each test result to SPA test scenario format
4. **Calculate Aggregates**: Derive category-based pass rates from test results
5. **Generate UI Data**: Create final object matching SPA's expected format

## Key Transformation Functions Needed

```javascript
// Core transformation functions:
- extractTaskName(initialInput)
- extractContext(requirementsDoc) 
- collectAllTestResults(stepData)
- mapComparisonToGraderScores(comparisonResult)
- calculateAggregateScores(testResults)
- extractInputParams(userMessage)
- extractTestDescription(userMessage)
```
