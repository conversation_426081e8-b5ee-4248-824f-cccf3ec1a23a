// Test script to verify data transformation
// Run with: node test_transformation.js

const fs = require('fs');
const path = require('path');

// Import transformer (assuming it's in same directory)
const StepDataTransformer = require('./data_transformer.js');

// Load the step_combined.json data
function loadStepData() {
  try {
    const dataPath = path.join(__dirname, '../../step_combined.json');
    const rawData = fs.readFileSync(dataPath, 'utf8');
    return JSON.parse(rawData);
  } catch (error) {
    console.error('Error loading step_combined.json:', error.message);
    return null;
  }
}

// Test the transformation
function testTransformation() {
  console.log('🔄 Loading step_combined.json...');
  const stepData = loadStepData();
  
  if (!stepData) {
    console.error('❌ Failed to load data');
    return;
  }

  console.log('✅ Data loaded successfully');
  console.log(`📊 Found ${stepData.step_by_step.sections.length} sections`);

  console.log('\n🔄 Running transformation...');
  const transformer = new StepDataTransformer(stepData);
  const spaData = transformer.transform();

  console.log('✅ Transformation complete');
  
  // Display results
  console.log('\n📋 TRANSFORMATION RESULTS:');
  console.log('=' .repeat(50));
  
  console.log(`\n🏷️  Task ID: ${spaData.id}`);
  console.log(`📝 Task Name: ${spaData.name}`);
  console.log(`🌍 Context: ${spaData.context}`);
  console.log(`🤖 System Prompt: ${spaData.systemPrompt.substring(0, 100)}...`);
  
  console.log(`\n🧪 Test Scenarios: ${spaData.testScenarios.length} found`);
  spaData.testScenarios.forEach((test, i) => {
    console.log(`  ${i + 1}. ${test.id}: ${test.description} [${test.status.toUpperCase()}]`);
    console.log(`     Output: ${test.output}`);
    console.log(`     Grader Scores: ${test.graderScores.length} criteria`);
    test.graderScores.forEach(score => {
      console.log(`       - ${score.criterion}: ${score.score}% [${score.status.toUpperCase()}]`);
    });
    console.log('');
  });

  console.log(`\n📊 Aggregate Scores: ${spaData.aggregateScores.length} categories`);
  spaData.aggregateScores.forEach(score => {
    console.log(`  - ${score.category}: ${score.passRate}%`);
  });

  // Save transformed data
  const outputPath = path.join(__dirname, 'transformed_spa_data.json');
  fs.writeFileSync(outputPath, JSON.stringify(spaData, null, 2));
  console.log(`\n💾 Transformed data saved to: ${outputPath}`);

  // Validate against SPA expected format
  console.log('\n🔍 VALIDATION:');
  validateSpaFormat(spaData);
}

// Validate the transformed data matches SPA expectations
function validateSpaFormat(data) {
  const errors = [];
  
  // Check required fields
  if (!data.id) errors.push('Missing id field');
  if (!data.name) errors.push('Missing name field');
  if (!data.context) errors.push('Missing context field');
  if (!data.systemPrompt) errors.push('Missing systemPrompt field');
  if (!Array.isArray(data.testScenarios)) errors.push('testScenarios must be array');
  if (!Array.isArray(data.aggregateScores)) errors.push('aggregateScores must be array');

  // Check test scenario format
  data.testScenarios.forEach((test, i) => {
    if (!test.id) errors.push(`Test ${i}: missing id`);
    if (!test.description) errors.push(`Test ${i}: missing description`);
    if (!['pass', 'fail'].includes(test.status)) errors.push(`Test ${i}: invalid status`);
    if (!test.output) errors.push(`Test ${i}: missing output`);
    if (!Array.isArray(test.graderScores)) errors.push(`Test ${i}: graderScores must be array`);
    
    test.graderScores.forEach((score, j) => {
      if (!score.criterion) errors.push(`Test ${i}, Score ${j}: missing criterion`);
      if (typeof score.score !== 'number') errors.push(`Test ${i}, Score ${j}: score must be number`);
      if (!['pass', 'fail', 'warn'].includes(score.status)) errors.push(`Test ${i}, Score ${j}: invalid status`);
    });
  });

  // Check aggregate scores format
  data.aggregateScores.forEach((score, i) => {
    if (!score.category) errors.push(`Aggregate ${i}: missing category`);
    if (typeof score.passRate !== 'number') errors.push(`Aggregate ${i}: passRate must be number`);
  });

  if (errors.length === 0) {
    console.log('✅ All validation checks passed!');
  } else {
    console.log('❌ Validation errors found:');
    errors.forEach(error => console.log(`  - ${error}`));
  }
}

// Run the test
if (require.main === module) {
  testTransformation();
}

module.exports = { testTransformation, validateSpaFormat };
