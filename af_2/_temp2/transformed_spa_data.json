{"id": "task-392755", "name": "Industry 101 Document Generator", "context": "Business research and analysis - Varies based on the selected industry branch", "systemPrompt": "You are an Industry Research Analyst, a market research expert specializing in industry analysis. Professional and analytical tone. Adhere to a data-driven methodology, ensure accuracy, cite all sources, structure the document clearly, and avoid unverified information.", "testScenarios": [{"id": "seed_1", "description": "Conduct a comprehensive analysis of the technology in the No...", "status": "pass", "output": "# Market Research Report: Industry Analysis\n\n## Executive Summary\nThis report presents a comprehe...", "input": {"industry": "technology", "timePeriod": "2020-2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 85, "status": "pass"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 60, "status": "warn"}, {"criterion": "Accuracy", "score": 100, "status": "pass"}]}, {"id": "seed_2", "description": "Conduct a comprehensive analysis of the Technology in the No...", "status": "fail", "output": "It seems that your message did not come through. Please specify the industry or market you would ...", "input": {"industry": "Technology", "timePeriod": "2020-2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 10, "status": "fail"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 0, "status": "fail"}, {"criterion": "Accuracy", "score": 0, "status": "fail"}]}, {"id": "seed_3", "description": "Conduct a comprehensive analysis of the automotive in the No...", "status": "fail", "output": "# Industry Analysis Report: Overview of the Electric Vehicle Market (2023)\n\n## Executive Summary\n...", "input": {"industry": "automotive", "timePeriod": "2020-2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 85, "status": "pass"}, {"criterion": "Confidence", "score": 70, "status": "warn"}, {"criterion": "Completeness", "score": 80, "status": "warn"}, {"criterion": "Accuracy", "score": 80, "status": "pass"}]}, {"id": "seed_1", "description": "Conduct a comprehensive analysis of the Automotive industry ...", "status": "fail", "output": "It appears that there was no information provided in your prompt. Please specify the industry or ...", "input": {"industry": "Automotive", "timePeriod": "2020-2023", "numCompanies": 7}, "graderScores": [{"criterion": "Content Match", "score": 10, "status": "fail"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 0, "status": "fail"}, {"criterion": "Accuracy", "score": 0, "status": "fail"}]}, {"id": "seed_2", "description": "Conduct a comprehensive analysis of the Automotive industry ...", "status": "fail", "output": "It appears that your message did not come through. How may I assist you today with your industry ...", "input": {"industry": "Automotive", "timePeriod": "2020-2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 10, "status": "fail"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 0, "status": "fail"}, {"criterion": "Accuracy", "score": 0, "status": "fail"}]}, {"id": "seed_3", "description": "Conduct a comprehensive analysis of the Automotive industry ...", "status": "fail", "output": "It appears that your message was empty. Please provide any specific information or questions you ...", "input": {"industry": "Automotive", "timePeriod": "2020-2025", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 0, "status": "fail"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 0, "status": "fail"}, {"criterion": "Accuracy", "score": 0, "status": "fail"}]}, {"id": "seed_1", "description": "Conduct a comprehensive analysis of the Automotive industry ...", "status": "fail", "output": "# Industry Analysis Report: [Insert Specific Industry or Sector]\n\n## Executive Summary\nThis repor...", "input": {"industry": "Automotive", "timePeriod": "2020-2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 85, "status": "pass"}, {"criterion": "Confidence", "score": 70, "status": "warn"}, {"criterion": "Completeness", "score": 60, "status": "warn"}, {"criterion": "Accuracy", "score": 60, "status": "warn"}]}, {"id": "seed_2", "description": "Conduct a comprehensive analysis of the Automotive industry ...", "status": "fail", "output": "It appears that your message was empty. Please provide the details or specific questions you woul...", "input": {"industry": "Automotive", "timePeriod": "2022 to 2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 10, "status": "fail"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 0, "status": "fail"}, {"criterion": "Accuracy", "score": 0, "status": "fail"}]}, {"id": "seed_3", "description": "Conduct a comprehensive analysis of the Automotive industry ...", "status": "fail", "output": "It appears that your message was empty. In order to assist you effectively, please provide more i...", "input": {"industry": "Automotive", "timePeriod": "2020-2023", "numCompanies": 5}, "graderScores": [{"criterion": "Content Match", "score": 10, "status": "fail"}, {"criterion": "Confidence", "score": 90, "status": "pass"}, {"criterion": "Completeness", "score": 0, "status": "fail"}, {"criterion": "Accuracy", "score": 0, "status": "fail"}]}], "aggregateScores": [{"category": "Overall", "passRate": 11}, {"category": "Content Quality", "passRate": 34}, {"category": "Completeness", "passRate": 22}, {"category": "Accuracy", "passRate": 27}, {"category": "Confidence", "passRate": 86}]}