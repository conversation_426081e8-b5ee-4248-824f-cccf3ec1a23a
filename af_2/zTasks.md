# zTasks - Data Mapping Analysis

## Task Checklist
- [x] Analyze af_chat_spa.js to understand proxy data structure requirements
- [x] Analyze step_combined.json to understand available data
- [x] Create detailed mapping analysis document
- [x] Implement practical mapping code (map_step_to_spa.js)
- [x] Test mapping implementation
- [x] Generate sample mapped_task_card.json

## Completed Tasks

### Data Mapping Analysis for SPA Integration
**Task**: Thoroughly analyze and create mapping between step_combined.json and SPA expectations

**Deliverables**:
1. `/af_2/.tmp/data_mapping_analysis.md` - Comprehensive analysis document showing:
   - What data the SPA expects (task cards with test scenarios, grader scores, aggregate scores)
   - What step_combined.json provides (workflow steps, requirements, prompts, test cases)
   - Detailed mapping strategy with implementation examples
   - Identified gaps and recommendations

2. `/af_2/map_step_to_spa.js` - Working implementation that:
   - Loads step_combined.json data
   - Transforms it to match SPA's expected format
   - Generates grader scores from quality metrics
   - Creates aggregate scores from various quality indicators
   - Exports mapped data as JSON

3. `/af_2/mapped_task_card.json` - Sample output showing transformed data ready for SPA

**Key Findings**:
- SPA expects test execution results, but step_combined.json only has test definitions
- Quality metrics need conversion from 0-1 scale to percentage scores
- Some data needs to be inferred or generated (e.g., test status from validation)
- Mapping is feasible but requires significant transformation

**Validation Checks Implemented**:
- ✓ Task identification (id, name) - derived from prompt and metadata
- ✓ System configuration - available in final_prompt
- ✓ Test definitions - available in test_cases
- ✓ Quality metrics - transformed to grader scores
- ✗ Real execution results - must be mocked or enhanced in future

## Next Steps
- [ ] Integrate mapped data with actual SPA
- [ ] Add real test execution results to data pipeline
- [ ] Enhance grader scoring with more specific criteria
- [ ] Create automated pipeline for continuous data mapping