{"id": "task-1752715484305", "name": "I want to create a Industry 101 document that will...", "context": "financial - professional", "systemPrompt": "You are an Industry Research Analyst, a market research expert specializing in industry analysis. Professional and analytical tone. Utilize quantitative analysis methods such as regression modeling and historical data comparison. Ensure that all sources are validated for accuracy and compliance with regulations like GDPR. Structure the document clearly, and avoid unverified information.", "testScenarios": [{"id": "seed_1", "description": "This test scenario is important as it assesses whether the system can handle valid inputs and produce a comprehensive industry analysis report, ensuring adherence to specified formatting and style guidelines", "status": "pass", "output": "MARKDOWN_REPORT with sections including Executive Summary, Industry Overview, Key Company Profiles w...", "input": {"{{region}}": "North America", "{{time_period}}": "2020-2023", "{{number_of_companies}}": "5"}, "graderScores": [{"criterion": "Complexity Handling", "score": 80, "status": "pass"}, {"criterion": "Relevance", "score": 90, "status": "pass"}, {"criterion": "Uniqueness", "score": 100, "status": "pass"}]}, {"id": "seed_2", "description": "This test scenario is important as it verifies the system's ability to process valid inputs and generate a comprehensive report that meets specified formatting and content guidelines", "status": "fail", "output": "MARKDOWN_REPORT containing sections: Executive Summary, Industry Overview, Key Company Profiles with...", "input": {"{{region}}": "North America", "{{time_period}}": "2020-2023", "{{number_of_companies}}": "5"}, "graderScores": [{"criterion": "Complexity Handling", "score": 80, "status": "pass"}, {"criterion": "Relevance", "score": 90, "status": "pass"}, {"criterion": "Uniqueness", "score": 100, "status": "pass"}]}, {"id": "seed_3", "description": "This test scenario is important as it validates the system's ability to generate a comprehensive and well-structured report based on specified inputs, ensuring accurate data sourcing and compliance with industry standards", "status": "pass", "output": "structured Markdown report containing an Executive Summary, Industry Overview, Key Company Profiles...", "input": {"{{region}}": "North America", "{{time_period}}": "2020-2023", "{{number_of_companies}}": "5"}, "graderScores": [{"criterion": "Complexity Handling", "score": 80, "status": "pass"}, {"criterion": "Relevance", "score": 90, "status": "pass"}, {"criterion": "Uniqueness", "score": 100, "status": "pass"}]}], "aggregateScores": [{"category": "Accuracy", "passRate": 92}, {"category": "Completeness", "passRate": 90}, {"category": "Consistency", "passRate": 93}, {"category": "Completeness", "passRate": 0}, {"category": "Consistency", "passRate": 100}, {"category": "Clarity", "passRate": 0}, {"category": "Feasibility", "passRate": 100}, {"category": "Traceability", "passRate": 100}, {"category": "Test Coverage", "passRate": 67}, {"category": "Test Diversity", "passRate": 20}]}