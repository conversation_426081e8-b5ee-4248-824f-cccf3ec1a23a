/**
 * Maps step_combined.json data to the SPA's expected task card format
 * This demonstrates the data transformation needed to display PFC workflow data in the UI
 */

const fs = require('fs');
const path = require('path');

// Load the step_combined.json data
function loadStepData(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading step data:', error);
    return null;
  }
}

// Extract placeholders and their values from test cases
function extractPlaceholders(testCase) {
  const placeholders = {};
  
  if (testCase.placeholders) {
    return testCase.placeholders;
  }
  
  // Extract from user message if not explicitly provided
  const userMessage = testCase.user_message || '';
  const matches = userMessage.match(/\{\{(\w+)\}\}/g) || [];
  
  matches.forEach(match => {
    const key = match.replace(/[{}]/g, '');
    // Try to infer values from seed data
    if (testCase.seed_data?.input) {
      if (testCase.seed_data.input.includes('North America')) {
        placeholders.region = 'North America';
      }
      if (testCase.seed_data.input.includes('2020-2023')) {
        placeholders.time_period = '2020-2023';
      }
      if (testCase.seed_data.input.includes('5')) {
        placeholders.number_of_companies = '5';
      }
    }
  });
  
  return placeholders;
}

// Generate grader scores from quality metrics
function generateGraderScores(qualityMetrics) {
  const scores = [];
  
  // Complexity score
  if (qualityMetrics?.complexity) {
    const complexityScore = {
      'simple': 60,
      'moderate': 80,
      'complex': 95
    }[qualityMetrics.complexity] || 70;
    
    scores.push({
      criterion: 'Complexity Handling',
      score: complexityScore,
      status: complexityScore >= 80 ? 'pass' : 'warn'
    });
  }
  
  // Relevance score
  if (qualityMetrics?.relevance !== undefined) {
    const relevanceScore = Math.round(qualityMetrics.relevance * 100);
    scores.push({
      criterion: 'Relevance',
      score: relevanceScore,
      status: relevanceScore >= 80 ? 'pass' : relevanceScore >= 60 ? 'warn' : 'fail'
    });
  }
  
  // Uniqueness score
  if (qualityMetrics?.uniqueness !== undefined) {
    const uniquenessScore = Math.round(qualityMetrics.uniqueness * 100);
    scores.push({
      criterion: 'Uniqueness',
      score: uniquenessScore,
      status: uniquenessScore >= 80 ? 'pass' : 'warn'
    });
  }
  
  // Add default score if none exist
  if (scores.length === 0) {
    scores.push({
      criterion: 'Overall Quality',
      score: 75,
      status: 'pass'
    });
  }
  
  return scores;
}

// Generate aggregate scores from various quality metrics
function generateAggregateScores(stepData) {
  const scores = [];
  
  // Extract quality metrics from requirements
  const qualityMetrics = stepData.results?.initial_requirements?.quality_metrics;
  if (qualityMetrics) {
    if (qualityMetrics.accuracy_threshold) {
      scores.push({
        category: 'Accuracy',
        passRate: Math.round(qualityMetrics.accuracy_threshold * 100)
      });
    }
    if (qualityMetrics.completeness_score) {
      scores.push({
        category: 'Completeness',
        passRate: Math.round(qualityMetrics.completeness_score * 100)
      });
    }
    if (qualityMetrics.consistency_score) {
      scores.push({
        category: 'Consistency',
        passRate: Math.round(qualityMetrics.consistency_score * 100)
      });
    }
  }
  
  // Extract validation results
  const validationStatus = stepData.results?.initial_requirements?.metadata?.validation_status;
  if (validationStatus) {
    Object.entries(validationStatus).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        scores.push({
          category: key.charAt(0).toUpperCase() + key.slice(1),
          passRate: value ? 100 : 0
        });
      }
    });
  }
  
  // Add test coverage metrics
  const testSection = stepData.step_by_step?.sections?.find(s => 
    s.name.includes('TEST CASE CHUNKER')
  );
  if (testSection?.data?.chunked_cases?.metadata?.quality_assessment) {
    const qa = testSection.data.chunked_cases.metadata.quality_assessment;
    scores.push({
      category: 'Test Coverage',
      passRate: Math.round((qa.coverage_score || 0) * 100)
    });
    scores.push({
      category: 'Test Diversity',
      passRate: Math.round((qa.diversity_score || 0) * 100)
    });
  }
  
  return scores;
}

// Main mapping function
function mapStepCombinedToTaskCard(stepData) {
  const results = stepData.results || {};
  
  // Find test cases section
  const testSection = stepData.step_by_step?.sections?.find(s => 
    s.name.includes('TEST CASE CHUNKER')
  );
  const testCases = testSection?.data?.chunked_cases?.test_cases || [];
  
  // Extract final prompt info
  const finalPrompt = results.final_prompt || {};
  const metadata = finalPrompt.metadata || {};
  
  // Create the task card
  const taskCard = {
    id: `task-${Date.now()}`,
    name: results.initial_prompt ? 
      results.initial_prompt.substring(0, 50).replace(/\s+/g, ' ').trim() + '...' :
      'Industry Analysis Task',
    context: `${metadata.domain || 'business'} - ${metadata.tone || 'professional'}`,
    systemPrompt: finalPrompt.system_message || 'No system prompt available',
    
    testScenarios: testCases.map((tc, idx) => {
      const seedData = tc.seed_data || {};
      const validation = tc.metadata?.validation || {};
      
      return {
        id: tc.seed_id || `test-${idx + 1}`,
        description: seedData.reasoning ? 
          seedData.reasoning.split('.')[0].trim() :
          'Test scenario ' + (idx + 1),
        status: validation.is_valid ? 'pass' : 'fail',
        output: seedData.expected_output ? 
          seedData.expected_output.substring(0, 100).trim() + '...' :
          'No output available',
        input: extractPlaceholders(tc),
        graderScores: generateGraderScores(seedData.quality_metrics)
      };
    }),
    
    aggregateScores: generateAggregateScores(stepData)
  };
  
  return taskCard;
}

// Export the mapped data as JSON
function exportMappedData(inputPath, outputPath) {
  const stepData = loadStepData(inputPath);
  if (!stepData) {
    console.error('Failed to load step data');
    return;
  }
  
  const taskCard = mapStepCombinedToTaskCard(stepData);
  
  // Write the mapped data
  fs.writeFileSync(outputPath, JSON.stringify(taskCard, null, 2));
  console.log(`Mapped data exported to: ${outputPath}`);
  
  // Also log a summary
  console.log('\nTask Card Summary:');
  console.log(`- Name: ${taskCard.name}`);
  console.log(`- Context: ${taskCard.context}`);
  console.log(`- Test Scenarios: ${taskCard.testScenarios.length}`);
  console.log(`- Aggregate Categories: ${taskCard.aggregateScores.length}`);
  
  return taskCard;
}

// Run the mapping if this script is executed directly
if (require.main === module) {
  const inputPath = path.join(__dirname, '..', 'step_combined.json');
  const outputPath = path.join(__dirname, 'mapped_task_card.json');
  
  exportMappedData(inputPath, outputPath);
}

// Export functions for use in other modules
module.exports = {
  loadStepData,
  mapStepCombinedToTaskCard,
  generateGraderScores,
  generateAggregateScores,
  exportMappedData
};