{"id": "task-633", "name": "Industry Analysis Pipeline Validation", "context": "staging - AWS us-east-1", "systemPrompt": "Validate the pipeline execution and ensure all components generate expected outputs according to specifications.", "testScenarios": [{"id": "test-001", "description": "Validate standard input processing", "status": "pass", "output": "Successful validation", "input": {"testCase": "scenario-1", "params": {"validate": true}}, "graderScores": [{"criterion": "Response Time", "score": 85, "status": "pass"}, {"criterion": "Format Compliance", "score": 90, "status": "pass"}, {"criterion": "Data Accuracy", "score": 88, "status": "pass"}, {"criterion": "Coverage", "score": 92, "status": "pass"}]}, {"id": "test-002", "description": "Test edge case handling", "status": "fail", "output": "Error: Invalid input format", "input": {"testCase": "scenario-2", "params": {"validate": true}}, "graderScores": [{"criterion": "Response Time", "score": 90, "status": "pass"}, {"criterion": "Format Compliance", "score": 90, "status": "pass"}, {"criterion": "Data Accuracy", "score": 88, "status": "pass"}, {"criterion": "Coverage", "score": 92, "status": "pass"}]}, {"id": "test-003", "description": "Verify error responses", "status": "pass", "output": "Successful validation", "input": {"testCase": "scenario-3", "params": {"validate": true}}, "graderScores": [{"criterion": "Response Time", "score": 95, "status": "pass"}, {"criterion": "Format Compliance", "score": 90, "status": "pass"}, {"criterion": "Data Accuracy", "score": 88, "status": "pass"}, {"criterion": "Coverage", "score": 92, "status": "pass"}]}], "aggregateScores": [{"category": "Authentication", "passRate": 85}, {"category": "Data Processing", "passRate": 92}, {"category": "API Responses", "passRate": 85}, {"category": "Erro<PERSON>", "passRate": 78}, {"category": "Performance", "passRate": 91}, {"category": "Security", "passRate": 96}, {"category": "Validation", "passRate": 91}, {"category": "Integration", "passRate": 88}, {"category": "Compliance", "passRate": 90}, {"category": "Documentation", "passRate": 87}]}