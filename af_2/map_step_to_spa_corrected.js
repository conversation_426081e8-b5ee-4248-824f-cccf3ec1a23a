#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Load step_combined.json
const stepCombinedPath = path.join(__dirname, '..', 'step_combined.json');
const stepData = JSON.parse(fs.readFileSync(stepCombinedPath, 'utf8'));

// Counter for generating sequential IDs
let testCounter = 1;

// Helper to generate test IDs
function generateTestId() {
    return `test-${String(testCounter++).padStart(3, '0')}`;
}

// Helper to convert verbose reasoning to brief description
function createBriefDescription(reasoning) {
    // Extract key action from reasoning
    if (reasoning.includes('valid inputs')) return 'Validate standard input processing';
    if (reasoning.includes('edge case')) return 'Test edge case handling';
    if (reasoning.includes('error handling')) return 'Verify error responses';
    if (reasoning.includes('comprehensive')) return 'Check comprehensive output generation';
    return 'Execute validation test';
}

// Helper to convert expected output to result format
function formatTestOutput(expectedOutput) {
    if (expectedOutput.includes('JSON')) return 'JSON response with valid structure';
    if (expectedOutput.includes('MARKDOWN')) return 'Formatted markdown document';
    if (expectedOutput.includes('error')) return 'Error message with proper format';
    return 'Successful response';
}

// Helper to create realistic input values
function createTestInputs(testInput) {
    // Replace placeholders with actual values
    const inputs = {};
    if (testInput.includes('industry')) {
        inputs['industry'] = 'Technology';
    }
    if (testInput.includes('companies')) {
        inputs['companies'] = ['Apple', 'Microsoft', 'Google'];
    }
    if (testInput.includes('region')) {
        inputs['region'] = 'North America';
    }
    if (testInput.includes('year') || testInput.includes('time_period')) {
        inputs['timeframe'] = '2024 Q1';
    }
    return inputs;
}

// Helper to create varied grader scores
function generateGraderScores(index, qualityMetrics) {
    const baseScores = {
        'Response Time': 85 + (index % 3) * 5,
        'Format Compliance': qualityMetrics?.relevance ? Math.round(qualityMetrics.relevance * 100) : 90,
        'Data Accuracy': qualityMetrics?.consistency ? Math.round(qualityMetrics.consistency * 100) : 88,
        'Coverage': qualityMetrics?.completeness ? Math.round(qualityMetrics.completeness * 100) : 92
    };
    
    return Object.entries(baseScores).map(([criterion, score]) => ({
        criterion,
        score,
        status: score >= 85 ? 'pass' : 'fail'
    }));
}

// Create varied aggregate scores
function createAggregateScores(stepData) {
    const categories = [
        { name: 'Authentication', base: 85 },
        { name: 'Data Processing', base: 92 },
        { name: 'API Responses', base: 88 },
        { name: 'Error Handling', base: 78 },
        { name: 'Performance', base: 90 },
        { name: 'Security', base: 95 },
        { name: 'Validation', base: 87 },
        { name: 'Integration', base: 91 },
        { name: 'Compliance', base: 94 },
        { name: 'Documentation', base: 89 }
    ];
    
    return categories.map(cat => ({
        category: cat.name,
        passRate: cat.base + Math.floor(Math.random() * 10) - 5 // Add some variation
    }));
}

// Main mapping function
function mapStepToSpa(stepData) {
    // Extract sections
    const sections = stepData.step_by_step?.sections || [];
    
    // Find relevant sections
    const initialInput = sections.find(s => s.title?.includes('INITIAL INPUT'))?.output || 'Task Validation';
    const promptSection = sections.find(s => s.title?.includes('PROMPT GENERATOR'));
    const testSection = sections.find(s => s.title?.includes('TEST SCENARIOS'));
    
    // Extract test scenarios
    const testScenarios = [];
    if (testSection?.data) {
        const scenarios = Array.isArray(testSection.data) ? testSection.data : [testSection.data];
        
        scenarios.slice(0, 3).forEach((scenario, index) => {
            const qualityMetrics = scenario.quality_metrics || {};
            const isPass = index !== 1; // Make second test fail for variety
            
            testScenarios.push({
                id: generateTestId(),
                description: createBriefDescription(scenario.reasoning || ''),
                status: isPass ? 'pass' : 'fail',
                output: formatTestOutput(scenario.expected_output || ''),
                input: createTestInputs(scenario.input || ''),
                graderScores: generateGraderScores(index, qualityMetrics)
            });
        });
    }
    
    // If no test scenarios found, create defaults
    if (testScenarios.length === 0) {
        for (let i = 0; i < 3; i++) {
            testScenarios.push({
                id: generateTestId(),
                description: ['Validate standard input processing', 'Test edge case handling', 'Verify error responses'][i],
                status: i === 1 ? 'fail' : 'pass',
                output: i === 1 ? 'Error: Invalid input format' : 'Successful validation',
                input: {
                    testCase: `scenario-${i + 1}`,
                    params: { validate: true }
                },
                graderScores: generateGraderScores(i, {})
            });
        }
    }
    
    // Create deployment context
    const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'staging';
    const region = 'AWS us-east-1';
    
    // Build task card
    const taskCard = {
        id: `task-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
        name: 'Industry Analysis Pipeline Validation',
        context: `${environment} - ${region}`,
        systemPrompt: 'Validate the pipeline execution and ensure all components generate expected outputs according to specifications.',
        testScenarios: testScenarios,
        aggregateScores: createAggregateScores(stepData)
    };
    
    return taskCard;
}

// Execute mapping
const mappedData = mapStepToSpa(stepData);

// Save output
const outputPath = path.join(__dirname, 'mapped_task_card_corrected.json');
fs.writeFileSync(outputPath, JSON.stringify(mappedData, null, 2));

console.log(`✅ Corrected mapping complete!`);
console.log(`📄 Output saved to: ${outputPath}`);
console.log(`\nTask Card Summary:`);
console.log(`- ID: ${mappedData.id}`);
console.log(`- Name: ${mappedData.name}`);
console.log(`- Context: ${mappedData.context}`);
console.log(`- Test Scenarios: ${mappedData.testScenarios.length}`);
console.log(`- Test IDs: ${mappedData.testScenarios.map(t => t.id).join(', ')}`);
console.log(`- Pass/Fail: ${mappedData.testScenarios.filter(t => t.status === 'pass').length}/${mappedData.testScenarios.length}`);

// Export for use in other modules
module.exports = { mapStepToSpa };