#!/usr/bin/env python3
"""
Comprehensive test script to verify the mapping between source data and the SPA format.
This script performs thorough field-by-field comparison and edge case testing.
"""

import json
import sys
from typing import Dict, List, Any, Tuple
from datetime import datetime


class MappingVerifier:
    def __init__(self):
        self.spa_template = self._get_spa_template()
        self.test_results = []
        self.field_mismatches = []
        
    def _get_spa_template(self) -> Dict[str, Any]:
        """Get the expected SPA structure from the dummy data"""
        return {
            "id": str,  # Expected format: 'task-XXX'
            "name": str,  # Task name/description
            "context": str,  # Context information
            "systemPrompt": str,  # System prompt for the task
            "testScenarios": [
                {
                    "id": str,  # Expected format: 'test-XXX'
                    "description": str,
                    "status": str,  # 'pass' or 'fail'
                    "output": str,
                    "input": dict,  # Input parameters
                    "graderScores": [
                        {
                            "criterion": str,
                            "score": (int, float),
                            "status": str  # 'pass', 'fail', or 'warn'
                        }
                    ]
                }
            ],
            "aggregateScores": [
                {
                    "category": str,
                    "passRate": (int, float)
                }
            ]
        }
    
    def verify_field_types(self, data: Dict[str, Any], path: str = "") -> List[str]:
        """Recursively verify field types match expected structure"""
        errors = []
        
        # Check top-level fields
        expected_fields = ["id", "name", "context", "systemPrompt", "testScenarios", "aggregateScores"]
        for field in expected_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
        
        # Verify ID format
        if "id" in data:
            if not isinstance(data["id"], str):
                errors.append(f"Field 'id' should be string, got {type(data['id']).__name__}")
            if not data["id"].startswith("task-"):
                errors.append(f"ID should start with 'task-', got: {data['id']}")
        
        # Verify test scenarios
        if "testScenarios" in data:
            if not isinstance(data["testScenarios"], list):
                errors.append("testScenarios should be a list")
            else:
                for i, scenario in enumerate(data["testScenarios"]):
                    scenario_errors = self._verify_test_scenario(scenario, f"testScenarios[{i}]")
                    errors.extend(scenario_errors)
        
        # Verify aggregate scores
        if "aggregateScores" in data:
            if not isinstance(data["aggregateScores"], list):
                errors.append("aggregateScores should be a list")
            else:
                for i, score in enumerate(data["aggregateScores"]):
                    score_errors = self._verify_aggregate_score(score, f"aggregateScores[{i}]")
                    errors.extend(score_errors)
        
        return errors
    
    def _verify_test_scenario(self, scenario: Dict[str, Any], path: str) -> List[str]:
        """Verify individual test scenario structure"""
        errors = []
        expected_fields = ["id", "description", "status", "output", "input", "graderScores"]
        
        for field in expected_fields:
            if field not in scenario:
                errors.append(f"{path}: Missing field '{field}'")
        
        # Verify ID format
        if "id" in scenario:
            if not isinstance(scenario["id"], str):
                errors.append(f"{path}.id should be string")
            # Check if it follows expected pattern
            if not (scenario["id"].startswith("test-") or scenario["id"].startswith("seed_")):
                errors.append(f"{path}.id should start with 'test-' or 'seed_', got: {scenario['id']}")
        
        # Verify status
        if "status" in scenario:
            if scenario["status"] not in ["pass", "fail"]:
                errors.append(f"{path}.status should be 'pass' or 'fail', got: {scenario['status']}")
        
        # Verify grader scores
        if "graderScores" in scenario:
            if not isinstance(scenario["graderScores"], list):
                errors.append(f"{path}.graderScores should be a list")
            else:
                for i, score in enumerate(scenario["graderScores"]):
                    score_errors = self._verify_grader_score(score, f"{path}.graderScores[{i}]")
                    errors.extend(score_errors)
        
        return errors
    
    def _verify_grader_score(self, score: Dict[str, Any], path: str) -> List[str]:
        """Verify grader score structure"""
        errors = []
        expected_fields = ["criterion", "score", "status"]
        
        for field in expected_fields:
            if field not in score:
                errors.append(f"{path}: Missing field '{field}'")
        
        if "score" in score:
            if not isinstance(score["score"], (int, float)):
                errors.append(f"{path}.score should be numeric, got: {type(score['score']).__name__}")
            elif not 0 <= score["score"] <= 100:
                errors.append(f"{path}.score should be between 0-100, got: {score['score']}")
        
        if "status" in score:
            if score["status"] not in ["pass", "fail", "warn"]:
                errors.append(f"{path}.status should be 'pass', 'fail', or 'warn', got: {score['status']}")
        
        return errors
    
    def _verify_aggregate_score(self, score: Dict[str, Any], path: str) -> List[str]:
        """Verify aggregate score structure"""
        errors = []
        expected_fields = ["category", "passRate"]
        
        for field in expected_fields:
            if field not in score:
                errors.append(f"{path}: Missing field '{field}'")
        
        if "passRate" in score:
            if not isinstance(score["passRate"], (int, float)):
                errors.append(f"{path}.passRate should be numeric")
            elif not 0 <= score["passRate"] <= 100:
                errors.append(f"{path}.passRate should be between 0-100, got: {score['passRate']}")
        
        return errors
    
    def compare_with_source(self, mapped_data: Dict[str, Any], source_data: Dict[str, Any]) -> Dict[str, Any]:
        """Compare mapped data with source data to identify mapping issues"""
        comparison = {
            "field_mappings": {},
            "data_transformations": [],
            "potential_issues": []
        }
        
        # Check if source data contains necessary information
        if "metadata" in source_data and "initial_prompt" in source_data["metadata"]:
            source_prompt = source_data["metadata"]["initial_prompt"]
            if mapped_data.get("name", "").strip() != source_prompt.strip():
                # Check if it's truncated
                if source_prompt.startswith(mapped_data.get("name", "")):
                    comparison["data_transformations"].append({
                        "field": "name",
                        "transformation": "truncated",
                        "source_length": len(source_prompt),
                        "mapped_length": len(mapped_data.get("name", ""))
                    })
                else:
                    comparison["potential_issues"].append({
                        "field": "name",
                        "issue": "Content mismatch",
                        "source": source_prompt[:50] + "...",
                        "mapped": mapped_data.get("name", "")[:50] + "..."
                    })
        
        # Check test scenarios mapping
        if "step_3_synthetic_data" in source_data and "seeds" in source_data["step_3_synthetic_data"]:
            source_seeds = source_data["step_3_synthetic_data"]["seeds"]
            mapped_scenarios = mapped_data.get("testScenarios", [])
            
            if len(source_seeds) != len(mapped_scenarios):
                comparison["potential_issues"].append({
                    "field": "testScenarios",
                    "issue": "Count mismatch",
                    "source_count": len(source_seeds),
                    "mapped_count": len(mapped_scenarios)
                })
        
        return comparison
    
    def test_edge_cases(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Test various edge cases in the mapped data"""
        edge_case_results = []
        
        # Test 1: Empty or missing fields
        empty_fields = []
        for field in ["id", "name", "context", "systemPrompt"]:
            if field not in data or not data[field]:
                empty_fields.append(field)
        
        if empty_fields:
            edge_case_results.append({
                "test": "Empty/Missing Required Fields",
                "status": "FAIL",
                "fields": empty_fields
            })
        
        # Test 2: Duplicate test scenario IDs
        if "testScenarios" in data:
            ids = [s.get("id") for s in data["testScenarios"]]
            duplicates = [id for id in ids if ids.count(id) > 1]
            if duplicates:
                edge_case_results.append({
                    "test": "Duplicate Test Scenario IDs",
                    "status": "FAIL",
                    "duplicates": list(set(duplicates))
                })
        
        # Test 3: Duplicate aggregate score categories
        if "aggregateScores" in data:
            categories = [s.get("category") for s in data["aggregateScores"]]
            duplicates = [cat for cat in categories if categories.count(cat) > 1]
            if duplicates:
                edge_case_results.append({
                    "test": "Duplicate Aggregate Score Categories",
                    "status": "WARNING",
                    "duplicates": list(set(duplicates))
                })
        
        # Test 4: Inconsistent grader score status vs score value
        if "testScenarios" in data:
            for scenario in data["testScenarios"]:
                if "graderScores" in scenario:
                    for score in scenario["graderScores"]:
                        if "score" in score and "status" in score:
                            score_val = score["score"]
                            status = score["status"]
                            # Check consistency
                            if status == "pass" and score_val < 50:
                                edge_case_results.append({
                                    "test": "Inconsistent Score/Status",
                                    "status": "WARNING",
                                    "scenario": scenario.get("id"),
                                    "criterion": score.get("criterion"),
                                    "score": score_val,
                                    "status": status,
                                    "issue": "Pass status with score < 50"
                                })
                            elif status == "fail" and score_val > 80:
                                edge_case_results.append({
                                    "test": "Inconsistent Score/Status",
                                    "status": "WARNING",
                                    "scenario": scenario.get("id"),
                                    "criterion": score.get("criterion"),
                                    "score": score_val,
                                    "status": status,
                                    "issue": "Fail status with score > 80"
                                })
        
        return edge_case_results
    
    def generate_report(self, mapped_file: str, source_file: str = None) -> str:
        """Generate comprehensive test report"""
        report_lines = []
        report_lines.append("# Mapping Test Report")
        report_lines.append(f"Generated at: {datetime.now().isoformat()}")
        report_lines.append("")
        
        # Load mapped data
        try:
            with open(mapped_file, 'r') as f:
                mapped_data = json.load(f)
        except Exception as e:
            return f"Error loading mapped file: {e}"
        
        # Field type verification
        report_lines.append("## Field Type Verification")
        field_errors = self.verify_field_types(mapped_data)
        if field_errors:
            report_lines.append("### Errors Found:")
            for error in field_errors:
                report_lines.append(f"- {error}")
        else:
            report_lines.append("✓ All field types match expected structure")
        report_lines.append("")
        
        # Source comparison if available
        if source_file:
            try:
                with open(source_file, 'r') as f:
                    source_data = json.load(f)
                
                report_lines.append("## Source Data Comparison")
                comparison = self.compare_with_source(mapped_data, source_data)
                
                if comparison["data_transformations"]:
                    report_lines.append("### Data Transformations:")
                    for transform in comparison["data_transformations"]:
                        report_lines.append(f"- {transform['field']}: {transform['transformation']}")
                
                if comparison["potential_issues"]:
                    report_lines.append("### Potential Issues:")
                    for issue in comparison["potential_issues"]:
                        report_lines.append(f"- {issue['field']}: {issue['issue']}")
                
                report_lines.append("")
            except Exception as e:
                report_lines.append(f"Error loading source file: {e}")
                report_lines.append("")
        
        # Edge case testing
        report_lines.append("## Edge Case Testing")
        edge_results = self.test_edge_cases(mapped_data)
        if edge_results:
            for result in edge_results:
                report_lines.append(f"### {result['test']}: {result['status']}")
                for key, value in result.items():
                    if key not in ["test", "status"]:
                        report_lines.append(f"  - {key}: {value}")
        else:
            report_lines.append("✓ No edge case issues found")
        report_lines.append("")
        
        # Data summary
        report_lines.append("## Data Summary")
        report_lines.append(f"- Task ID: {mapped_data.get('id', 'N/A')}")
        report_lines.append(f"- Task Name Length: {len(mapped_data.get('name', ''))}")
        report_lines.append(f"- Number of Test Scenarios: {len(mapped_data.get('testScenarios', []))}")
        report_lines.append(f"- Number of Aggregate Score Categories: {len(mapped_data.get('aggregateScores', []))}")
        
        if "testScenarios" in mapped_data:
            pass_count = sum(1 for s in mapped_data["testScenarios"] if s.get("status") == "pass")
            fail_count = sum(1 for s in mapped_data["testScenarios"] if s.get("status") == "fail")
            report_lines.append(f"- Test Results: {pass_count} pass, {fail_count} fail")
        
        report_lines.append("")
        
        # Specific field analysis
        report_lines.append("## Detailed Field Analysis")
        
        # Test scenarios
        if "testScenarios" in mapped_data:
            report_lines.append("### Test Scenarios:")
            for i, scenario in enumerate(mapped_data["testScenarios"]):
                report_lines.append(f"#### Scenario {i+1} (ID: {scenario.get('id', 'N/A')})")
                report_lines.append(f"  - Description length: {len(scenario.get('description', ''))}")
                report_lines.append(f"  - Status: {scenario.get('status', 'N/A')}")
                report_lines.append(f"  - Output length: {len(scenario.get('output', ''))}")
                report_lines.append(f"  - Number of grader scores: {len(scenario.get('graderScores', []))}")
                
                # Check grader scores
                if "graderScores" in scenario:
                    criteria = [g.get("criterion") for g in scenario["graderScores"]]
                    report_lines.append(f"  - Grader criteria: {', '.join(criteria)}")
        
        report_lines.append("")
        
        # Aggregate scores analysis
        if "aggregateScores" in mapped_data:
            report_lines.append("### Aggregate Scores:")
            categories = {}
            for score in mapped_data["aggregateScores"]:
                cat = score.get("category", "Unknown")
                rate = score.get("passRate", 0)
                if cat in categories:
                    report_lines.append(f"  - WARNING: Duplicate category '{cat}' (rates: {categories[cat]}, {rate})")
                categories[cat] = rate
            
            # Sort by pass rate
            sorted_cats = sorted(categories.items(), key=lambda x: x[1])
            report_lines.append("  Categories by pass rate:")
            for cat, rate in sorted_cats:
                report_lines.append(f"    - {cat}: {rate}%")
        
        return "\n".join(report_lines)


def main():
    verifier = MappingVerifier()
    
    # Generate report
    report = verifier.generate_report(
        "/Users/<USER>/Code3b/Github/pfc/af_2/mapped_task_card.json",
        "/Users/<USER>/Code3b/Github/pfc/af_2/pipeline_output.json"
    )
    
    # Write report to file
    report_path = "/Users/<USER>/Code3b/Github/pfc/af_2/.tmp/mapping_test_report.md"
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"Test report generated: {report_path}")
    
    # Also check combined_output.json for more detailed source
    print("\nGenerating detailed report with combined_output.json...")
    detailed_report = verifier.generate_report(
        "/Users/<USER>/Code3b/Github/pfc/af_2/mapped_task_card.json",
        "/Users/<USER>/Code3b/Github/pfc/af_2/combined_output.json"
    )
    
    detailed_path = "/Users/<USER>/Code3b/Github/pfc/af_2/.tmp/mapping_test_report_detailed.md"
    with open(detailed_path, 'w') as f:
        f.write(detailed_report)
    
    print(f"Detailed test report generated: {detailed_path}")


if __name__ == "__main__":
    main()