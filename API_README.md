# 🚀 Prompt Engineering Ecosystem API

This repository now includes FastAPI wrappers for all three components of the Prompt Engineering Ecosystem, providing REST API access to the AI-powered systems.

## 📋 Services Overview

### 1. **Prompt Generator API** (Port 8001)
- **Purpose**: Generate high-quality prompts using multi-agent orchestration
- **Key Features**: Workflow recommendations, analytics, quality scoring
- **Endpoints**: `/generate-prompt`, `/workflow-recommendation`, `/analytics`

### 2. **Requirements Document Generator API** (Port 8002)
- **Purpose**: Generate comprehensive requirements documents
- **Key Features**: Multi-format output, validation, domain-specific analysis
- **Endpoints**: `/generate-requirements`, `/validate-requirements`

### 3. **Synthetic Data Generator API** (Port 8003)
- **Purpose**: Generate test seeds and synthetic data for validation
- **Key Features**: Category-based generation, quality validation, coverage analysis
- **Endpoints**: `/generate-seeds`, `/validate-data`

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start All Services
```bash
python start_services.py
```

This will start all three services on their respective ports:
- Prompt Generator: http://localhost:8001
- Requirements Generator: http://localhost:8002
- Synthetic Data Generator: http://localhost:8003

### 3. Health Check
```bash
python health_checker.py
```

## 📚 API Documentation

### Prompt Generator API

#### Generate a Prompt
```bash
curl -X POST "http://localhost:8001/generate-prompt" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "Create a professional email template for customer service",
    "target_score": 8.5,
    "max_turns": 12
  }'
```

#### Get Workflow Recommendation
```bash
curl -X POST "http://localhost:8001/workflow-recommendation" \
  -H "Content-Type: application/json" \
  -d '{
    "task": "Financial analysis for investment portfolio",
    "domain": "financial",
    "complexity": "high"
  }'
```

### Requirements Document Generator API

#### Generate Requirements Document
```bash
curl -X POST "http://localhost:8002/generate-requirements" \
  -H "Content-Type: application/json" \
  -d '{
    "initial_prompt": "Create a medical diagnosis assistant for patient triage",
    "max_iterations": 3,
    "output_format": "json"
  }'
```

#### Validate Requirements Document
```bash
curl -X POST "http://localhost:8002/validate-requirements" \
  -H "Content-Type: application/json" \
  -d '{
    "requirements_doc": {
      "problem_statement": "Need for efficient patient triage",
      "core_objectives": ["Improve triage accuracy", "Reduce wait times"],
      "key_requirements": ["HIPAA compliance", "Real-time processing"]
    }
  }'
```

### Synthetic Data Generator API

#### Generate Test Seeds
```bash
curl -X POST "http://localhost:8003/generate-seeds" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt_json": {
      "system_message": "You are a medical diagnosis assistant"
    },
    "requirements_doc": {
      "requirements_doc": {
        "key_requirements": ["Patient data privacy", "Accurate diagnosis"]
      }
    },
    "max_iterations": 1,
    "seed_count_per_category": 10
  }'
```

#### Validate Synthetic Data
```bash
curl -X POST "http://localhost:8003/validate-data" \
  -H "Content-Type: application/json" \
  -d '{
    "seeds": [
      {
        "input": "Patient with chest pain",
        "expected_output": "Immediate cardiac evaluation required",
        "reasoning": "Chest pain is a cardiac emergency symptom",
        "category": "emergency",
        "metadata": {"urgency": "high"}
      }
    ],
    "requirements_doc": {
      "requirements_doc": {
        "key_requirements": ["Emergency response", "Accurate triage"]
      }
    }
  }'
```

## 🔧 Individual Service Management

### Start Individual Services
```bash
# Prompt Generator
python prompt_generator_api.py

# Requirements Generator  
python requirements_doc_api.py

# Synthetic Data Generator
python synthetic_data_api.py
```

### Service URLs and Documentation
- **Prompt Generator**: http://localhost:8001/docs
- **Requirements Generator**: http://localhost:8002/docs  
- **Synthetic Data Generator**: http://localhost:8003/docs

## 📊 Monitoring and Health Checks

### Automated Health Check
The `health_checker.py` script provides comprehensive monitoring:

```bash
python health_checker.py
```

**Features:**
- ✅ Concurrent health checks for all services
- 📊 Response time monitoring
- 🧪 Functional testing of API endpoints
- 📈 Detailed status reports
- 🔧 Troubleshooting recommendations

### Manual Health Checks
```bash
# Check individual services
curl http://localhost:8001/health
curl http://localhost:8002/health  
curl http://localhost:8003/health
```

## 🛠️ Configuration

### Environment Variables
Create a `.env` file with your API keys:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

### Service Configuration
Each service can be configured via their respective config files:
- `prompt_generator/config.yaml`
- `requirements_doc_generator/config.yaml`
- `synthetic_data_generator/config.yaml`

## 🔍 Troubleshooting

### Common Issues

1. **Service Won't Start**
   - Check if ports are already in use
   - Verify API keys are set correctly
   - Check Python dependencies are installed

2. **API Calls Failing**
   - Verify service is running on correct port
   - Check request format matches API specification
   - Review service logs for error details

3. **Health Check Failures**
   - Ensure all services are started
   - Check network connectivity
   - Verify service endpoints are accessible

### Debug Mode
Run services with verbose logging:
```bash
# Add --log-level debug to any service
python prompt_generator_api.py --log-level debug
```

## 📈 Performance Monitoring

### Response Times
Monitor API response times through the health checker:
- Target: < 2 seconds for simple requests
- Target: < 30 seconds for complex generation tasks

### Resource Usage
- Memory: ~500MB per service
- CPU: Varies based on request complexity
- Network: Minimal for health checks, significant for generation

## 🔄 Integration Examples

### Python Client Example
```python
import aiohttp
import asyncio

async def generate_prompt_ecosystem():
    async with aiohttp.ClientSession() as session:
        # 1. Generate requirements
        req_data = {
            "initial_prompt": "Create a financial analysis tool",
            "max_iterations": 2
        }
        async with session.post("http://localhost:8002/generate-requirements", json=req_data) as resp:
            requirements = await resp.json()
        
        # 2. Generate prompt
        prompt_data = {
            "task": "Financial analysis for investment portfolio",
            "target_score": 8.5
        }
        async with session.post("http://localhost:8001/generate-prompt", json=prompt_data) as resp:
            prompt_result = await resp.json()
        
        # 3. Generate test seeds
        seed_data = {
            "prompt_json": prompt_result["prompt"],
            "requirements_doc": requirements,
            "max_iterations": 1
        }
        async with session.post("http://localhost:8003/generate-seeds", json=seed_data) as resp:
            seeds = await resp.json()
        
        return {
            "requirements": requirements,
            "prompt": prompt_result,
            "seeds": seeds
        }

# Run the example
result = asyncio.run(generate_prompt_ecosystem())
print(result)
```

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

async function generatePromptEcosystem() {
    try {
        // 1. Generate requirements
        const requirements = await axios.post('http://localhost:8002/generate-requirements', {
            initial_prompt: 'Create a financial analysis tool',
            max_iterations: 2
        });
        
        // 2. Generate prompt
        const prompt = await axios.post('http://localhost:8001/generate-prompt', {
            task: 'Financial analysis for investment portfolio',
            target_score: 8.5
        });
        
        // 3. Generate test seeds
        const seeds = await axios.post('http://localhost:8003/generate-seeds', {
            prompt_json: prompt.data.prompt,
            requirements_doc: requirements.data,
            max_iterations: 1
        });
        
        return {
            requirements: requirements.data,
            prompt: prompt.data,
            seeds: seeds.data
        };
    } catch (error) {
        console.error('Error:', error.message);
    }
}

generatePromptEcosystem().then(console.log);
```

## 🚀 Production Deployment

### Docker Deployment
```dockerfile
# Example Dockerfile for each service
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8001

CMD ["python", "prompt_generator_api.py"]
```

### Load Balancing
For production, consider using a reverse proxy (nginx) to load balance requests across multiple service instances.

### Environment Variables
Set production environment variables:
```bash
export OPENAI_API_KEY=your_production_key
export LOG_LEVEL=info
export MAX_WORKERS=4
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section above
- Review service logs for error details
- Open an issue on GitHub with detailed error information 